package models

import (
	"fmt"
	"sync"
	"zone/lib/core"
	"zone/lib/db"
	"zone/lib/storage"
	"zone/lib/utils"
)

// type San_LiveRecord struct {
// 	Season   int //! 时间戳，每周三 5：00时间戳
// 	Kill     int //! 击杀
// 	Gift     int //! 礼物
// 	Like     int //! 点赞
// 	Point    int //! 积分
// 	WonTotal int //! 获胜场次
// 	Rank     int //! 最后排名
// }

// type San_LiveMonthData struct {
// 	MonthRank  int   // 周排行
// 	MonthScore int64 // 周积分
// }

// type San_LiveWinStreakData struct {
// 	WinStreak int // 连胜
// }

// type San_LiveScoreData struct {
// 	TotalScore     int64 // 总积分
// 	TotalKillScore int64 // 总击杀
// }

// LivePlayerDB ! 玩家数据结构
type LivePlayerDB struct {
	PlayerId      int64     `db:"playerid"`      //! 直播Id-服务器上的Id
	OpenId        string    `db:"openid"`        //! 平台Id-OpenId
	Icon          string    `db:"icon"`          //! 头像
	PlayerName    string    `db:"playername"`    //! 昵称，需要考虑昵称重复的情况
	LastJoinGame  int64     `db:"lastjoingame"`  //! 上次加入游戏时间
	JoinGameTimes int       `db:"joingametimes"` //! 参与游戏次数（总）
	WinStreak     int       `db:"winstreak"`     // 连胜
	WinStreakRank int       `db:"winstreakrank"` // 连胜排行
	WeekScore     int64     `db:"weekscore"`     // 周积分
	WeekRank      int       `db:"weekrank"`      // 周排行
	MonthScore    int64     `db:"monthscore"`    // 周积分
	MonthRank     int       `db:"monthrank"`     // 周排行
	TotalCall     int64     `db:"totalcall"`     // 总击杀
	GiftRecord    string    `db:"giftrecord"`    // 充值金额
	GiftValue     int64     `db:"giftvalue"`     // 充值金额
	Family        string    `db:"family"`        // 家族
	MapFamily     *sync.Map // 家族Map
	MapGiftRecord *sync.Map // 家族Map

	db.DataUpdate //! 数据库接口
}

// OnGetData 获得数据
func (self *LivePlayerDB) OnGetData(liveId int64) {
	self.PlayerId = liveId

	sql := fmt.Sprintf("select * from %s where id = %d", storage.TABLE_Player, self.PlayerId)
	ret := db.GetDBMgr().DBUser.GetOneData(sql, self, storage.TABLE_Player, self.PlayerId)
	if ret == false {
		self.Encode()
		db.InsertTable(storage.TABLE_Player, self, 0, true)
		self.Init(storage.TABLE_Player, self, true)
	} else {
		self.Decode()
		self.Init(storage.TABLE_Player, self, true)
	}
}

func (self *LivePlayerDB) GetTitle() int32 {
	if self.WinStreakRank == 1 {
		return 1
	} else if self.WinStreakRank >= 2 && self.WinStreakRank <= 10 {
		return 2
	} else if self.WinStreakRank >= 11 && self.WinStreakRank <= 50 {
		return 3
	} else if self.WinStreak > 200 {
		return 4
	} else if self.WinStreak >= 101 && self.WinStreak <= 200 {
		return 5
	} else if self.WinStreak >= 51 && self.WinStreak <= 100 {
		return 6
	} else if self.WinStreak >= 11 && self.WinStreak <= 50 {
		return 7
	} else if self.WinStreak > 0 && self.WinStreak <= 10 {
		return 8
	}
	return 0
}

func (self *LivePlayerDB) SetFamilyScore(name string, score int64) {
	if value, ok := self.MapFamily.LoadOrStore(name, score); ok {
		// 现在可以安全地进行类型断言，因为我们使用了专用的int64序列化方法
		if existingScore, ok := value.(int64); ok {
			score = existingScore + score
			self.MapFamily.Store(name, score)
		} else {
			// 如果类型不匹配，记录错误并重置值
			core.LogError("SetFamilyScore: 期望int64类型", "PlayerId:", self.PlayerId, "Name:", name, "Value:", value, "Type:", fmt.Sprintf("%T", value))
			self.MapFamily.Store(name, score)
		}
	}
}

func (self *LivePlayerDB) AddGiftRecord(name string, count int) {
	if value, ok := self.MapGiftRecord.LoadOrStore(name, count); ok {
		// 现在可以安全地进行类型断言，因为我们使用了专用的int序列化方法
		if existingCount, ok := value.(int); ok {
			count = existingCount + count
			self.MapGiftRecord.Store(name, count)
		} else {
			// 如果类型不匹配，记录错误并重置值
			core.LogError("AddGiftRecord: 期望int类型", "PlayerId:", self.PlayerId, "Name:", name, "Value:", value, "Type:", fmt.Sprintf("%T", value))
			self.MapGiftRecord.Store(name, count)
		}
	}
}

// Decode save
func (self *LivePlayerDB) Decode() {
	// 确保MapFamily已初始化
	if self.MapFamily == nil {
		self.MapFamily = new(sync.Map)
	}

	// 确保MapGiftRecord已初始化
	if self.MapGiftRecord == nil {
		self.MapGiftRecord = new(sync.Map)
	}

	// 使用专用的JSONToSyncMapInt64函数进行反序列化
	if err := utils.JSONToSyncMapInt64(self.Family, self.MapFamily); err != nil {
		core.LogError("家族数据解码失败", "PlayerId:", self.PlayerId, "Family:", self.Family, "Error:", err)
		// 重置为空，避免后续错误
		self.Family = "{}"
	}

	if err := utils.JSONToSyncMapInt(self.GiftRecord, self.MapGiftRecord); err != nil {
		core.LogError("礼物记录解码失败", "PlayerId:", self.PlayerId, "GiftRecord:", self.GiftRecord, "Error:", err)
		// 重置为空，避免后续错误
		self.GiftRecord = "{}"
	}
}

// Encode !
func (self *LivePlayerDB) Encode() {
	// 确保MapFamily已初始化
	if self.MapFamily == nil {
		self.MapFamily = new(sync.Map)
	}

	// 确保MapGiftRecord已初始化
	if self.MapGiftRecord == nil {
		self.MapGiftRecord = new(sync.Map)
	}

	content, err := utils.SyncMapInt64ToJSON(self.MapFamily)
	if err != nil {
		core.LogError("家族数据编码失败", "PlayerId:", self.PlayerId, "Error:", err)
		self.Family = "{}" // 设置为空JSON对象，避免数据库存储问题
		return
	}
	self.Family = content

	content, err = utils.SyncMapIntToJSON(self.MapGiftRecord)
	if err != nil {
		core.LogError("礼物记录编码失败", "PlayerId:", self.PlayerId, "Error:", err)
		self.GiftRecord = "{}" // 设置为空JSON对象，避免数据库存储问题
		return
	}
	self.GiftRecord = content
}

// OnSave ! 储存
func (self *LivePlayerDB) OnSave(sql bool) {
	self.Encode()
	self.Update(sql, false)
}
