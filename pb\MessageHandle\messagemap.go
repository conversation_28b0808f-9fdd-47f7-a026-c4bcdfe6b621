// Code generated by pb_exporter.go. DO NOT EDIT.
package MessageHandle

import (
	"reflect"
	"zone/pb/Message"
)

// 协议号常量定义
const (
	ErrorCode = 101
	HeartBeat = 102
	GmOrder = 103
	Announcement = 104
	AddPlayer = 2001
	PlayerOperate = 2002
	SyncPlayerData = 2003
	KuaiShouAck = 2004
	CreateRoom = 3000
	CreateGame = 3001
	MatchMutiGame = 3002
	EnterMutiGame = 3003
	SyncFrame = 3005
	PlayerKillCount = 3006
	GameStart = 3007
	GameOver = 3008
	GetScoreRank = 3011
	FamilyRank = 3012
	ReloginGame = 3013
	InitGameOver = 3014
	SyncWinStreakPool = 3015
)

var MessageC2STypeMap = map[int32]reflect.Type{
	ErrorCode: reflect.TypeOf(Message.ErrorCodeC2S{}),
	HeartBeat: reflect.TypeOf(Message.HeartBeatC2S{}),
	GmOrder: reflect.TypeOf(Message.GmOrderC2S{}),
	Announcement: reflect.TypeOf(Message.AnnouncementC2S{}),
	AddPlayer: reflect.TypeOf(Message.AddPlayerC2S{}),
	PlayerOperate: reflect.TypeOf(Message.PlayerOperateC2S{}),
	SyncPlayerData: reflect.TypeOf(Message.SyncPlayerDataC2S{}),
	KuaiShouAck: reflect.TypeOf(Message.KuaiShouAckC2S{}),
	CreateRoom: reflect.TypeOf(Message.CreateRoomC2S{}),
	CreateGame: reflect.TypeOf(Message.CreateGameC2S{}),
	MatchMutiGame: reflect.TypeOf(Message.MatchMutiGameC2S{}),
	EnterMutiGame: reflect.TypeOf(Message.EnterMutiGameC2S{}),
	SyncFrame: reflect.TypeOf(Message.SyncFrameC2S{}),
	PlayerKillCount: reflect.TypeOf(Message.PlayerKillCountC2S{}),
	GameStart: reflect.TypeOf(Message.GameStartC2S{}),
	GameOver: reflect.TypeOf(Message.GameOverC2S{}),
	GetScoreRank: reflect.TypeOf(Message.GetScoreRankC2S{}),
	FamilyRank: reflect.TypeOf(Message.FamilyRankC2S{}),
	ReloginGame: reflect.TypeOf(Message.ReloginGameC2S{}),
	InitGameOver: reflect.TypeOf(Message.InitGameOverC2S{}),
	SyncWinStreakPool: reflect.TypeOf(Message.SyncWinStreakPoolC2S{}),
}

var MessageS2CTypeMap = map[int32]reflect.Type{
	ErrorCode: reflect.TypeOf(Message.ErrorCodeS2C{}),
	HeartBeat: reflect.TypeOf(Message.HeartBeatS2C{}),
	GmOrder: reflect.TypeOf(Message.GmOrderS2C{}),
	Announcement: reflect.TypeOf(Message.AnnouncementS2C{}),
	AddPlayer: reflect.TypeOf(Message.AddPlayerS2C{}),
	PlayerOperate: reflect.TypeOf(Message.PlayerOperateS2C{}),
	SyncPlayerData: reflect.TypeOf(Message.SyncPlayerDataS2C{}),
	KuaiShouAck: reflect.TypeOf(Message.KuaiShouAckS2C{}),
	CreateRoom: reflect.TypeOf(Message.CreateRoomS2C{}),
	CreateGame: reflect.TypeOf(Message.CreateGameS2C{}),
	MatchMutiGame: reflect.TypeOf(Message.MatchMutiGameS2C{}),
	EnterMutiGame: reflect.TypeOf(Message.EnterMutiGameS2C{}),
	SyncFrame: reflect.TypeOf(Message.SyncFrameS2C{}),
	PlayerKillCount: reflect.TypeOf(Message.PlayerKillCountS2C{}),
	GameStart: reflect.TypeOf(Message.GameStartS2C{}),
	GameOver: reflect.TypeOf(Message.GameOverS2C{}),
	GetScoreRank: reflect.TypeOf(Message.GetScoreRankS2C{}),
	FamilyRank: reflect.TypeOf(Message.FamilyRankS2C{}),
	ReloginGame: reflect.TypeOf(Message.ReloginGameS2C{}),
	InitGameOver: reflect.TypeOf(Message.InitGameOverS2C{}),
	SyncWinStreakPool: reflect.TypeOf(Message.SyncWinStreakPoolS2C{}),
}

var MessageC2SIDMap = map[reflect.Type]int32{
	reflect.TypeOf(Message.ErrorCodeC2S{}): ErrorCode,
	reflect.TypeOf(Message.HeartBeatC2S{}): HeartBeat,
	reflect.TypeOf(Message.GmOrderC2S{}): GmOrder,
	reflect.TypeOf(Message.AnnouncementC2S{}): Announcement,
	reflect.TypeOf(Message.AddPlayerC2S{}): AddPlayer,
	reflect.TypeOf(Message.PlayerOperateC2S{}): PlayerOperate,
	reflect.TypeOf(Message.SyncPlayerDataC2S{}): SyncPlayerData,
	reflect.TypeOf(Message.KuaiShouAckC2S{}): KuaiShouAck,
	reflect.TypeOf(Message.CreateRoomC2S{}): CreateRoom,
	reflect.TypeOf(Message.CreateGameC2S{}): CreateGame,
	reflect.TypeOf(Message.MatchMutiGameC2S{}): MatchMutiGame,
	reflect.TypeOf(Message.EnterMutiGameC2S{}): EnterMutiGame,
	reflect.TypeOf(Message.SyncFrameC2S{}): SyncFrame,
	reflect.TypeOf(Message.PlayerKillCountC2S{}): PlayerKillCount,
	reflect.TypeOf(Message.GameStartC2S{}): GameStart,
	reflect.TypeOf(Message.GameOverC2S{}): GameOver,
	reflect.TypeOf(Message.GetScoreRankC2S{}): GetScoreRank,
	reflect.TypeOf(Message.FamilyRankC2S{}): FamilyRank,
	reflect.TypeOf(Message.ReloginGameC2S{}): ReloginGame,
	reflect.TypeOf(Message.InitGameOverC2S{}): InitGameOver,
	reflect.TypeOf(Message.SyncWinStreakPoolC2S{}): SyncWinStreakPool,
}

var MessageS2CIDMap = map[reflect.Type]int32{
	reflect.TypeOf(Message.ErrorCodeS2C{}): ErrorCode,
	reflect.TypeOf(Message.HeartBeatS2C{}): HeartBeat,
	reflect.TypeOf(Message.GmOrderS2C{}): GmOrder,
	reflect.TypeOf(Message.AnnouncementS2C{}): Announcement,
	reflect.TypeOf(Message.AddPlayerS2C{}): AddPlayer,
	reflect.TypeOf(Message.PlayerOperateS2C{}): PlayerOperate,
	reflect.TypeOf(Message.SyncPlayerDataS2C{}): SyncPlayerData,
	reflect.TypeOf(Message.KuaiShouAckS2C{}): KuaiShouAck,
	reflect.TypeOf(Message.CreateRoomS2C{}): CreateRoom,
	reflect.TypeOf(Message.CreateGameS2C{}): CreateGame,
	reflect.TypeOf(Message.MatchMutiGameS2C{}): MatchMutiGame,
	reflect.TypeOf(Message.EnterMutiGameS2C{}): EnterMutiGame,
	reflect.TypeOf(Message.SyncFrameS2C{}): SyncFrame,
	reflect.TypeOf(Message.PlayerKillCountS2C{}): PlayerKillCount,
	reflect.TypeOf(Message.GameStartS2C{}): GameStart,
	reflect.TypeOf(Message.GameOverS2C{}): GameOver,
	reflect.TypeOf(Message.GetScoreRankS2C{}): GetScoreRank,
	reflect.TypeOf(Message.FamilyRankS2C{}): FamilyRank,
	reflect.TypeOf(Message.ReloginGameS2C{}): ReloginGame,
	reflect.TypeOf(Message.InitGameOverS2C{}): InitGameOver,
	reflect.TypeOf(Message.SyncWinStreakPoolS2C{}): SyncWinStreakPool,
}
