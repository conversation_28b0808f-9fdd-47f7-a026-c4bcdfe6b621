// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.5.1
// source: protocols.proto

package Message

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 协议号定义
type ProtocolManager int32

const (
	ProtocolManager_NONE              ProtocolManager = 0
	ProtocolManager_ErrorCode         ProtocolManager = 101
	ProtocolManager_HeartBeat         ProtocolManager = 102  // 心跳
	ProtocolManager_GmOrder           ProtocolManager = 103  // GM指令
	ProtocolManager_Announcement      ProtocolManager = 104  // 公告
	ProtocolManager_AddPlayer         ProtocolManager = 2001 // 落座
	ProtocolManager_PlayerOperate     ProtocolManager = 2002 // 玩家操作
	ProtocolManager_SyncPlayerData    ProtocolManager = 2003 // 同步玩家信息
	ProtocolManager_KuaiShouAck       ProtocolManager = 2004 // 快手Ack
	ProtocolManager_CreateRoom        ProtocolManager = 3000
	ProtocolManager_CreateGame        ProtocolManager = 3001 //  创建游戏
	ProtocolManager_MatchMutiGame     ProtocolManager = 3002 //  进入匹配池
	ProtocolManager_EnterMutiGame     ProtocolManager = 3003 // 进入多人游戏
	ProtocolManager_SyncFrame         ProtocolManager = 3005 // 同步帧
	ProtocolManager_PlayerKillCount   ProtocolManager = 3006 // 玩家杀敌
	ProtocolManager_GameStart         ProtocolManager = 3007 // 开始游戏
	ProtocolManager_GameOver          ProtocolManager = 3008 // 游戏结束
	ProtocolManager_GetScoreRank      ProtocolManager = 3011 // 获取排行
	ProtocolManager_FamilyRank        ProtocolManager = 3012 // 家族排行
	ProtocolManager_ReloginGame       ProtocolManager = 3013 // 创建游戏
	ProtocolManager_InitGameOver      ProtocolManager = 3014 // 初始化游戏结束
	ProtocolManager_SyncWinStreakPool ProtocolManager = 3015 // 同步连胜池
)

// Enum value maps for ProtocolManager.
var (
	ProtocolManager_name = map[int32]string{
		0:    "NONE",
		101:  "ErrorCode",
		102:  "HeartBeat",
		103:  "GmOrder",
		104:  "Announcement",
		2001: "AddPlayer",
		2002: "PlayerOperate",
		2003: "SyncPlayerData",
		2004: "KuaiShouAck",
		3000: "CreateRoom",
		3001: "CreateGame",
		3002: "MatchMutiGame",
		3003: "EnterMutiGame",
		3005: "SyncFrame",
		3006: "PlayerKillCount",
		3007: "GameStart",
		3008: "GameOver",
		3011: "GetScoreRank",
		3012: "FamilyRank",
		3013: "ReloginGame",
		3014: "InitGameOver",
		3015: "SyncWinStreakPool",
	}
	ProtocolManager_value = map[string]int32{
		"NONE":              0,
		"ErrorCode":         101,
		"HeartBeat":         102,
		"GmOrder":           103,
		"Announcement":      104,
		"AddPlayer":         2001,
		"PlayerOperate":     2002,
		"SyncPlayerData":    2003,
		"KuaiShouAck":       2004,
		"CreateRoom":        3000,
		"CreateGame":        3001,
		"MatchMutiGame":     3002,
		"EnterMutiGame":     3003,
		"SyncFrame":         3005,
		"PlayerKillCount":   3006,
		"GameStart":         3007,
		"GameOver":          3008,
		"GetScoreRank":      3011,
		"FamilyRank":        3012,
		"ReloginGame":       3013,
		"InitGameOver":      3014,
		"SyncWinStreakPool": 3015,
	}
)

func (x ProtocolManager) Enum() *ProtocolManager {
	p := new(ProtocolManager)
	*p = x
	return p
}

func (x ProtocolManager) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ProtocolManager) Descriptor() protoreflect.EnumDescriptor {
	return file_protocols_proto_enumTypes[0].Descriptor()
}

func (ProtocolManager) Type() protoreflect.EnumType {
	return &file_protocols_proto_enumTypes[0]
}

func (x ProtocolManager) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ProtocolManager.Descriptor instead.
func (ProtocolManager) EnumDescriptor() ([]byte, []int) {
	return file_protocols_proto_rawDescGZIP(), []int{0}
}

var File_protocols_proto protoreflect.FileDescriptor

const file_protocols_proto_rawDesc = "" +
	"\n" +
	"\x0fprotocols.proto\x12\n" +
	"PB.Message*\x93\x03\n" +
	"\x0fProtocolManager\x12\b\n" +
	"\x04NONE\x10\x00\x12\r\n" +
	"\tErrorCode\x10e\x12\r\n" +
	"\tHeartBeat\x10f\x12\v\n" +
	"\aGmOrder\x10g\x12\x10\n" +
	"\fAnnouncement\x10h\x12\x0e\n" +
	"\tAddPlayer\x10\xd1\x0f\x12\x12\n" +
	"\rPlayerOperate\x10\xd2\x0f\x12\x13\n" +
	"\x0eSyncPlayerData\x10\xd3\x0f\x12\x10\n" +
	"\vKuaiShouAck\x10\xd4\x0f\x12\x0f\n" +
	"\n" +
	"CreateRoom\x10\xb8\x17\x12\x0f\n" +
	"\n" +
	"CreateGame\x10\xb9\x17\x12\x12\n" +
	"\rMatchMutiGame\x10\xba\x17\x12\x12\n" +
	"\rEnterMutiGame\x10\xbb\x17\x12\x0e\n" +
	"\tSyncFrame\x10\xbd\x17\x12\x14\n" +
	"\x0fPlayerKillCount\x10\xbe\x17\x12\x0e\n" +
	"\tGameStart\x10\xbf\x17\x12\r\n" +
	"\bGameOver\x10\xc0\x17\x12\x11\n" +
	"\fGetScoreRank\x10\xc3\x17\x12\x0f\n" +
	"\n" +
	"FamilyRank\x10\xc4\x17\x12\x10\n" +
	"\vReloginGame\x10\xc5\x17\x12\x11\n" +
	"\fInitGameOver\x10\xc6\x17\x12\x16\n" +
	"\x11SyncWinStreakPool\x10\xc7\x17b\x06proto3"

var (
	file_protocols_proto_rawDescOnce sync.Once
	file_protocols_proto_rawDescData []byte
)

func file_protocols_proto_rawDescGZIP() []byte {
	file_protocols_proto_rawDescOnce.Do(func() {
		file_protocols_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_protocols_proto_rawDesc), len(file_protocols_proto_rawDesc)))
	})
	return file_protocols_proto_rawDescData
}

var file_protocols_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_protocols_proto_goTypes = []any{
	(ProtocolManager)(0), // 0: PB.Message.ProtocolManager
}
var file_protocols_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_protocols_proto_init() }
func file_protocols_proto_init() {
	if File_protocols_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_protocols_proto_rawDesc), len(file_protocols_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_protocols_proto_goTypes,
		DependencyIndexes: file_protocols_proto_depIdxs,
		EnumInfos:         file_protocols_proto_enumTypes,
	}.Build()
	File_protocols_proto = out.File
	file_protocols_proto_goTypes = nil
	file_protocols_proto_depIdxs = nil
}
