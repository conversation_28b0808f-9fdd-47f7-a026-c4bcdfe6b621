package configs

import (
	"fmt"
	"math/rand"
	"sync"
	"time"
	"zone/lib/config"
	"zone/lib/core"
	"zone/lib/network"
	"zone/pb/Data"
)

type BlindBox struct {
	item int
	prob float32
}

// 主播ID为自定义ID，从1开始一直加
type GiftConfig struct {
	MapGift      *sync.Map
	MapGiftById  *sync.Map
	MapBlindBoxs *sync.Map
	GiftIds      []string
}

var s_GiftConfig *GiftConfig = nil

func GetGiftConfig() *GiftConfig {
	if s_GiftConfig == nil {
		s_GiftConfig = new(GiftConfig)
	}

	return s_GiftConfig
}

func (giftMgr *GiftConfig) Init() {
	// 使用新的数据加载器加载GiftConfig
	var configs []*Data.Giftconfig
	err := config.GetDataLoader().LoadData("giftconfig", &configs)
	if err != nil {
		core.LogError("加载GiftConfig失败:", err)
		return
	}

	giftMgr.MapGift = new(sync.Map)
	giftMgr.MapGiftById = new(sync.Map)
	index := 0
	for _, config := range configs {
		if len(config.GiftId) > network.Platform {
			giftMgr.MapGift.Store(config.GiftId[network.Platform], config)
			index++
		}
		giftMgr.MapGiftById.Store(int(config.Id), config)
	}

	giftMgr.GiftIds = make([]string, index)
	index = 0
	for _, config := range configs {
		if len(config.GiftId) > network.Platform {
			giftMgr.GiftIds[index] = config.GiftId[network.Platform]
			index++
		}
	}

	core.LogInfo(fmt.Sprintf("加载GiftConfig成功，共%d条记录", len(configs)))

	var blindboxConfigs []*Data.Blindbox
	config.GetDataLoader().LoadData("blindbox", &blindboxConfigs)
	giftMgr.MapBlindBoxs = new(sync.Map)
	for _, config := range blindboxConfigs {

		BlindBoxs := make([]BlindBox, 0)
		for i, item := range config.Item {
			BlindBoxs = append(BlindBoxs, BlindBox{item: int(item), prob: config.Probability[i]})
		}
		giftMgr.MapBlindBoxs.Store(config.Id, BlindBoxs)
	}
}

func (giftMgr *GiftConfig) GetGiftParameter(giftId int, giftNum int) int32 {
	return int32(giftNum<<8 | giftId)
}

func (giftMgr *GiftConfig) GetGiftParameterByString(giftId string, giftNum int) int32 {
	config := giftMgr.GetGiftConfig(giftId)
	if config != nil {
		return int32(giftNum<<8 | int(config.Id))
	}
	return 0
}

func (giftMgr *GiftConfig) GetGiftConfig(giftId string) *Data.Giftconfig {
	if config, ok := giftMgr.MapGift.Load(giftId); ok {
		return config.(*Data.Giftconfig)
	}
	return nil
}

func (giftMgr *GiftConfig) GetGiftConfigById(giftId int) *Data.Giftconfig {
	if config, ok := giftMgr.MapGiftById.Load(giftId); ok {
		return config.(*Data.Giftconfig)
	}
	return nil
}

func (giftMgr *GiftConfig) GetGiftIds() []string {
	return giftMgr.GiftIds
}

func (giftMgr *GiftConfig) GetGiftName(giftId string) string {
	config := giftMgr.GetGiftConfig(giftId)
	if config != nil {
		return config.Name[network.Platform]
	}
	return ""
}

// 通过盲盒ID 随机出盲盒带来的礼物ID
func (giftMgr *GiftConfig) GetBlindBox(blindBoxId int32, count int) map[int]int {
	if blindBoxId == 0 {
		return nil
	}
	var blindBoxs []BlindBox
	if value, ok := giftMgr.MapBlindBoxs.Load(blindBoxId); ok {
		blindBoxs = value.([]BlindBox)
	} else {
		return nil
	}

	rand.Seed(time.Now().UnixNano())
	itemmap := make(map[int]int)
	for i := 0; i < count; i++ {
		itemid := 0
		r := rand.Float32()
		accumulator := float32(0)
		for _, item := range blindBoxs {
			accumulator += item.prob
			if r <= accumulator {
				itemid = item.item
				break
			}
		}
		itemmap[itemid]++
	}
	return itemmap
}
