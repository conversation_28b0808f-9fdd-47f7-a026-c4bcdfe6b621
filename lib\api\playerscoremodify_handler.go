package api

import (
	"net/http"
	"strconv"
	"zone/game/mods"

	"github.com/gin-gonic/gin"
)

type ScoreModifyHandler struct{}

// NewAnnouncementHandler 创建公告处理器
func NewScoreModifyHandler() *ScoreModifyHandler {
	return &ScoreModifyHandler{}
}

type AddScoreRequest struct {
	Score    int    `json:"score" binding:"required"`
	Reason   string `json:"reason" binding:"required"`
	Operator string `json:"operator" binding:"required"`
}

func (h *ScoreModifyHandler) AddScore(c *gin.Context) {
	idStr := c.Param("id")
	playerid, err := strconv.Atoi(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, APIResponse{
			Code:    400,
			Message: "无效的玩家ID",
		})
		return
	}

	var req AddScoreRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.J<PERSON>N(http.StatusBadRequest, APIResponse{
			Code:    400,
			Message: "请求参数错误: " + err.Error(),
		})
		return
	}

	// 在这里实现添加分数的逻辑
	liveID := int64(playerid)

	livePlayerDB := mods.GetLiveMgr().LoadLivePlayerDB(liveID)
	if livePlayerDB == nil {
		c.JSON(http.StatusBadRequest, APIResponse{
			Code:    400,
			Message: "玩家不存在",
		})
		return
	}

	// 添加分数
	score := int64(req.Score)
	livePlayerDB.WeekScore += score
	livePlayerDB.MonthScore += score
	livePlayerDB.OnSave(true)
	mods.GetLiveTopMgr().AddScore(liveID, score)
	mods.GetLiveTopMgr().RankSort()

	c.JSON(http.StatusOK, APIResponse{
		Code:    0,
		Message: "分数添加成功",
	})
}
