// JoysGames copyrights this specification. No part of this specification may be
// reproduced in any form or means, without the prior written consent of JoysGames.
//
// This specification is preliminary and is subject to change at any time without notice.
// JoysGames assumes no responsibility for any errors contained herein.
//
// The above copyright notice and this permission notice shall be included
// in all copies or substantial portions of the Software.
// @package JGServer
// @copyright joysgames.cn All rights reserved.
// @version v1.0

package core

import (
	"fmt"
	"log"
	"reflect"
	"sync"
	"sync/atomic"
)

// 委托(多线程安全)
type Delegate struct {
	sync.Mutex                         // 互斥锁
	Invokers   map[int][]reflect.Value // 事件通知列表
	LockCount  int32                   // 锁次数，防止循环加锁
}

// 初始化
func (that *Delegate) init() {
	that.Invokers = make(map[int][]reflect.Value)
}

// 加锁通知列表
// 为防止嵌套锁，加入原子锁计次
func (that *Delegate) lockInvoker() {
	if atomic.AddInt32(&that.LockCount, 1) == 1 {
		that.Lock()
	}
}

// 解锁通知列表
// 为防止嵌套锁，加入原子锁计次
func (that *Delegate) unlockInvoker() {
	if atomic.AddInt32(&that.LockCount, -1) == 0 {
		that.Unlock()
	}
}

// 增加监听
func (that *Delegate) AddListener(event int, cb interface{}) error {
	if reflect.TypeOf(cb).Kind() != reflect.Func {
		return fmt.Errorf("%s is not of type reflect.Func", reflect.TypeOf(cb).Kind())
	}

	that.lockInvoker()
	defer that.unlockInvoker()
	if that.Invokers == nil {
		that.init()
	}
	funcArr, ok := that.Invokers[event]
	if !ok {
		funcArr = make([]reflect.Value, 0)
		that.Invokers[event] = funcArr
	}
	handler := reflect.ValueOf(cb)
	index := FindReflectSlice(funcArr, func(value reflect.Value, key int) bool {
		return value == handler
	})
	if index == -1 {
		that.Invokers[event] = append(funcArr, handler)
	}
	return nil
}

// 移除监听
func (that *Delegate) RemoveListener(event int, cb interface{}) error {
	if !(reflect.TypeOf(cb).Kind() == reflect.Func) {
		return fmt.Errorf("%s is not of type reflect.Func", reflect.TypeOf(cb).Kind())
	}
	that.lockInvoker()
	defer that.unlockInvoker()
	if that.Invokers == nil {
		return nil
	}
	funcArr, ok := that.Invokers[event]
	if !ok {
		return nil
	}
	handler := reflect.ValueOf(cb)
	that.Invokers[event] = RemoveReflectSliceByVal(funcArr, handler)
	return nil
}

// 移除所有监听
func (that *Delegate) RemoveAllListeners() {
	that.lockInvoker()
	defer that.unlockInvoker()
	if that.Invokers == nil {
		return
	}
	for key := range that.Invokers {
		delete(that.Invokers, key)
	}
}

// 事件通知
func (that *Delegate) Notify(event int, args ...interface{}) {
	that.lockInvoker()
	defer that.unlockInvoker()
	if that.Invokers == nil {
		return
	}
	funcArr, ok := that.Invokers[event]
	if !ok || len(funcArr) < 1 {
		return
	}
	defer func() {
		if err := recover(); err != nil {
			log.Printf("notify event %d raise %v", event, err)
		}
	}()
	funcType := funcArr[0].Type()
	options := make([]reflect.Value, len(args))
	for i, v := range args {
		if v == nil {
			options[i] = reflect.New(funcType.In(i)).Elem()
		} else {
			options[i] = reflect.ValueOf(v)
		}
	}
	for _, item := range funcArr {
		item.Call(options)
	}
}
