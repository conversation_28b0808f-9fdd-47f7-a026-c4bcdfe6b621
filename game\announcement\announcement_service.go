package announcement

import (
	"fmt"
	"time"
	"zone/game/Request"
	"zone/game/models"
	"zone/game/mods"
	"zone/lib/core"
	"zone/lib/db"
	"zone/lib/network"
	"zone/lib/storage"
)

// AnnouncementService 公告服务
type AnnouncementService struct {
	isRunning      bool
	stopChan       chan bool
	tickerInterval time.Duration

	// 去重管理器（支持Redis和内存两种模式）
	// deduplicationMgr       *DeduplicationManager       // Redis去重管理器（保留兼容性）
	memoryDeduplicationMgr *MemoryDeduplicationManager // 内存去重管理器（新增）
	useMemoryDeduplication bool                        // 是否使用内存去重

	// 清理管理器
	mysqlCleanupMgr *MySQLCleanupManager // MySQL清理管理器（主要使用）

	// timeZoneHelper *TimeZoneHelper
}

// TimeZoneHelper 时区处理辅助工具
type TimeZoneHelper struct {
	serverLocation *time.Location
	useServerTime  bool
}

// NewTimeZoneHelper 创建时区处理辅助工具
func NewTimeZoneHelper() *TimeZoneHelper {
	// 默认使用本地时区，但支持配置
	serverLocation := time.Local

	// 检查是否有时区配置
	if core.GetZoneApp() != nil {
		// 使用服务器配置的时区偏移
		timeArea := core.GetZoneApp().GetTimeArea()
		if timeArea != 0 {
			// 创建固定偏移时区
			serverLocation = time.FixedZone("SERVER", timeArea*3600)
		}
	}

	return &TimeZoneHelper{
		serverLocation: serverLocation,
		useServerTime:  true, // 默认使用服务器时间
	}
}

// SetServerLocation 设置服务器时区
func (h *TimeZoneHelper) SetServerLocation(loc *time.Location) {
	h.serverLocation = loc
}

// SetUseServerTime 设置是否使用服务器时间偏移
func (h *TimeZoneHelper) SetUseServerTime(use bool) {
	h.useServerTime = use
}

// NormalizeTime 标准化时间到服务器时区
func (h *TimeZoneHelper) NormalizeTime(t time.Time) time.Time {
	// 转换到服务器配置的时区
	return t.In(h.serverLocation)
}

// GetServerTime 获取服务器当前时间（时区安全）
func (h *TimeZoneHelper) GetServerTime() time.Time {
	var serverTime time.Time

	if h.useServerTime {
		// 使用系统的服务器时间（包含偏移）
		serverTime = core.TimeServer()
	} else {
		// 使用标准时间
		serverTime = time.Now()
	}

	// 转换到服务器配置的时区
	return h.NormalizeTime(serverTime)
}

// NewAnnouncementService 创建公告服务实例
func NewAnnouncementService() *AnnouncementService {
	return &AnnouncementService{
		isRunning:      false,
		stopChan:       make(chan bool, 1),
		tickerInterval: 30 * time.Second, // 每30秒检查一次

		// 去重管理器（默认使用内存去重）
		// deduplicationMgr:       NewDeduplicationManager(),       // Redis去重（保留兼容性）
		memoryDeduplicationMgr: NewMemoryDeduplicationManager(), // 内存去重（默认使用）
		// useMemoryDeduplication: true,                            // 默认使用内存去重

		// 清理管理器
		mysqlCleanupMgr: NewMySQLCleanupManager(), // MySQL清理（主要使用）

		// timeZoneHelper: NewTimeZoneHelper(),
	}
}

// Start 启动公告服务
func (s *AnnouncementService) Start() {
	if s.isRunning {
		core.LogInfo("公告服务已经在运行中")
		return
	}

	s.isRunning = true

	s.memoryDeduplicationMgr.Start()
	// 启动去重管理器
	// if s.useMemoryDeduplication {
	// 	core.LogInfo("启用内存去重管理器")
	// } else {
	// 	core.LogInfo("启用Redis去重管理器")
	// }

	// 启动MySQL清理管理器
	s.mysqlCleanupMgr.Start()

	core.LogInfo("启动公告服务，检查间隔:", s.tickerInterval)

	go s.run()
}

// Stop 停止公告服务
func (s *AnnouncementService) Stop() {
	if !s.isRunning {
		return
	}

	core.LogInfo("停止公告服务")
	s.isRunning = false

	// 停止去重管理器
	s.memoryDeduplicationMgr.Stop()
	// if s.useMemoryDeduplication {

	// }

	// 停止MySQL清理管理器
	s.mysqlCleanupMgr.Stop()

	s.stopChan <- true
}

// run 运行公告服务主循环
func (s *AnnouncementService) run() {
	ticker := time.NewTicker(s.tickerInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			s.processAnnouncements()
		case <-s.stopChan:
			core.LogInfo("公告服务已停止")
			return
		}
	}
}

// processAnnouncements 处理公告推送和清理
func (s *AnnouncementService) processAnnouncements() {
	// 1. 推送新的有效公告
	s.pushActiveAnnouncements()

	// 2. MySQL清理由独立的清理管理器处理，这里不需要手动调用

	// // 3. 清理过期的去重记录
	// if s.useMemoryDeduplication {
	// 	// 内存去重管理器有自己的清理循环，这里不需要手动清理
	// 	// core.LogDebug("内存去重管理器自动清理中")
	// } else {
	// 	// Redis去重需要手动清理
	// 	s.deduplicationMgr.CleanupExpiredRecords()
	// }
}

// pushActiveAnnouncements 推送当前有效的公告
func (s *AnnouncementService) pushActiveAnnouncements() {

	now := core.TimeServer().Unix()
	query := fmt.Sprintf(`
		SELECT * FROM %s
		WHERE status = 2
		AND start_time <= %d
		AND end_time >= %d
		ORDER BY priority DESC, created_time ASC`,
		storage.TABLE_Announcement, now, now)

	var announcementTemplate models.AnnouncementDB
	results := db.GetDBMgr().DBUser.GetAllData(query, &announcementTemplate)

	if len(results) == 0 {
		return
	}

	// 处理每个公告
	for _, result := range results {
		announcement := result.(*models.AnnouncementDB)
		s.pushAnnouncementToSessions(announcement)
	}
}

// pushAnnouncementToSessions 将公告推送给符合条件的会话
func (s *AnnouncementService) pushAnnouncementToSessions(announcement *models.AnnouncementDB) {
	// 获取所有在线会话
	sessions := s.getOnlineSessions()

	for _, session := range sessions {
		// 检查会话是否符合推送条件
		if s.shouldPushToSession(session, announcement) {
			s.sendAnnouncementToSession(session, announcement)
		}
	}
}

// shouldPushToSession 检查是否应该向会话推送公告
func (s *AnnouncementService) shouldPushToSession(session *network.Session, announcement *models.AnnouncementDB) bool {
	// 1. 检查会话基本状态
	if session == nil {
		core.LogDebug("会话为空，跳过推送")
		return false
	}

	if session.ShutDown {
		core.LogDebug("会话已关闭，跳过推送", "sessionID:", session.ID)
		return false
	}

	// 2. 检查房间状态过滤
	if !s.isRoomStateValid(session.RoomID) {
		core.LogDebug("房间状态无效，跳过推送", "sessionID:", session.ID, "roomID:", session.RoomID)
		return false
	}

	// 3. 检查公告基本有效性
	if announcement == nil {
		core.LogDebug("公告为空，跳过推送", "sessionID:", session.ID)
		return false
	}

	if announcement.Status != 2 {
		core.LogDebug("公告状态无效，跳过推送", "sessionID:", session.ID, "announcementID:", announcement.Id, "状态:", announcement.Status)
		return false
	}

	// 4. 检查公告时间有效性（时区安全）
	serverTime := core.TimeServer().Unix()

	if serverTime < announcement.StartTime {
		core.LogDebug("公告尚未开始，跳过推送", "sessionID:", session.ID, "announcementID:", announcement.Id)
		return false
	}
	if serverTime > announcement.EndTime {
		core.LogDebug("公告已过期，跳过推送", "sessionID:", session.ID, "announcementID:", announcement.Id)
		return false
	}

	// 5. 检查去重机制（包含Redis异常处理）
	if s.isAnnouncementAlreadySentWithFallback(int(session.ID), announcement.Id) {
		// core.LogDebug("公告已发送过，跳过推送", "sessionID:", session.ID, "announcementID:", announcement.Id)
		return false
	}
	return true
}

// isRoomStateValid 检查房间状态是否有效
func (s *AnnouncementService) isRoomStateValid(roomID int32) bool {
	// 获取房间状态
	roomState := s.getRoomState(roomID)

	// 只有当房间状态 >= PLAY_STATE_READY 时才推送公告
	// PLAY_STATE_READY = 1 (根据现有代码分析)
	return roomState >= 1
}

// isAnnouncementAlreadySentWithFallback 检查公告是否已经发送过（支持内存和Redis两种模式）
func (s *AnnouncementService) isAnnouncementAlreadySentWithFallback(sessionID, announcementID int) bool {
	// core.LogDebug("开始检查公告去重", "sessionID:", sessionID, "announcementID:", announcementID, "使用内存去重:", s.useMemoryDeduplication)

	alreadySent := s.memoryDeduplicationMgr.IsAlreadySent(sessionID, announcementID)
	// core.LogDebug("内存去重检查结果", "sessionID:", sessionID, "announcementID:", announcementID, "已发送:", alreadySent)
	return alreadySent
}

// sendAnnouncementToSession 向会话发送公告
func (s *AnnouncementService) sendAnnouncementToSession(session *network.Session, announcement *models.AnnouncementDB) {
	// core.LogInfo("开始发送公告", "sessionID:", session.ID, "announcementID:", announcement.Id, "内容:", announcement.Content)

	// 创建公告消息
	Request.AnnouncementRequest(session, announcement.Content)

	// 记录去重键，使用公告结束时间设置合理的TTL
	// core.LogDebug("准备标记公告为已发送", "sessionID:", session.ID, "announcementID:", announcement.Id, "结束时间:", announcement.EndTime)
	s.markAnnouncementAsSentWithEndTime(int(session.ID), announcement.Id, announcement.EndTime)

	// core.LogInfo("公告发送完成", "sessionID:", session.ID, "announcementID:", announcement.Id)
}

// markAnnouncementAsSentWithEndTime 根据公告结束时间标记为已发送
func (s *AnnouncementService) markAnnouncementAsSentWithEndTime(sessionID, announcementID int, endTime int64) {
	s.memoryDeduplicationMgr.MarkAsSentWithEndTime(sessionID, announcementID, endTime)
	// if s.useMemoryDeduplication {
	// 	// 使用内存去重管理器
	// 	// core.LogDebug("内存去重标记完成", "sessionID:", sessionID, "announcementID:", announcementID)
	// } else {
	// 	// 使用Redis去重管理器
	// 	err := s.deduplicationMgr.MarkAsSentWithEndTime(sessionID, announcementID, endTime)
	// 	if err != nil {
	// 		core.LogError("Redis标记公告为已发送失败:", err)
	// 	}
	// }
}

// 注意：cleanupExpiredAnnouncements 方法已删除
// MySQL清理由独立的MySQLCleanupManager处理

// getOnlineSessions 获取所有在线会话信息
func (s *AnnouncementService) getOnlineSessions() []*network.Session {
	// 使用现有的会话管理系统获取所有在线会话
	sessionMgr := network.GetSessionMgr()
	sessions := make([]*network.Session, 0)

	sessionMgr.Lock.RLock()
	defer sessionMgr.Lock.RUnlock()

	for _, session := range sessionMgr.MapSession {
		if session != nil && !session.ShutDown && session.RoomID > 0 {
			sessions = append(sessions, session)
		}
	}

	return sessions
}

// getRoomState 获取房间状态
func (s *AnnouncementService) getRoomState(roomID int32) int {
	// 使用现有的房间管理系统获取房间状态
	roomMgr := mods.GetRoomMgr()
	room := roomMgr.GetRoomById(roomID)

	if room == nil {
		return 0 // 房间不存在
	}

	return room.GetPlayState() // 房间未准备
}

// isSessionValid 检查会话是否有效
func (s *AnnouncementService) isSessionValid(session *network.Session) bool {
	if session == nil {
		return false
	}

	if session.ShutDown {
		return false
	}

	if session.RoomID <= 0 {
		return false
	}

	return true
}

// 以下是公共方法，用于测试

// ShouldPushToSession 公共方法：检查是否应该向会话推送公告（用于测试）
func (s *AnnouncementService) ShouldPushToSession(session *network.Session, announcement *models.AnnouncementDB) bool {
	return s.shouldPushToSession(session, announcement)
}

// MarkAnnouncementAsSentWithEndTime 公共方法：根据公告结束时间标记为已发送（用于测试）
func (s *AnnouncementService) MarkAnnouncementAsSentWithEndTime(sessionID, announcementID int, endTime int64) {
	s.markAnnouncementAsSentWithEndTime(sessionID, announcementID, endTime)
}

// GetDeduplicationStats 获取去重统计信息（用于测试和监控）
// func (s *AnnouncementService) GetDeduplicationStats() map[string]interface{} {
// 	return s.deduplicationMgr.GetDeduplicationStats()
// }

// IsRedisAvailable 检查Redis是否可用（用于测试和监控）
// func (s *AnnouncementService) IsRedisAvailable() bool {
// 	return s.deduplicationMgr.IsRedisAvailable()
// }

// // GetDeduplicationManager 获取去重管理器（用于测试）
// func (s *AnnouncementService) GetDeduplicationManager() *DeduplicationManager {
// 	return s.deduplicationMgr
// }

// 注意：GetCleanupManager 方法已删除
// 请使用 GetMySQLCleanupManager() 获取MySQL清理管理器

// GetMemoryDeduplicationManager 获取内存去重管理器（用于测试）
func (s *AnnouncementService) GetMemoryDeduplicationManager() *MemoryDeduplicationManager {
	return s.memoryDeduplicationMgr
}

// GetMySQLCleanupManager 获取MySQL清理管理器（用于测试）
func (s *AnnouncementService) GetMySQLCleanupManager() *MySQLCleanupManager {
	return s.mysqlCleanupMgr
}

// SetUseMemoryDeduplication 设置是否使用内存去重（用于测试和配置）
// func (s *AnnouncementService) SetUseMemoryDeduplication(useMemory bool) {
// 	s.useMemoryDeduplication = useMemory
// 	core.LogInfo("切换去重模式", "使用内存去重:", useMemory)
// }

// // GetDeduplicationMode 获取当前去重模式（用于测试和监控）
// func (s *AnnouncementService) GetDeduplicationMode() string {
// 	if s.useMemoryDeduplication {
// 		return "memory"
// 	}
// 	return "redis"
// }

// GetAllStats 获取所有统计信息（用于监控）
func (s *AnnouncementService) GetAllStats() map[string]interface{} {
	stats := make(map[string]interface{})

	// 基本信息
	stats["is_running"] = s.isRunning
	stats["deduplication_mode"] = "memory"
	stats["ticker_interval"] = s.tickerInterval.String()

	stats["memory_deduplication"] = s.memoryDeduplicationMgr.GetStats()
	// 去重统计
	// if s.useMemoryDeduplication {
	// } else {
	// 	stats["redis_deduplication"] = s.deduplicationMgr.GetDeduplicationStats()
	// }

	// 清理统计
	stats["mysql_cleanup"] = s.mysqlCleanupMgr.GetStats()

	return stats
}
