package mods

import (
	"errors"
	"fmt"
	"sync"
	"sync/atomic"
	"zone/game/Request"
	"zone/lib/core"
	"zone/lib/payload"
	"zone/lib/utils"
	"zone/pb/Message"
)

// LiveMultiGame 多房间游戏管理器
// 支持多个房间连接到同一个游戏实例
type LiveMultiGame struct {
	// 基础游戏属性（继承自LiveGame的设计）
	PlayState     int    // 游戏状态
	ScorePool     int64  // 积分池
	WinStreakPool int32  // 连胜池
	IsOver        bool   // 游戏是否结束
	StartTime     int64  // 开始时间
	EndTime       int64  // 结束时间
	RoundID       string // 回合ID
	LogicFrame    int32  // 逻辑帧

	// 多房间管理
	GameID         int32               // 多人游戏唯一标识
	ConnectedRooms *utils.SafeMapInt32 // map[roomId]*RoomConnection
	MaxRoomCount   int                 // 最大房间数量
	roomMutex      sync.RWMutex        // 房间操作锁

	// 游戏事件队列（聚合所有房间的事件）
	MapComment *utils.SafeSlice[[]byte]
	MapGift    *utils.SafeSlice[[]*payload.PayloadData]
	MapFans    *utils.SafeSlice[[]*payload.PayloadData]
	MapLike    *utils.SafeSlice[[]byte]

	// 同步机制
	broadcastMutex sync.RWMutex // 广播锁
	syncVersion    int64        // 同步版本号

	// 玩家数据
	MapPlayer *utils.SafeMapString // 参加的玩家数据
}

// NewLiveMultiGame 创建新的多人游戏实例
func NewLiveMultiGame(gameId int32) *LiveMultiGame {
	game := &LiveMultiGame{
		GameID:         gameId,
		ConnectedRooms: utils.NewSafeMapInt32(),
		MaxRoomCount:   10, // 默认最大10个房间
		PlayState:      PLAY_STATE_WAIT,
		syncVersion:    0,
	}
	game.Init()
	return game
}

// Init 初始化游戏数据
func (game *LiveMultiGame) Init() {
	// 初始化事件队列
	game.MapComment = utils.NewSafeSlice[[]byte]()
	game.MapGift = utils.NewSafeSlice[[]*payload.PayloadData]()
	game.MapFans = utils.NewSafeSlice[[]*payload.PayloadData]()
	game.MapLike = utils.NewSafeSlice[[]byte]()

	core.LogInfo("LiveMultiGame初始化完成", "GameID:", game.GameID)
}

func (game *LiveMultiGame) GetMapPlayer() *utils.SafeMapString {
	return game.MapPlayer
}

// Reset 重置游戏数据
func (game *LiveMultiGame) Reset(close bool) {
	game.roomMutex.Lock()
	defer game.roomMutex.Unlock()

	if close {
		// 如果是关闭重置，断开所有房间连接
		game.ConnectedRooms.Range(func(key int32, value interface{}) bool {
			roomConn := value.(*RoomConnection)
			roomConn.Status = RoomStatusDisconnected
			return true
		})
		game.ConnectedRooms.Clear()
	}

	// 重置游戏状态
	game.PlayState = PLAY_STATE_WAIT
	game.IsOver = false
	game.StartTime = 0
	game.EndTime = 0
	game.LogicFrame = 0

	// 重新初始化事件队列
	game.MapComment = utils.NewSafeSlice[[]byte]()
	game.MapGift = utils.NewSafeSlice[[]*payload.PayloadData]()
	game.MapFans = utils.NewSafeSlice[[]*payload.PayloadData]()
	game.MapLike = utils.NewSafeSlice[[]byte]()
	game.WinStreakPool = 0

	// 更新积分池
	game.ScorePool = game.ScorePool * 3 / 10

	// 增加同步版本号
	atomic.AddInt64(&game.syncVersion, 1)

	core.LogInfo("LiveMultiGame重置完成", "GameID:", game.GameID, "close:", close)
}

// ==================== 游戏状态管理方法 ====================

// GetPlayState 获取游戏状态
func (game *LiveMultiGame) GetPlayState() int {
	return game.PlayState
}

// SetPlayState 设置游戏状态
func (game *LiveMultiGame) SetPlayState(state int) {
	game.PlayState = state
	atomic.AddInt64(&game.syncVersion, 1)
	// 广播状态变化到所有房间
	game.BroadcastGameState()
}

// GetScorePool 获取积分池
func (game *LiveMultiGame) GetScorePool() int64 {
	return game.ScorePool
}

// SetScorePool 设置积分池
func (game *LiveMultiGame) SetScorePool(pool int64) {
	game.ScorePool = pool
	atomic.AddInt64(&game.syncVersion, 1)
}

// GetWinStreakPool 获取连胜池
func (game *LiveMultiGame) GetWinStreakPool() int32 {
	return game.WinStreakPool
}

// SetWinStreakPool 设置连胜池
func (game *LiveMultiGame) SetWinStreakPool(pool int32) {
	game.WinStreakPool = pool
	atomic.AddInt64(&game.syncVersion, 1)
}

// GetLogicFrame 获取逻辑帧
func (game *LiveMultiGame) GetLogicFrame() int32 {
	return game.LogicFrame
}

// SetLogicFrame 设置逻辑帧
func (game *LiveMultiGame) SetLogicFrame(frame int32) {
	game.LogicFrame = frame
}

// GetStartTime 获取开始时间
func (game *LiveMultiGame) GetStartTime() int64 {
	return game.StartTime
}

// SetStartTime 设置开始时间
func (game *LiveMultiGame) SetStartTime(time int64) {
	game.StartTime = time
}

// GetEndTime 获取结束时间
func (game *LiveMultiGame) GetEndTime() int64 {
	return game.EndTime
}

// SetEndTime 设置结束时间
func (game *LiveMultiGame) SetEndTime(time int64) {
	game.EndTime = time
}

// GetIsOver 获取游戏是否结束
func (game *LiveMultiGame) GetIsOver() bool {
	return game.IsOver
}

// SetIsOver 设置游戏是否结束
func (game *LiveMultiGame) SetIsOver(over bool) {
	game.IsOver = over
	atomic.AddInt64(&game.syncVersion, 1)
}

// GetRoundID 获取回合ID
func (game *LiveMultiGame) GetRoundID() string {
	return game.RoundID
}

// SetRoundID 设置回合ID
func (game *LiveMultiGame) SetRoundID(id string) {
	game.RoundID = id
}

// ==================== 多房间管理接口实现 ====================

// AddRoom 添加房间到多人游戏
func (game *LiveMultiGame) AddRoom(room *LiveRoom) error {
	if room == nil {
		return errors.New("房间不能为空")
	}

	game.roomMutex.Lock()
	defer game.roomMutex.Unlock()

	// 检查是否可以接受新房间
	if !game.CanAcceptNewRooms() {
		return errors.New("游戏已达到最大房间数量或已结束")
	}

	// 检查房间是否已经连接
	if _, exists := game.ConnectedRooms.Load(room.RoomId); exists {
		return errors.New("房间已经连接到此游戏")
	}

	// 创建房间连接信息
	roomConn := &RoomConnection{
		Room:       room,
		Status:     RoomStatusConnecting,
		JoinTime:   core.TimeServer().Unix(),
		LastSync:   core.TimeServer().Unix(),
		SyncErrors: 0,
	}

	// 添加到连接列表
	game.ConnectedRooms.Store(room.RoomId, roomConn)

	// 同步当前游戏状态到新房间
	if err := game.SyncRoomState(room); err != nil {
		// 同步失败，移除房间
		game.ConnectedRooms.Delete(room.RoomId)
		return fmt.Errorf("同步房间状态失败: %v", err)
	}

	// 更新房间状态为已连接
	roomConn.Status = RoomStatusConnected

	// 增加同步版本号
	atomic.AddInt64(&game.syncVersion, 1)

	core.LogInfo("房间成功加入多人游戏", "GameID:", game.GameID, "RoomId:", room.RoomId, "连接房间数:", game.GetConnectedRoomCount())

	return nil
}

// RemoveRoom 从多人游戏中移除房间
func (game *LiveMultiGame) RemoveRoom(roomId int32) error {
	game.roomMutex.Lock()
	defer game.roomMutex.Unlock()

	roomConnValue, exists := game.ConnectedRooms.Load(roomId)
	if !exists {
		return errors.New("房间未连接到此游戏")
	}

	roomConn := roomConnValue.(*RoomConnection)

	// 从连接列表中移除
	game.ConnectedRooms.Delete(roomId)

	// 更新房间状态
	roomConn.Status = RoomStatusDisconnected

	// 增加同步版本号
	atomic.AddInt64(&game.syncVersion, 1)

	core.LogInfo("房间从多人游戏中移除", "GameID:", game.GameID, "RoomId:", roomId, "剩余房间数:", game.GetConnectedRoomCount())

	return nil
}

// GetConnectedRooms 获取所有连接的房间
func (game *LiveMultiGame) GetConnectedRooms() []*LiveRoom {
	var rooms []*LiveRoom

	game.ConnectedRooms.Range(func(key int32, value interface{}) bool {
		roomConn := value.(*RoomConnection)
		if roomConn.Status == RoomStatusConnected || roomConn.Status == RoomStatusActive {
			rooms = append(rooms, roomConn.Room)
		}
		return true
	})

	return rooms
}

// GetConnectedRoomCount 获取连接的房间数量
func (game *LiveMultiGame) GetConnectedRoomCount() int {
	count := 0
	game.ConnectedRooms.Range(func(key int32, value interface{}) bool {
		roomConn := value.(*RoomConnection)
		if roomConn.Status == RoomStatusConnected || roomConn.Status == RoomStatusActive {
			count++
		}
		return true
	})
	return count
}

// GetGameID 获取游戏ID
func (game *LiveMultiGame) GetGameID() int32 {
	return game.GameID
}

// CanAcceptNewRooms 检查是否可以接受新房间
func (game *LiveMultiGame) CanAcceptNewRooms() bool {
	// 游戏已结束不能接受新房间
	if game.IsOver {
		return false
	}

	// 检查房间数量限制
	currentCount := game.GetConnectedRoomCount()
	return currentCount < game.MaxRoomCount
}

// GetMaxRoomCount 获取最大房间数量
func (game *LiveMultiGame) GetMaxRoomCount() int {
	return game.MaxRoomCount
}

// SetMaxRoomCount 设置最大房间数量
func (game *LiveMultiGame) SetMaxRoomCount(count int) {
	if count > 0 {
		game.MaxRoomCount = count
	}
}

// ==================== 广播和同步机制 ====================

// BroadcastToAllRooms 向所有连接的房间广播消息
func (game *LiveMultiGame) BroadcastToAllRooms(msgType int, data []byte) {
	game.broadcastMutex.RLock()
	defer game.broadcastMutex.RUnlock()

	rooms := game.GetConnectedRooms()
	for _, room := range rooms {
		if room != nil && !room.IsSerssionNil() {
			room.Session.SendPBMsg(data)
		}
	}

	core.LogDebug("广播消息到所有房间", "GameID:", game.GameID, "房间数:", len(rooms), "消息类型:", msgType)
}

// BroadcastGameState 广播游戏状态到所有房间
func (game *LiveMultiGame) BroadcastGameState() {
	rooms := game.GetConnectedRooms()
	for _, room := range rooms {
		if room != nil && !room.IsSerssionNil() {
			// 发送游戏状态同步消息
			Request.SyncFrameRequest(room.Session, game.LogicFrame)

			// 发送连胜池同步
			Request.SyncWinStreakPoolRequest(room.Session, game.WinStreakPool)
		}
	}

	core.LogDebug("广播游戏状态", "GameID:", game.GameID, "房间数:", len(rooms), "逻辑帧:", game.LogicFrame)
}

// SyncRoomState 同步游戏状态到指定房间
func (game *LiveMultiGame) SyncRoomState(room *LiveRoom) error {
	if room == nil || room.IsSerssionNil() {
		return errors.New("房间或会话无效")
	}

	// 同步基本游戏状态
	if game.PlayState >= PLAY_STATE_READY {
		// 同步逻辑帧
		Request.SyncFrameRequest(room.Session, game.LogicFrame)

		// 同步连胜池
		Request.SyncWinStreakPoolRequest(room.Session, game.WinStreakPool)

		// 数据系统已删除，不再同步玩家数据
	}

	core.LogInfo("房间状态同步完成", "GameID:", game.GameID, "RoomId:", room.RoomId, "PlayState:", game.PlayState)
	return nil
}

// ==================== 游戏生命周期方法 ====================

// Create 创建游戏
func (game *LiveMultiGame) Create(createGame *Message.CreateGameC2S) {
	// 根据MapId设置游戏配置（参考LiveGame的实现）
	mapId := createGame.MapId
	// 配置系统已删除，使用默认值

	game.PlayState = PLAY_STATE_READY
	game.RoundID = GetRoomMgr().GetRoomRoundID()

	// 广播游戏创建状态到所有房间
	game.BroadcastGameState()

	core.LogInfo("多人游戏创建完成", "GameID:", game.GameID, "MapId:", mapId)
}

// GameStart 开始游戏
func (game *LiveMultiGame) GameStart() {
	game.StartTime = core.TimeServer().Unix()
	game.EndTime = game.StartTime + 300 // 默认5分钟游戏时长
	game.PlayState = PLAY_STATE_START
	game.IsOver = false

	// 向所有房间发送游戏开始消息
	rooms := game.GetConnectedRooms()
	for _, room := range rooms {
		if room != nil && !room.IsSerssionNil() {
			Request.GameStartRequest(room.Session)
		}
	}

	// 特殊礼物系统已删除，不再启动定时器

	// 广播游戏状态
	game.BroadcastGameState()

	core.LogInfo("多人游戏开始", "GameID:", game.GameID, "连接房间数:", len(rooms))
}

// GameOver 游戏结束
func (game *LiveMultiGame) GameOver(gameOverC2S *Message.GameOverC2S) {
	game.PlayState = PLAY_STATE_END
	game.IsOver = true

	// 向所有房间发送游戏结束消息并切换回单人游戏模式
	rooms := game.GetConnectedRooms()
	for _, room := range rooms {
		if room != nil && !room.IsSerssionNil() {
			// 发送游戏结束的具体消息
			Request.SyncFrameRequest(room.Session, game.LogicFrame)
		}

		// 将房间切换回单人游戏模式
		if room != nil {
			room.SwitchToLiveGame()
			core.LogInfo("房间已切换回单人游戏模式", "GameID:", game.GameID, "RoomId:", room.RoomId)
		}
	}

	// 广播最终游戏状态
	game.BroadcastGameState()

	core.LogInfo("多人游戏结束，所有房间已切换回单人游戏模式", "GameID:", game.GameID, "连接房间数:", len(rooms))
}

// InitGameOver 初始化游戏结束
func (game *LiveMultiGame) InitGameOver() {
	// 向所有房间发送初始化游戏结束消息
	rooms := game.GetConnectedRooms()
	for _, room := range rooms {
		if room != nil && !room.IsSerssionNil() {
			Request.InitGameOverRequest(room.Session)
		}
	}

	core.LogInfo("多人游戏初始化结束", "GameID:", game.GameID)
}

// ==================== 事件处理方法 ====================

// AddComment 添加评论
func (game *LiveMultiGame) AddComment(comment []byte) {
	game.MapComment.Append(comment)
}

// AddGift 添加礼物
func (game *LiveMultiGame) AddGift(comment []*payload.PayloadData, msgId string) {
	for i := 0; i < len(comment); i++ {
		comment[i].UMsgId = msgId
		// 为所有连接的房间记录礼物
		rooms := game.GetConnectedRooms()
		for _, room := range rooms {
			GetGiftRecordMgr().CreateRecord(room.RoomId, room.AnchorId, comment[i])
		}
	}
	game.MapGift.Append(comment)
}

// AddFans 添加粉丝
func (game *LiveMultiGame) AddFans(comment []*payload.PayloadData, msgId string) {
	for i := 0; i < len(comment); i++ {
		comment[i].UMsgId = msgId
		// 为所有连接的房间记录粉丝
		rooms := game.GetConnectedRooms()
		for _, room := range rooms {
			GetGiftRecordMgr().CreateRecord(room.RoomId, room.AnchorId, comment[i])
		}
	}
	game.MapFans.Append(comment)
}

// AddLike 添加点赞
func (game *LiveMultiGame) AddLike(comment []byte) {
	game.MapLike.Append(comment)
}

// ExComment 处理评论事件
func (game *LiveMultiGame) ExComment() {
	// 处理评论队列中的事件
	for game.MapComment.Len() > 0 {
		if comment, err := game.MapComment.RemoveAt(0); err == nil {
			// 向所有房间广播评论
			game.BroadcastToAllRooms(0, comment)
		}
	}
}

// ExGift 处理礼物事件
func (game *LiveMultiGame) ExGift() {
	// 处理礼物队列中的事件
	for game.MapGift.Len() > 0 {
		if gifts, err := game.MapGift.RemoveAt(0); err == nil {
			// 处理礼物逻辑
			for _, gift := range gifts {
				// 这里可以添加具体的礼物处理逻辑
				core.LogDebug("处理礼物", "GameID:", game.GameID, "Gift:", gift.Content)
			}
		}
	}
}

// ExFans 处理粉丝事件
func (game *LiveMultiGame) ExFans() {
	// 处理粉丝队列中的事件
	for game.MapFans.Len() > 0 {
		if fans, err := game.MapFans.RemoveAt(0); err == nil {
			// 处理粉丝逻辑
			for _, fan := range fans {
				// 这里可以添加具体的粉丝处理逻辑
				core.LogDebug("处理粉丝", "GameID:", game.GameID, "Fan:", fan.Content)
			}
		}
	}
}

// ExLike 处理点赞事件
func (game *LiveMultiGame) ExLike() {
	// 处理点赞队列中的事件
	for game.MapLike.Len() > 0 {
		if like, err := game.MapLike.RemoveAt(0); err == nil {
			// 向所有房间广播点赞
			game.BroadcastToAllRooms(0, like)
		}
	}
}

// ==================== 击杀系统方法（简化实现） ====================

// PlayerKillCount 玩家击杀计数（简化实现）
func (game *LiveMultiGame) PlayerKillCount(playerKillCount *Message.PlayerKillCountC2S) {
	// 击杀系统已删除，空实现
	core.LogInfo("玩家击杀计数（已禁用）", "GameID:", game.GameID)
}

// ==================== 用户交互接口的多房间支持 ====================

// ProcessComment 处理评论（多房间版本）
func (game *LiveMultiGame) ProcessComment(roomId int32, comment []byte) {
	// 添加到评论队列
	game.AddComment(comment)

	// 向所有房间广播评论（除了发送房间）
	rooms := game.GetConnectedRooms()
	for _, room := range rooms {
		if room != nil && !room.IsSerssionNil() && room.RoomId != roomId {
			room.Session.SendPBMsg(comment)
		}
	}

	core.LogDebug("多人游戏处理评论", "GameID:", game.GameID, "FromRoom:", roomId, "广播到房间数:", len(rooms)-1)
}

// ProcessGift 处理礼物（多房间版本）
func (game *LiveMultiGame) ProcessGift(roomId int32, gifts []*payload.PayloadData, msgId string) {
	// 添加到礼物队列
	game.AddGift(gifts, msgId)

	// 礼物积分处理功能已删除（数据系统已删除）

	// 礼物广播功能已删除

	core.LogDebug("多人游戏处理礼物", "GameID:", game.GameID, "FromRoom:", roomId, "礼物数量:", len(gifts))
}

// ProcessLike 处理点赞（多房间版本）
func (game *LiveMultiGame) ProcessLike(roomId int32, like []byte) {
	// 添加到点赞队列
	game.AddLike(like)

	// 向所有房间广播点赞（除了发送房间）
	rooms := game.GetConnectedRooms()
	for _, room := range rooms {
		if room != nil && !room.IsSerssionNil() && room.RoomId != roomId {
			room.Session.SendPBMsg(like)
		}
	}

	core.LogDebug("多人游戏处理点赞", "GameID:", game.GameID, "FromRoom:", roomId, "广播到房间数:", len(rooms)-1)
}

// ProcessFans 处理粉丝事件（多房间版本）
func (game *LiveMultiGame) ProcessFans(roomId int32, fans []*payload.PayloadData, msgId string) {
	// 添加到粉丝队列
	game.AddFans(fans, msgId)

	// 粉丝积分处理功能已删除（数据系统已删除）

	// 粉丝广播功能已删除

	core.LogDebug("多人游戏处理粉丝", "GameID:", game.GameID, "FromRoom:", roomId, "粉丝数量:", len(fans))
}

// getRoomById 根据ID获取房间
func (game *LiveMultiGame) getRoomById(roomId int32) *LiveRoom {
	rooms := game.GetConnectedRooms()
	for _, room := range rooms {
		if room.RoomId == roomId {
			return room
		}
	}
	return nil
}

// ==================== 多房间特有的辅助方法 ====================

// SyncAllRoomsData 同步所有房间数据
func (game *LiveMultiGame) SyncAllRoomsData() {
	rooms := game.GetConnectedRooms()
	for _, room := range rooms {
		if err := game.SyncRoomState(room); err != nil {
			core.LogError("同步房间数据失败", "GameID:", game.GameID, "RoomId:", room.RoomId, "Error:", err)
		}
	}
}

// CleanupDisconnectedRooms 清理断开连接的房间
func (game *LiveMultiGame) CleanupDisconnectedRooms() {
	var disconnectedRooms []int32

	game.ConnectedRooms.Range(func(key int32, value interface{}) bool {
		roomConn := value.(*RoomConnection)
		if roomConn.Status == RoomStatusDisconnected || roomConn.Room.IsSerssionNil() {
			disconnectedRooms = append(disconnectedRooms, key)
		}
		return true
	})

	for _, roomId := range disconnectedRooms {
		err := game.RemoveRoom(roomId)
		if err != nil {
			core.LogError("清理断开连接的房间失败", "GameID:", game.GameID, "RoomId:", roomId, "Error:", err)
		}
	}

	if len(disconnectedRooms) > 0 {
		core.LogInfo("清理断开连接的房间", "GameID:", game.GameID, "数量:", len(disconnectedRooms))
	}
}
