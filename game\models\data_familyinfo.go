package models

import (
	"fmt"
	"sync"
	"zone/lib/core"
	"zone/lib/db"
	"zone/lib/utils"
)

// FamilyInfoDB ! 玩家数据结构
type FamilyInfoDB struct {
	Id         int64     `db:"id"`         //! 直播Id-服务器上的Id
	Name       string    `db:"name"`       //! 平台Id-OpenId
	Score      int64     `db:"score"`      //! 头像
	PlayerList string    `db:"playerlist"` // 家族
	MapPlayer  *sync.Map // 家族Map

	db.DataUpdate //! 数据库接口
}

type FamilyTopNode struct {
	Name  string //! 平台Id
	Rank  int    //! 排名
	Score int64
}

func (self *FamilyInfoDB) SetFamilyScore(liveId int64, score int64) {
	// 更新家族总分
	self.Score += score

	// 更新玩家个人贡献分数
	if existingScore, ok := self.MapPlayer.Load(liveId); ok {
		// 如果玩家已存在，累加分数
		if currentScore, ok := existingScore.(int64); ok {
			self.MapPlayer.Store(liveId, currentScore+score)
		} else {
			// 类型不匹配，记录错误并重置
			core.LogError("SetFamilyScore: 期望int64类型", "LiveId:", liveId, "Value:", existingScore, "Type:", fmt.Sprintf("%T", existingScore))
			self.MapPlayer.Store(liveId, score)
		}
	} else {
		// 新玩家，直接存储分数
		self.MapPlayer.Store(liveId, score)
	}
	self.Encode()
}

// Decode save
func (self *FamilyInfoDB) Decode() {
	// 确保MapPlayer已初始化
	if self.MapPlayer == nil {
		self.MapPlayer = new(sync.Map)
	}

	// 处理空字符串情况
	if self.PlayerList == "" || self.PlayerList == "{}" {
		return
	}

	// 使用专用的JSONToSyncMapInt64函数进行反序列化
	if err := utils.JSONToSyncMapInt64(self.PlayerList, self.MapPlayer); err != nil {
		core.LogError("家族玩家列表解码失败", "Id:", self.Id, "Name:", self.Name, "PlayerList:", self.PlayerList, "Error:", err)
		// 重置为空，避免后续错误
		self.PlayerList = "{}"
	}
}

// Encode !
func (self *FamilyInfoDB) Encode() {
	// 确保MapPlayer已初始化
	if self.MapPlayer == nil {
		self.MapPlayer = new(sync.Map)
	}

	content, err := utils.SyncMapInt64ToJSON(self.MapPlayer)
	if err != nil {
		core.LogError("家族玩家列表编码失败", "Id:", self.Id, "Name:", self.Name, "Error:", err)
		self.PlayerList = "{}" // 设置为空JSON对象，避免数据库存储问题
		return
	}
	self.PlayerList = content
}

// OnSave ! 储存
func (self *FamilyInfoDB) OnSave(sql bool) {
	self.Encode()
	self.Update(sql, false)
}
