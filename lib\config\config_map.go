// Code generated by pb_exporter.go. DO NOT EDIT.
package config

import (
	"reflect"
	"zone/pb/Data"
	"google.golang.org/protobuf/proto"
)

// initTypeMap 初始化类型映射表
func (dl *DataLoader) initTypeMap() {
	// Actorconfig
	dl.RegisterConfigType("actorconfig", &ConfigTypeInfo{
		MessageName: "Actorconfig",
		MessageType: reflect.TypeOf((*Data.Actorconfig)(nil)),
		NewInstance: func() proto.Message { return &Data.Actorconfig{} },
	})

	// Blindbox
	dl.RegisterConfigType("blindbox", &ConfigTypeInfo{
		MessageName: "Blindbox",
		MessageType: reflect.TypeOf((*Data.Blindbox)(nil)),
		NewInstance: func() proto.Message { return &Data.Blindbox{} },
	})

	// Giftconfig
	dl.RegisterConfigType("giftconfig", &ConfigTypeInfo{
		MessageName: "Giftconfig",
		MessageType: reflect.TypeOf((*Data.Giftconfig)(nil)),
		NewInstance: func() proto.Message { return &Data.Giftconfig{} },
	})

	// Mapconfig
	dl.RegisterConfigType("mapconfig", &ConfigTypeInfo{
		MessageName: "Mapconfig",
		MessageType: reflect.TypeOf((*Data.Mapconfig)(nil)),
		NewInstance: func() proto.Message { return &Data.Mapconfig{} },
	})

	// Skillconfig
	dl.RegisterConfigType("skillconfig", &ConfigTypeInfo{
		MessageName: "Skillconfig",
		MessageType: reflect.TypeOf((*Data.Skillconfig)(nil)),
		NewInstance: func() proto.Message { return &Data.Skillconfig{} },
	})

	// Skilllevelconfig
	dl.RegisterConfigType("skilllevelconfig", &ConfigTypeInfo{
		MessageName: "Skilllevelconfig",
		MessageType: reflect.TypeOf((*Data.Skilllevelconfig)(nil)),
		NewInstance: func() proto.Message { return &Data.Skilllevelconfig{} },
	})

	// Treasureconfig
	dl.RegisterConfigType("treasureconfig", &ConfigTypeInfo{
		MessageName: "Treasureconfig",
		MessageType: reflect.TypeOf((*Data.Treasureconfig)(nil)),
		NewInstance: func() proto.Message { return &Data.Treasureconfig{} },
	})

}
