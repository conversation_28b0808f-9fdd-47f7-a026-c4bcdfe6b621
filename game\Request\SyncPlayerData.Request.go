// Code generated by pb_exporter.go. DO NOT EDIT.
package Request

import (
	"zone/lib/network"
	"zone/pb/Message"
)

func SyncPlayerDataRequest(session *network.Session, playerid int32, sync *Message.PlayerSyncDTO, scorepool int64) {
	syncplayerdataS2C := &Message.SyncPlayerDataS2C{
		PlayerId: int32(playerid),
		Sync: sync,
		ScorePool: scorepool,
	}
	pbMsg := network.NewPBMsg(SyncPlayerData, syncplayerdataS2C)
	session.SendPBMsg(pbMsg.MsgToBytes())
}
