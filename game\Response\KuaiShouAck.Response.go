package Response

import (
	"zone/game/Request"
	"zone/game/mods"
	"zone/game/platform/kuaishou"
	"zone/lib/network"
	"zone/pb/Message"
)

const (
	KuaiShouAck_Receive = "cpClientReceive"
	KuaiShouAck_Show    = "cpClientShow"
)

func KuaiShouAckResponse(session *network.Session, msg any) {
	if network.IsTestServer {
		return
	}

	// 将 msg 转换为对应的 C2S 消息结构指针
	kuaishouack := msg.(*Message.KuaiShouAckC2S)

	var room = mods.GetSessionRoomMgr().GetRoom(session)
	if room == nil {
		Request.ErrorCodeRequest(session, 0, 0, "查找房间失败，请重启直播间")
		return
	}

	switch kuaishouack.AckType {
	case KuaiShouAck_Receive:
		kuaishou.KS_ReceiveAck(room.RoomPlatformId, kuaishouack.MsgId, kuaishouack.Time)
	case KuaiShouAck_Show:
		kuaishou.KS_ShowAck(room.RoomPlatformId, kuaishouack.MsgId, kuaishouack.Time)
	}

	// TODO: 实现具体的业务逻辑
	// 从 kuaishouack 中提取字段值
	// 处理业务逻辑
	// 发送响应消息

	// 示例：创建响应消息
	// KuaiShouAckRequest(session, /* 参数 */)
}
