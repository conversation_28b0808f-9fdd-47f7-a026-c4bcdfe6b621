// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.5.1
// source: playerMsg.proto

package Message

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AddPlayerC2S struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Suc           int32                  `protobuf:"varint,1,opt,name=suc,proto3" json:"suc,omitempty"`
	PlayerId      int32                  `protobuf:"varint,2,opt,name=playerId,proto3" json:"playerId,omitempty"`
	BlockId       int32                  `protobuf:"varint,3,opt,name=blockId,proto3" json:"blockId,omitempty"`
	RoomId        int32                  `protobuf:"varint,4,opt,name=roomId,proto3" json:"roomId,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddPlayerC2S) Reset() {
	*x = AddPlayerC2S{}
	mi := &file_playerMsg_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddPlayerC2S) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddPlayerC2S) ProtoMessage() {}

func (x *AddPlayerC2S) ProtoReflect() protoreflect.Message {
	mi := &file_playerMsg_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddPlayerC2S.ProtoReflect.Descriptor instead.
func (*AddPlayerC2S) Descriptor() ([]byte, []int) {
	return file_playerMsg_proto_rawDescGZIP(), []int{0}
}

func (x *AddPlayerC2S) GetSuc() int32 {
	if x != nil {
		return x.Suc
	}
	return 0
}

func (x *AddPlayerC2S) GetPlayerId() int32 {
	if x != nil {
		return x.PlayerId
	}
	return 0
}

func (x *AddPlayerC2S) GetBlockId() int32 {
	if x != nil {
		return x.BlockId
	}
	return 0
}

func (x *AddPlayerC2S) GetRoomId() int32 {
	if x != nil {
		return x.RoomId
	}
	return 0
}

type AddPlayerS2C struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	Player          *PlayerDTO             `protobuf:"bytes,1,opt,name=player,proto3" json:"player,omitempty"`
	ForceSit        int32                  `protobuf:"varint,2,opt,name=forceSit,proto3" json:"forceSit,omitempty"`               // 是否强制落座
	OldBlockID      int32                  `protobuf:"varint,3,opt,name=oldBlockID,proto3" json:"oldBlockID,omitempty"`           // 老大地块ID
	SmallBlockIndex int32                  `protobuf:"varint,4,opt,name=smallBlockIndex,proto3" json:"smallBlockIndex,omitempty"` // 新加入的玩家的小方块索引 老大地块的小地块数量随机 random[0,smallBlockCount)
	WinStreakPool   int32                  `protobuf:"varint,5,opt,name=winStreakPool,proto3" json:"winStreakPool,omitempty"`     // 连胜池
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *AddPlayerS2C) Reset() {
	*x = AddPlayerS2C{}
	mi := &file_playerMsg_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddPlayerS2C) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddPlayerS2C) ProtoMessage() {}

func (x *AddPlayerS2C) ProtoReflect() protoreflect.Message {
	mi := &file_playerMsg_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddPlayerS2C.ProtoReflect.Descriptor instead.
func (*AddPlayerS2C) Descriptor() ([]byte, []int) {
	return file_playerMsg_proto_rawDescGZIP(), []int{1}
}

func (x *AddPlayerS2C) GetPlayer() *PlayerDTO {
	if x != nil {
		return x.Player
	}
	return nil
}

func (x *AddPlayerS2C) GetForceSit() int32 {
	if x != nil {
		return x.ForceSit
	}
	return 0
}

func (x *AddPlayerS2C) GetOldBlockID() int32 {
	if x != nil {
		return x.OldBlockID
	}
	return 0
}

func (x *AddPlayerS2C) GetSmallBlockIndex() int32 {
	if x != nil {
		return x.SmallBlockIndex
	}
	return 0
}

func (x *AddPlayerS2C) GetWinStreakPool() int32 {
	if x != nil {
		return x.WinStreakPool
	}
	return 0
}

// cmd=2002 玩家发送弹幕
type PlayerOperateC2S struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Type          int32                  `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`
	IsSuccess     int32                  `protobuf:"varint,2,opt,name=isSuccess,proto3" json:"isSuccess,omitempty"`
	MsgId         string                 `protobuf:"bytes,3,opt,name=msgId,proto3" json:"msgId,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PlayerOperateC2S) Reset() {
	*x = PlayerOperateC2S{}
	mi := &file_playerMsg_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PlayerOperateC2S) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlayerOperateC2S) ProtoMessage() {}

func (x *PlayerOperateC2S) ProtoReflect() protoreflect.Message {
	mi := &file_playerMsg_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlayerOperateC2S.ProtoReflect.Descriptor instead.
func (*PlayerOperateC2S) Descriptor() ([]byte, []int) {
	return file_playerMsg_proto_rawDescGZIP(), []int{2}
}

func (x *PlayerOperateC2S) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *PlayerOperateC2S) GetIsSuccess() int32 {
	if x != nil {
		return x.IsSuccess
	}
	return 0
}

func (x *PlayerOperateC2S) GetMsgId() string {
	if x != nil {
		return x.MsgId
	}
	return ""
}

type PlayerOperateS2C struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PlayerOperate *PlayerOperateDTO      `protobuf:"bytes,1,opt,name=playerOperate,proto3" json:"playerOperate,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PlayerOperateS2C) Reset() {
	*x = PlayerOperateS2C{}
	mi := &file_playerMsg_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PlayerOperateS2C) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlayerOperateS2C) ProtoMessage() {}

func (x *PlayerOperateS2C) ProtoReflect() protoreflect.Message {
	mi := &file_playerMsg_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlayerOperateS2C.ProtoReflect.Descriptor instead.
func (*PlayerOperateS2C) Descriptor() ([]byte, []int) {
	return file_playerMsg_proto_rawDescGZIP(), []int{3}
}

func (x *PlayerOperateS2C) GetPlayerOperate() *PlayerOperateDTO {
	if x != nil {
		return x.PlayerOperate
	}
	return nil
}

// cmd=2003 同步玩家数据
type SyncPlayerDataC2S struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Time          int32                  `protobuf:"varint,1,opt,name=time,proto3" json:"time,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SyncPlayerDataC2S) Reset() {
	*x = SyncPlayerDataC2S{}
	mi := &file_playerMsg_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SyncPlayerDataC2S) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncPlayerDataC2S) ProtoMessage() {}

func (x *SyncPlayerDataC2S) ProtoReflect() protoreflect.Message {
	mi := &file_playerMsg_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncPlayerDataC2S.ProtoReflect.Descriptor instead.
func (*SyncPlayerDataC2S) Descriptor() ([]byte, []int) {
	return file_playerMsg_proto_rawDescGZIP(), []int{4}
}

func (x *SyncPlayerDataC2S) GetTime() int32 {
	if x != nil {
		return x.Time
	}
	return 0
}

type SyncPlayerDataS2C struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PlayerId      int32                  `protobuf:"varint,1,opt,name=playerId,proto3" json:"playerId,omitempty"` // 玩家id
	Sync          *PlayerSyncDTO         `protobuf:"bytes,2,opt,name=sync,proto3" json:"sync,omitempty"`          // 玩家积分
	ScorePool     int64                  `protobuf:"varint,3,opt,name=scorePool,proto3" json:"scorePool,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SyncPlayerDataS2C) Reset() {
	*x = SyncPlayerDataS2C{}
	mi := &file_playerMsg_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SyncPlayerDataS2C) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncPlayerDataS2C) ProtoMessage() {}

func (x *SyncPlayerDataS2C) ProtoReflect() protoreflect.Message {
	mi := &file_playerMsg_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncPlayerDataS2C.ProtoReflect.Descriptor instead.
func (*SyncPlayerDataS2C) Descriptor() ([]byte, []int) {
	return file_playerMsg_proto_rawDescGZIP(), []int{5}
}

func (x *SyncPlayerDataS2C) GetPlayerId() int32 {
	if x != nil {
		return x.PlayerId
	}
	return 0
}

func (x *SyncPlayerDataS2C) GetSync() *PlayerSyncDTO {
	if x != nil {
		return x.Sync
	}
	return nil
}

func (x *SyncPlayerDataS2C) GetScorePool() int64 {
	if x != nil {
		return x.ScorePool
	}
	return 0
}

// cmd=2002 玩家发送弹幕
type KuaiShouAckC2S struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AckType       string                 `protobuf:"bytes,1,opt,name=AckType,proto3" json:"AckType,omitempty"`
	Time          int64                  `protobuf:"varint,2,opt,name=time,proto3" json:"time,omitempty"`
	MsgId         string                 `protobuf:"bytes,3,opt,name=msgId,proto3" json:"msgId,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *KuaiShouAckC2S) Reset() {
	*x = KuaiShouAckC2S{}
	mi := &file_playerMsg_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *KuaiShouAckC2S) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KuaiShouAckC2S) ProtoMessage() {}

func (x *KuaiShouAckC2S) ProtoReflect() protoreflect.Message {
	mi := &file_playerMsg_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KuaiShouAckC2S.ProtoReflect.Descriptor instead.
func (*KuaiShouAckC2S) Descriptor() ([]byte, []int) {
	return file_playerMsg_proto_rawDescGZIP(), []int{6}
}

func (x *KuaiShouAckC2S) GetAckType() string {
	if x != nil {
		return x.AckType
	}
	return ""
}

func (x *KuaiShouAckC2S) GetTime() int64 {
	if x != nil {
		return x.Time
	}
	return 0
}

func (x *KuaiShouAckC2S) GetMsgId() string {
	if x != nil {
		return x.MsgId
	}
	return ""
}

type KuaiShouAckS2C struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *KuaiShouAckS2C) Reset() {
	*x = KuaiShouAckS2C{}
	mi := &file_playerMsg_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *KuaiShouAckS2C) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KuaiShouAckS2C) ProtoMessage() {}

func (x *KuaiShouAckS2C) ProtoReflect() protoreflect.Message {
	mi := &file_playerMsg_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KuaiShouAckS2C.ProtoReflect.Descriptor instead.
func (*KuaiShouAckS2C) Descriptor() ([]byte, []int) {
	return file_playerMsg_proto_rawDescGZIP(), []int{7}
}

var File_playerMsg_proto protoreflect.FileDescriptor

const file_playerMsg_proto_rawDesc = "" +
	"\n" +
	"\x0fplayerMsg.proto\x12\n" +
	"PB.Message\x1a\fcommon.proto\"n\n" +
	"\fAddPlayerC2S\x12\x10\n" +
	"\x03suc\x18\x01 \x01(\x05R\x03suc\x12\x1a\n" +
	"\bplayerId\x18\x02 \x01(\x05R\bplayerId\x12\x18\n" +
	"\ablockId\x18\x03 \x01(\x05R\ablockId\x12\x16\n" +
	"\x06roomId\x18\x04 \x01(\x05R\x06roomId\"\xc9\x01\n" +
	"\fAddPlayerS2C\x12-\n" +
	"\x06player\x18\x01 \x01(\v2\x15.PB.Message.PlayerDTOR\x06player\x12\x1a\n" +
	"\bforceSit\x18\x02 \x01(\x05R\bforceSit\x12\x1e\n" +
	"\n" +
	"oldBlockID\x18\x03 \x01(\x05R\n" +
	"oldBlockID\x12(\n" +
	"\x0fsmallBlockIndex\x18\x04 \x01(\x05R\x0fsmallBlockIndex\x12$\n" +
	"\rwinStreakPool\x18\x05 \x01(\x05R\rwinStreakPool\"Z\n" +
	"\x10PlayerOperateC2S\x12\x12\n" +
	"\x04type\x18\x01 \x01(\x05R\x04type\x12\x1c\n" +
	"\tisSuccess\x18\x02 \x01(\x05R\tisSuccess\x12\x14\n" +
	"\x05msgId\x18\x03 \x01(\tR\x05msgId\"V\n" +
	"\x10PlayerOperateS2C\x12B\n" +
	"\rplayerOperate\x18\x01 \x01(\v2\x1c.PB.Message.PlayerOperateDTOR\rplayerOperate\"'\n" +
	"\x11SyncPlayerDataC2S\x12\x12\n" +
	"\x04time\x18\x01 \x01(\x05R\x04time\"|\n" +
	"\x11SyncPlayerDataS2C\x12\x1a\n" +
	"\bplayerId\x18\x01 \x01(\x05R\bplayerId\x12-\n" +
	"\x04sync\x18\x02 \x01(\v2\x19.PB.Message.PlayerSyncDTOR\x04sync\x12\x1c\n" +
	"\tscorePool\x18\x03 \x01(\x03R\tscorePool\"T\n" +
	"\x0eKuaiShouAckC2S\x12\x18\n" +
	"\aAckType\x18\x01 \x01(\tR\aAckType\x12\x12\n" +
	"\x04time\x18\x02 \x01(\x03R\x04time\x12\x14\n" +
	"\x05msgId\x18\x03 \x01(\tR\x05msgId\"\x10\n" +
	"\x0eKuaiShouAckS2Cb\x06proto3"

var (
	file_playerMsg_proto_rawDescOnce sync.Once
	file_playerMsg_proto_rawDescData []byte
)

func file_playerMsg_proto_rawDescGZIP() []byte {
	file_playerMsg_proto_rawDescOnce.Do(func() {
		file_playerMsg_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_playerMsg_proto_rawDesc), len(file_playerMsg_proto_rawDesc)))
	})
	return file_playerMsg_proto_rawDescData
}

var file_playerMsg_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_playerMsg_proto_goTypes = []any{
	(*AddPlayerC2S)(nil),      // 0: PB.Message.AddPlayerC2S
	(*AddPlayerS2C)(nil),      // 1: PB.Message.AddPlayerS2C
	(*PlayerOperateC2S)(nil),  // 2: PB.Message.PlayerOperateC2S
	(*PlayerOperateS2C)(nil),  // 3: PB.Message.PlayerOperateS2C
	(*SyncPlayerDataC2S)(nil), // 4: PB.Message.SyncPlayerDataC2S
	(*SyncPlayerDataS2C)(nil), // 5: PB.Message.SyncPlayerDataS2C
	(*KuaiShouAckC2S)(nil),    // 6: PB.Message.KuaiShouAckC2S
	(*KuaiShouAckS2C)(nil),    // 7: PB.Message.KuaiShouAckS2C
	(*PlayerDTO)(nil),         // 8: PB.Message.PlayerDTO
	(*PlayerOperateDTO)(nil),  // 9: PB.Message.PlayerOperateDTO
	(*PlayerSyncDTO)(nil),     // 10: PB.Message.PlayerSyncDTO
}
var file_playerMsg_proto_depIdxs = []int32{
	8,  // 0: PB.Message.AddPlayerS2C.player:type_name -> PB.Message.PlayerDTO
	9,  // 1: PB.Message.PlayerOperateS2C.playerOperate:type_name -> PB.Message.PlayerOperateDTO
	10, // 2: PB.Message.SyncPlayerDataS2C.sync:type_name -> PB.Message.PlayerSyncDTO
	3,  // [3:3] is the sub-list for method output_type
	3,  // [3:3] is the sub-list for method input_type
	3,  // [3:3] is the sub-list for extension type_name
	3,  // [3:3] is the sub-list for extension extendee
	0,  // [0:3] is the sub-list for field type_name
}

func init() { file_playerMsg_proto_init() }
func file_playerMsg_proto_init() {
	if File_playerMsg_proto != nil {
		return
	}
	file_common_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_playerMsg_proto_rawDesc), len(file_playerMsg_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_playerMsg_proto_goTypes,
		DependencyIndexes: file_playerMsg_proto_depIdxs,
		MessageInfos:      file_playerMsg_proto_msgTypes,
	}.Build()
	File_playerMsg_proto = out.File
	file_playerMsg_proto_goTypes = nil
	file_playerMsg_proto_depIdxs = nil
}
