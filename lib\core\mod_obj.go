// JoysGames copyrights this specification. No part of this specification may be
// reproduced in any form or means, without the prior written consent of JoysGames.
//
// This specification is preliminary and is subject to change at any time without notice.
// JoysGames assumes no responsibility for any errors contained herein.
//
// The above copyright notice and this permission notice shall be included
// in all copies or substantial portions of the Software.
// @package JGServer
// @copyright joysgames.cn All rights reserved.
// @version v1.0

package core

import (
	"fmt"
	"reflect"
)

var DefMod *ModObj = &ModObj{}

// 模块接口
type IModule interface {
	GetName() string
	Init(name string, host interface{}) IModule
	Load()
	LaterLoad()
	OnStart()
	Save()
	Update(dt int64)
	OnRefresh()
	SendInfo()
	OnDestory()
	HasSaveDirty() bool
	SetSaveDirty(dirty bool)
	SetInvoker(funcName string, cb interface{})
	UnsetInvoker(funcName string, cb interface{})
	Invoker(funcName string, args ...interface{}) ([]reflect.Value, error)
	GetCheckTables() []string
	GetCheckFields() [][2]string
	GetTableStatements() []string
	GetAddField() [][3]string
	GetModifyField() [][4]string
	GetDropField() [][3]string
}

// 模块实现
type ModObj struct {
	invokers  map[string]reflect.Value // 反射列表
	name      string                   // 名称
	host      interface{}              // 模块宿主
	saveDirty bool                     // 存档标记
}

// 取档
func (that *ModObj) GetName() string {
	return that.name
}

// 初始化
func (that *ModObj) Init(name string, host interface{}) IModule {
	that.name = name
	that.invokers = make(map[string]reflect.Value)
	that.host = host
	return that
}

// 取档
func (that *ModObj) Load() {}

// 取档后处理，只执行一次
func (that *ModObj) LaterLoad() {}

// 启动
func (that *ModObj) OnStart() {}

// 存档
func (that *ModObj) Save() {}

// 逻辑帧更新
func (that *ModObj) Update(dt int64) {}

// 定点刷新
func (that *ModObj) OnRefresh() {}

// 同步消息
func (that *ModObj) SendInfo() {}

// 销毁
func (that *ModObj) OnDestory() {}

// 是否需要保存
func (that *ModObj) HasSaveDirty() bool {
	return that.saveDirty
}

// 是否需要保存
func (that *ModObj) SetSaveDirty(dirty bool) {
	that.saveDirty = dirty
}

// 注册函数
func (that *ModObj) SetInvoker(funcName string, cb interface{}) {
	kind := reflect.TypeOf(cb).Kind()
	if kind == reflect.Func {
		that.invokers[funcName] = reflect.ValueOf(cb)
	} else {
		that.invokers[funcName] = cb.(reflect.Value)
	}
}

// 反注册函数
func (that *ModObj) UnsetInvoker(funcName string, cb interface{}) {
	delete(that.invokers, funcName)
}

// 调用注册函数
func (that *ModObj) Invoker(funcName string, args ...interface{}) (_ []reflect.Value, reterr error) {
	handler, ok := that.invokers[funcName]
	if !ok {
		return nil, fmt.Errorf("can't find %s_%s ", that.GetName(), funcName)
	}
	defer func() {
		if err := recover(); err != nil {
			reterr = fmt.Errorf("failed to invoker %s_%s %v", that.GetName(), funcName, err)
			LogError(reterr)
		}
	}()
	funcType := handler.Type()
	options := make([]reflect.Value, len(args))
	for i, v := range args {
		if v == nil {
			options[i] = reflect.New(funcType.In(i)).Elem()
		} else {
			options[i] = reflect.ValueOf(v)
		}
	}
	retArr := handler.Call(options)
	if len(retArr) > 0 {
		last := retArr[len(retArr)-1].Interface()
		opterr, ok := last.(error)
		if ok {
			return nil, opterr // 捕捉函数返回错误
		}
	}
	return retArr, nil
}

// 一键设置导出
func (that *ModObj) SetInvokerAll(inst interface{}) {
	// 基础函数不导出
	filterArr := []string{}
	baseVal := reflect.ValueOf(that)
	baseType := baseVal.Type()
	baseNum := baseType.NumMethod()
	for i := 0; i < baseNum; i++ {
		name := baseType.Method(i).Name
		if name == "Save" || name == "SendInfo" {
			continue
		}
		method := baseVal.Method(i)
		if method.CanInterface() {
			filterArr = append(filterArr, name)
		}
	}

	// 导出
	instVal := reflect.ValueOf(inst)
	instType := instVal.Type()
	methodNum := instType.NumMethod()
	for i := 0; i < methodNum; i++ {
		name := instType.Method(i).Name
		idx := FindSlice(filterArr, func(value interface{}, _ int) bool {
			return name == value
		})
		if idx != -1 {
			continue
		}
		method := instVal.Method(i)
		if method.CanInterface() {
			that.SetInvoker(name, method)
			// Logger.Infof("SetInvoker inst=%v method=%s", instType, name)
		}
	}
}

// 获取表检查语句
func (that *ModObj) GetCheckTables() []string {
	return nil
}

// 获取字段检查语句
func (that *ModObj) GetCheckFields() [][2]string {
	return nil
}

// 获取表创建语句
func (that *ModObj) GetTableStatements() []string {
	return nil
}

// 获取增加字段语句
func (that *ModObj) GetAddField() [][3]string {
	return nil
}

// 获取修改字段语句
func (that *ModObj) GetModifyField() [][4]string {
	return nil
}

// 获取删除字段语句
func (that *ModObj) GetDropField() [][3]string {
	return nil
}
