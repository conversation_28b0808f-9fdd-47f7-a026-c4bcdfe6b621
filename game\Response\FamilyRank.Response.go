// Code generated by pb_exporter.go. DO NOT EDIT.
// Generated at: 2025-06-12 16:35:42

package Response

import (
	"fmt"
	"strconv"
	"zone/game/Request"
	"zone/game/configs"
	"zone/game/models"
	"zone/pb/Message"

	// "zone/lib/core"
	"zone/game/mods"
	"zone/lib/core"
	"zone/lib/network"
	// "zone/pb/Message"
)

func FamilyRankResponse(session *network.Session, msg any) {
	// 将 msg 转换为对应的 C2S 消息结构指针
	// familyrank := msg.(*Message.FamilyRankC2S)
	// core.LogDebug("FamilyRankC2S:", familyrank)
	FamilyData := configs.GetGameConfig().GetFamilyData()

	familyRank := make([]*Message.FamilyRankDTO, 0)
	FamilyData.Range(func(key, value interface{}) bool {
		node := value.(*models.FamilyInfoDB)
		var familyRankDTO = new(Message.FamilyRankDTO)
		familyRankDTO.FamilyName = node.Name
		familyRankDTO.FamilyScore = node.Score
		familyRankDTO.Rank = make([]*Message.ScoreRankDTO, 0)
		node.MapPlayer.Range(func(key, value interface{}) bool {
			// 安全地处理key类型转换
			var playerId int64
			switch k := key.(type) {
			case int64:
				playerId = k
			case string:
				// 尝试将字符串转换为int64
				if id, err := strconv.ParseInt(k, 10, 64); err == nil {
					playerId = id
				} else {
					core.LogError("GetFamilyRank: 无法转换playerId", "Key:", k, "Error:", err)
					return true // 跳过这个条目
				}
			default:
				core.LogError("GetFamilyRank: 不支持的key类型", "Key:", key, "Type:", fmt.Sprintf("%T", key))
				return true // 跳过这个条目
			}

			// 安全地处理value类型转换
			var score int64
			switch v := value.(type) {
			case int64:
				score = v
			case float64:
				score = int64(v)
			case int:
				score = int64(v)
			case int32:
				score = int64(v)
			default:
				core.LogError("GetFamilyRank: 不支持的score类型", "PlayerId:", playerId, "Value:", value, "Type:", fmt.Sprintf("%T", value))
				return true // 跳过这个条目
			}

			playerData := mods.GetLiveMgr().GetLiveData(playerId)
			if playerData == nil {
				core.LogDebug("GetFamilyRank: 玩家数据不存在", "PlayerId:", playerId)
				return true // 跳过这个条目
			}

			var dto = new(Message.ScoreRankDTO)
			dto.PlayerId = int32(playerId)
			dto.HeaderUrl = playerData.Icon
			dto.PlayerName = playerData.PlayerName
			dto.Score = score
			dto.Title = playerData.GetTitle()
			familyRankDTO.Rank = append(familyRankDTO.Rank, dto)
			return true
		})
		if len(familyRankDTO.Rank) > 0 {
			familyRank = append(familyRank, familyRankDTO)
		}
		return true
	})
	Request.FamilyRankRequest(session, familyRank)
	// TODO: 实现具体的业务逻辑
	// 从 familyrank 中提取字段值
	// 处理业务逻辑
	// 发送响应消息

	// 示例：创建响应消息
	// FamilyRankRequire(session, /* 参数 */)
}
