// JoysGames copyrights this specification. No part of this specification may be
// reproduced in any form or means, without the prior written consent of JoysGames.
//
// This specification is preliminary and is subject to change at any time without notice.
// JoysGames assumes no responsibility for any errors contained herein.
//
// The above copyright notice and this permission notice shall be included
// in all copies or substantial portions of the Software.
// @package JGServer
// @copyright joysgames.cn All rights reserved.
// @version v1.0

package core

import (
	"bytes"
	"reflect"
	"runtime"
	"strconv"
	"sync"
	"sync/atomic"
)

const CHAN_TASK_SIZE = 5000 // 玩家管道任务长度

// 协程buff池
var littleBuf = sync.Pool{
	New: func() interface{} {
		buf := make([]byte, 64)
		return &buf
	},
}

// 通道任务池
var chanTaskPool = sync.Pool{
	New: func() interface{} {
		return new(ChanTask)
	},
}

// 获取协程id(调用10000次50ms)
func GetGoroutineID() uint64 {
	bp := littleBuf.Get().(*[]byte)
	defer littleBuf.Put(bp)
	buff := *bp
	runtime.Stack(buff, false)
	buff = bytes.TrimPrefix(buff, []byte("goroutine "))
	buff = buff[:bytes.IndexByte(buff, ' ')]
	goroutineId, _ := strconv.ParseUint(string(buff), 10, 64)
	return goroutineId
}

// 从池中分配通道任务
func NewChanTask(modName string, funName string, args []interface{}) *ChanTask {
	task := chanTaskPool.Get().(*ChanTask)
	task.Module = modName
	task.FunName = funName
	task.Args = args
	atomic.StoreInt32(&task.Failed, 0)
	return task
}

// 通道任务
type ChanTask struct {
	Module  string          // 模块名称
	FunName string          // 函数名称
	Args    []interface{}   // 参数
	Result  []reflect.Value // 执行结果
	Err     error           // 错误
	Stack   []string        // 当前堆栈
	Failed  int32           // 状态

	sync.WaitGroup
}

// 释放通道任务
func (that *ChanTask) Free() {
	chanTaskPool.Put(that)
}
