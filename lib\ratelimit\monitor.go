package ratelimit

import (
	"encoding/json"
	"runtime"
	"sync"
	"time"
	"zone/lib/core"
)

// PerformanceMetrics 性能指标
type PerformanceMetrics struct {
	// 内存使用情况
	MemoryUsage struct {
		Alloc      uint64 `json:"alloc"`       // 当前分配的内存
		TotalAlloc uint64 `json:"total_alloc"` // 总分配的内存
		Sys        uint64 `json:"sys"`         // 系统内存
		NumGC      uint32 `json:"num_gc"`      // GC次数
	} `json:"memory_usage"`

	// 频率限制统计
	RateLimitStats struct {
		HTTPRequests struct {
			Total    int64 `json:"total"`    // 总请求数
			Allowed  int64 `json:"allowed"`  // 允许的请求数
			Rejected int64 `json:"rejected"` // 拒绝的请求数
		} `json:"http_requests"`

		WebSocketConnections struct {
			Total    int64 `json:"total"`    // 总连接数
			Allowed  int64 `json:"allowed"`  // 允许的连接数
			Rejected int64 `json:"rejected"` // 拒绝的连接数
		} `json:"websocket_connections"`

		ActiveIPs int `json:"active_ips"` // 活跃IP数量
	} `json:"rate_limit_stats"`

	// 时间戳
	Timestamp time.Time `json:"timestamp"`
}

// PerformanceMonitor 性能监控器
type PerformanceMonitor struct {
	metrics   *PerformanceMetrics
	mu        sync.RWMutex
	stopChan  chan struct{}
	interval  time.Duration
	isRunning bool
}

// NewPerformanceMonitor 创建新的性能监控器
func NewPerformanceMonitor(interval time.Duration) *PerformanceMonitor {
	return &PerformanceMonitor{
		metrics:   &PerformanceMetrics{},
		stopChan:  make(chan struct{}),
		interval:  interval,
		isRunning: false,
	}
}

// Start 启动性能监控
func (pm *PerformanceMonitor) Start() {
	pm.mu.Lock()
	defer pm.mu.Unlock()

	if pm.isRunning {
		return
	}

	pm.isRunning = true
	go pm.monitorLoop()
	core.LogInfo("性能监控器已启动，监控间隔:", pm.interval)
}

// Stop 停止性能监控
func (pm *PerformanceMonitor) Stop() {
	pm.mu.Lock()
	defer pm.mu.Unlock()

	if !pm.isRunning {
		return
	}

	pm.isRunning = false
	close(pm.stopChan)
	core.LogInfo("性能监控器已停止")
}

// monitorLoop 监控循环
func (pm *PerformanceMonitor) monitorLoop() {
	ticker := time.NewTicker(pm.interval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			pm.updateMetrics()
		case <-pm.stopChan:
			return
		}
	}
}

// updateMetrics 更新性能指标
func (pm *PerformanceMonitor) updateMetrics() {
	pm.mu.Lock()
	defer pm.mu.Unlock()

	// 更新内存使用情况
	var memStats runtime.MemStats
	runtime.ReadMemStats(&memStats)

	pm.metrics.MemoryUsage.Alloc = memStats.Alloc
	pm.metrics.MemoryUsage.TotalAlloc = memStats.TotalAlloc
	pm.metrics.MemoryUsage.Sys = memStats.Sys
	pm.metrics.MemoryUsage.NumGC = memStats.NumGC

	// 更新频率限制统计
	manager := GetGlobalRateLimiterManager()
	httpStats := manager.GetHTTPStats()

	if totalIPs, ok := httpStats["total_ips"].(int); ok {
		pm.metrics.RateLimitStats.ActiveIPs = totalIPs
	}

	// 更新时间戳
	pm.metrics.Timestamp = time.Now()

	// 记录性能指标（可选）
	if pm.shouldLogMetrics() {
		pm.logMetrics()
	}
}

// shouldLogMetrics 判断是否应该记录指标
func (pm *PerformanceMonitor) shouldLogMetrics() bool {
	// 每分钟记录一次
	return pm.metrics.Timestamp.Second() == 0
}

// logMetrics 记录性能指标
func (pm *PerformanceMonitor) logMetrics() {
	core.LogInfo("性能指标",
		"内存使用:", pm.formatBytes(pm.metrics.MemoryUsage.Alloc),
		"系统内存:", pm.formatBytes(pm.metrics.MemoryUsage.Sys),
		"GC次数:", pm.metrics.MemoryUsage.NumGC,
		"活跃IP数:", pm.metrics.RateLimitStats.ActiveIPs,
	)
}

// formatBytes 格式化字节数
func (pm *PerformanceMonitor) formatBytes(bytes uint64) string {
	const unit = 1024
	if bytes < unit {
		return "0B"
	}
	div, exp := int64(unit), 0
	for n := bytes / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}
	units := []string{"B", "KB", "MB", "GB", "TB", "PB", "EB"}
	if exp >= len(units) {
		exp = len(units) - 1
	}
	return string(rune(bytes/uint64(div))) + units[exp]
}

// GetMetrics 获取当前性能指标
func (pm *PerformanceMonitor) GetMetrics() *PerformanceMetrics {
	pm.mu.RLock()
	defer pm.mu.RUnlock()

	// 创建副本以避免并发访问问题
	metricsCopy := *pm.metrics
	return &metricsCopy
}

// GetMetricsJSON 获取JSON格式的性能指标
func (pm *PerformanceMonitor) GetMetricsJSON() ([]byte, error) {
	metrics := pm.GetMetrics()
	return json.Marshal(metrics)
}

// MemoryOptimizer 内存优化器
type MemoryOptimizer struct {
	gcThreshold uint64        // GC触发阈值（字节）
	gcInterval  time.Duration // 强制GC间隔
	lastGC      time.Time     // 上次GC时间
	stopChan    chan struct{}
	isRunning   bool
	mu          sync.Mutex
}

// NewMemoryOptimizer 创建新的内存优化器
func NewMemoryOptimizer(gcThreshold uint64, gcInterval time.Duration) *MemoryOptimizer {
	return &MemoryOptimizer{
		gcThreshold: gcThreshold,
		gcInterval:  gcInterval,
		lastGC:      time.Now(),
		stopChan:    make(chan struct{}),
		isRunning:   false,
	}
}

// Start 启动内存优化器
func (mo *MemoryOptimizer) Start() {
	mo.mu.Lock()
	defer mo.mu.Unlock()

	if mo.isRunning {
		return
	}

	mo.isRunning = true
	go mo.optimizeLoop()
	core.LogInfo("内存优化器已启动",
		"GC阈值:", mo.formatBytes(mo.gcThreshold),
		"GC间隔:", mo.gcInterval)
}

// Stop 停止内存优化器
func (mo *MemoryOptimizer) Stop() {
	mo.mu.Lock()
	defer mo.mu.Unlock()

	if !mo.isRunning {
		return
	}

	mo.isRunning = false
	close(mo.stopChan)
	core.LogInfo("内存优化器已停止")
}

// optimizeLoop 优化循环
func (mo *MemoryOptimizer) optimizeLoop() {
	ticker := time.NewTicker(30 * time.Second) // 每30秒检查一次
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			mo.checkAndOptimize()
		case <-mo.stopChan:
			return
		}
	}
}

// checkAndOptimize 检查并优化内存
func (mo *MemoryOptimizer) checkAndOptimize() {
	var memStats runtime.MemStats
	runtime.ReadMemStats(&memStats)

	// 检查是否需要强制GC
	shouldGC := false

	// 条件1：内存使用超过阈值
	if memStats.Alloc > mo.gcThreshold {
		shouldGC = true
		core.LogDebug("内存使用超过阈值，触发GC", "当前:", mo.formatBytes(memStats.Alloc), "阈值:", mo.formatBytes(mo.gcThreshold))
	}

	// 条件2：距离上次GC时间过长
	if time.Since(mo.lastGC) > mo.gcInterval {
		shouldGC = true
		core.LogDebug("距离上次GC时间过长，触发GC", "间隔:", time.Since(mo.lastGC))
	}

	if shouldGC {
		mo.performGC()
	}
}

// performGC 执行垃圾回收
func (mo *MemoryOptimizer) performGC() {
	start := time.Now()
	var beforeStats, afterStats runtime.MemStats

	runtime.ReadMemStats(&beforeStats)
	runtime.GC()
	runtime.ReadMemStats(&afterStats)

	mo.lastGC = time.Now()
	duration := time.Since(start)

	core.LogDebug("GC完成",
		"耗时:", duration,
		"回收前:", (beforeStats.Alloc / 1024),
		"回收后:", (afterStats.Alloc / 1024),
		"回收量:", (beforeStats.Alloc-afterStats.Alloc)/1024)
}

// formatBytes 格式化字节数
func (mo *MemoryOptimizer) formatBytes(bytes uint64) string {
	const unit = 1024
	if bytes < unit {
		return "0B"
	}
	div, exp := int64(unit), 0
	for n := bytes / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}
	units := []string{"B", "KB", "MB", "GB", "TB", "PB", "EB"}
	if exp >= len(units) {
		exp = len(units) - 1
	}
	return string(rune(bytes/uint64(div))) + units[exp]
}

// 全局实例
var (
	globalPerformanceMonitor *PerformanceMonitor
	globalMemoryOptimizer    *MemoryOptimizer
	monitorOnce              sync.Once
	optimizerOnce            sync.Once
)

// GetGlobalPerformanceMonitor 获取全局性能监控器
func GetGlobalPerformanceMonitor() *PerformanceMonitor {
	monitorOnce.Do(func() {
		globalPerformanceMonitor = NewPerformanceMonitor(10 * time.Second)
	})
	return globalPerformanceMonitor
}

// GetGlobalMemoryOptimizer 获取全局内存优化器
func GetGlobalMemoryOptimizer() *MemoryOptimizer {
	optimizerOnce.Do(func() {
		// 默认阈值：100MB，间隔：5分钟
		globalMemoryOptimizer = NewMemoryOptimizer(100*1024*1024, 5*time.Minute)
	})
	return globalMemoryOptimizer
}
