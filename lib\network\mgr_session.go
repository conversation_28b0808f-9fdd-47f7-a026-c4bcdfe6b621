package network

import (
	"net/http"
	"sync"
	"time"
	"zone/lib/core"
	"zone/lib/utils"

	"github.com/gorilla/websocket"
)

const (
	// 通道缓冲区大小配置
	sendChanSize  = 2000 // 发送通道缓冲区大小
	recvChanSize  = 2000 // 接收通道缓冲区大小
	socketTimeOut = 10   // WebSocket操作超时时间（秒）
	socketTryNum  = 10   // 网络发送重试次数

	// 广播消息配置
	broadcastMaxBatch  = 10   // 单次广播处理的最大消息数量
	broadcastQueueSize = 5000 // 广播消息队列大小
)

// sessionMap 会话映射表类型定义
type sessionMap map[int64]*Session

// 兼容性变量
var sessionindex int64 = 0

// MultiGameSessionState 多人游戏会话状态
type MultiGameSessionState struct {
	IsInMultiGame    bool   // 是否在多人游戏中
	GameID           string // 游戏ID
	RoomID           int    // 房间ID
	PlayerRole       int    // 玩家角色：0-普通玩家，1-房主
	GameStatus       int    // 游戏状态：0-等待，1-游戏中，2-已结束
	JoinTime         int64  // 加入时间
	LastActivityTime int64  // 最后活动时间
}

// SessionMgr 会话管理器，负责管理所有WebSocket会话的生命周期
type SessionMgr struct {
	MapSession        sessionMap    // 活跃会话映射表（保持兼容性）
	AddSession        chan *Session // 新增会话通道（保持兼容性）
	DelSession        chan *Session // 移除会话通道（保持兼容性）
	Lock              *sync.RWMutex // 会话映射表读写锁（保持兼容性）
	RemoveLock        *sync.RWMutex // 移除会话列表读写锁（保持兼容性）
	RemoveSessionList []*Session    // 待清理的会话列表（保持兼容性）
	Shutdown          bool          // 系统关闭标志（保持兼容性）
	BroadcastMsgArr   chan []byte   // 广播消息队列（保持兼容性）

	// 新增：多人游戏状态管理
	MultiGameStates *utils.SafeMapInt // map[sessionId]*MultiGameSessionState
}

// 会话管理器单例实例
var sessionManagerInstance *SessionMgr

// GetSessionMgr 获取会话管理器单例实例
func GetSessionMgr() *SessionMgr {
	if sessionManagerInstance == nil {
		sessionManagerInstance = &SessionMgr{
			MapSession:        make(sessionMap),
			Lock:              &sync.RWMutex{},
			RemoveLock:        &sync.RWMutex{},
			RemoveSessionList: make([]*Session, 0),
			BroadcastMsgArr:   make(chan []byte, broadcastQueueSize),
			Shutdown:          false,

			// 初始化多人游戏状态管理
			MultiGameStates: utils.NewSafeMapInt(),
		}
	}
	return sessionManagerInstance
}

// Run 启动会话管理器的消息广播处理循环
func (sessionMgr *SessionMgr) Run() {
	core.LogInfo("会话管理器广播服务已启动")

	for message := range sessionMgr.BroadcastMsgArr {
		// 检查系统是否正在关闭
		if sessionMgr.Shutdown {
			core.LogInfo("检测到系统关闭信号，停止广播服务")
			break
		}

		core.LogDebug("处理广播消息，长度:", len(message))

		// // 编码消息
		// var messageBuffer bytes.Buffer
		// messageBuffer.Write(HF_DecodeMsg("1", message))
		// encodedMessage := messageBuffer.Bytes()

		// // 广播消息到所有活跃会话
		// sessionMgr.broadcastToAllSessions(encodedMessage)
	}

	core.LogInfo("会话管理器广播服务已停止")
}

// broadcastToAllSessions 向所有活跃会话广播消息
func (sessionMgr *SessionMgr) broadcastToAllSessions(message []byte) {
	sessionMgr.Lock.RLock()
	defer sessionMgr.Lock.RUnlock()

	broadcastCount := 0
	for _, session := range sessionMgr.MapSession {
		// TODO: 添加玩家对象检查
		// if utils.IsNil(session.PlayerObj) {
		//     continue
		// }
		session.SendMsgBatch(message)
		broadcastCount++
	}

	core.LogDebug("广播消息完成，发送到会话数量:", broadcastCount)
}

// GetNewSession 创建新的WebSocket会话
// websocketConn: WebSocket连接对象
// request: HTTP请求对象
// 返回值: 新创建的会话对象，如果创建失败返回nil
func (sessionMgr *SessionMgr) GetNewSession(websocketConn *websocket.Conn, request *http.Request) *Session {
	// TODO: 添加连接数量限制检查
	// if len(sessionMgr.AddSession) >= maxSessionLimit {
	//     return nil
	// }

	sessionMgr.Lock.Lock()
	defer sessionMgr.Lock.Unlock()

	// 生成新的会话ID
	sessionindex++
	newSessionID := sessionindex

	// 创建新会话对象
	session := &Session{
		ID:       newSessionID,
		Ws:       websocketConn,
		RoomID:   -1, // 初始状态未分配房间
		SendChan: make(chan []byte, sendChanSize),
		RecvChan: make(chan []byte, recvChanSize),
		ShutDown: false,
		IP:       utils.HF_GetHttpIP(request),
		MsgMaxId: -1,
		// 初始化新增的连接状态管理字段
		isConnected:      false, // 初始为false，在Run()中设置为true
		readErrCount:     0,
		readTimeoutCount: 0,
		lastReadTime:     time.Now(),
	}

	// 将会话添加到管理器
	sessionMgr.MapSession[newSessionID] = session

	core.LogDebug("创建新会话，ID:", newSessionID, "客户端IP:", session.IP)
	return session
}

// RemoveSession 从管理器中移除会话
// session: 要移除的会话对象
func (sessionMgr *SessionMgr) RemoveSession(session *Session) {
	// 从活跃会话映射表中移除
	sessionMgr.Lock.Lock()
	delete(sessionMgr.MapSession, session.ID)
	sessionMgr.Lock.Unlock()

	// 添加到待清理列表
	sessionMgr.RemoveLock.Lock()
	sessionMgr.RemoveSessionList = append(sessionMgr.RemoveSessionList, session)
	sessionMgr.RemoveLock.Unlock()

	core.LogDebug("会话已移除，ID:", session.ID)
}

// ClearRemoveSession 集中清理已移除的会话资源
// 定期调用此方法来释放已关闭会话的资源
func (sessionMgr *SessionMgr) ClearRemoveSession() {
	sessionMgr.RemoveLock.Lock()
	defer sessionMgr.RemoveLock.Unlock()

	waitingCloseCount := 0
	currentTime := time.Now().Unix()

	// 遍历待清理的会话列表
	for i := 0; i < len(sessionMgr.RemoveSessionList); i++ {
		session := sessionMgr.RemoveSessionList[i]

		// 检查会话是否可以清理（已关闭且超过关闭时间）
		if session.ShutDown &&
			session.SendChan != nil &&
			currentTime > session.ShutTime {

			// 安全关闭发送通道
			sessionMgr.safeCloseChannel(session)
		} else {
			waitingCloseCount++
		}
	}

	// 如果所有会话都已清理完成，重置列表
	if waitingCloseCount == 0 && len(sessionMgr.RemoveSessionList) > 0 {
		sessionMgr.RemoveSessionList = make([]*Session, 0)
		core.LogDebug("所有待清理会话已处理完成")
	}
}

// safeCloseChannel 安全关闭会话的通道
func (sessionMgr *SessionMgr) safeCloseChannel(session *Session) {
	// 关闭发送通道
	if session.SendChan != nil {
		close(session.SendChan)
		session.SendChan = nil
	}

	// 安全关闭接收通道
	if session.RecvChan != nil {
		func() {
			defer func() {
				if panicInfo := recover(); panicInfo != nil {
					core.LogDebug("接收通道已关闭，忽略panic:", panicInfo)
				}
			}()
			close(session.RecvChan)
		}()
		session.RecvChan = nil
	}

	core.LogDebug("会话通道已安全关闭，ID:", session.ID)
}

// BroadCastMsg 添加消息到广播队列
// head: 消息头部标识
// body: 消息体数据
func (sessionMgr *SessionMgr) BroadCastMsg(head string, body []byte) {
	// 检查广播队列是否已满
	if len(sessionMgr.BroadcastMsgArr) >= broadcastQueueSize {
		core.LogError("广播队列已满，丢弃消息")
		return
	}

	// 将消息添加到广播队列
	sessionMgr.BroadcastMsgArr <- body
	core.LogDebug("消息已添加到广播队列，队列长度:", len(sessionMgr.BroadcastMsgArr))
}

// OrderBroadCastMsg 立即广播消息（预留方法）
// head: 消息头部标识
// body: 消息体数据
func (sessionMgr *SessionMgr) OrderBroadCastMsg(head string, body []byte) {
	// TODO: 实现立即广播逻辑
	// sessionMgr.Lock.RLock()
	// defer sessionMgr.Lock.RUnlock()
	// for _, session := range sessionMgr.MapSession {
	//     session.SendMsg(head, body)
	// }
	core.LogDebug("立即广播消息功能暂未实现")
}

// GetSessionNum 获取当前活跃会话数量
// 返回值: 活跃会话的数量
func (sessionMgr *SessionMgr) GetSessionNum() int {
	sessionMgr.Lock.RLock()
	defer sessionMgr.Lock.RUnlock()

	sessionCount := len(sessionMgr.MapSession)
	core.LogDebug("当前活跃会话数量:", sessionCount)
	return sessionCount
}

// ==================== 多人游戏状态管理方法 ====================

// SetMultiGameState 设置会话的多人游戏状态
func (sessionMgr *SessionMgr) SetMultiGameState(sessionID int64, gameID string, roomID int, playerRole int) {
	state := &MultiGameSessionState{
		IsInMultiGame:    true,
		GameID:           gameID,
		RoomID:           roomID,
		PlayerRole:       playerRole,
		GameStatus:       0, // 等待状态
		JoinTime:         time.Now().Unix(),
		LastActivityTime: time.Now().Unix(),
	}

	sessionMgr.MultiGameStates.Store(int(sessionID), state)
	core.LogDebug("设置多人游戏状态", "SessionID:", sessionID, "GameID:", gameID, "RoomID:", roomID)
}

// GetMultiGameState 获取会话的多人游戏状态
func (sessionMgr *SessionMgr) GetMultiGameState(sessionID int64) (*MultiGameSessionState, bool) {
	if state, exists := sessionMgr.MultiGameStates.Load(int(sessionID)); exists {
		return state.(*MultiGameSessionState), true
	}
	return nil, false
}

// UpdateMultiGameStatus 更新会话的多人游戏状态
func (sessionMgr *SessionMgr) UpdateMultiGameStatus(sessionID int64, gameStatus int) {
	if state, exists := sessionMgr.MultiGameStates.Load(int(sessionID)); exists {
		gameState := state.(*MultiGameSessionState)
		gameState.GameStatus = gameStatus
		gameState.LastActivityTime = time.Now().Unix()
		sessionMgr.MultiGameStates.Store(int(sessionID), gameState)
		core.LogDebug("更新多人游戏状态", "SessionID:", sessionID, "Status:", gameStatus)
	}
}

// UpdateMultiGameActivity 更新会话的多人游戏活动时间
func (sessionMgr *SessionMgr) UpdateMultiGameActivity(sessionID int64) {
	if state, exists := sessionMgr.MultiGameStates.Load(int(sessionID)); exists {
		gameState := state.(*MultiGameSessionState)
		gameState.LastActivityTime = time.Now().Unix()
		sessionMgr.MultiGameStates.Store(int(sessionID), gameState)
	}
}

// RemoveMultiGameState 移除会话的多人游戏状态
func (sessionMgr *SessionMgr) RemoveMultiGameState(sessionID int64) {
	sessionMgr.MultiGameStates.Delete(int(sessionID))
	core.LogDebug("移除多人游戏状态", "SessionID:", sessionID)
}

// IsInMultiGame 检查会话是否在多人游戏中
func (sessionMgr *SessionMgr) IsInMultiGame(sessionID int64) bool {
	if state, exists := sessionMgr.GetMultiGameState(sessionID); exists {
		return state.IsInMultiGame
	}
	return false
}

// GetMultiGameSessionsByGameID 获取指定游戏ID的所有会话
func (sessionMgr *SessionMgr) GetMultiGameSessionsByGameID(gameID string) []*Session {
	var sessions []*Session

	sessionMgr.MultiGameStates.Range(func(key int, value interface{}) bool {
		state := value.(*MultiGameSessionState)
		if state.GameID == gameID && state.IsInMultiGame {
			if session, exists := sessionMgr.GetSession(int64(key)); exists {
				sessions = append(sessions, session)
			}
		}
		return true
	})

	return sessions
}

// GetSession 根据会话ID获取会话对象
func (sessionMgr *SessionMgr) GetSession(sessionID int64) (*Session, bool) {
	sessionMgr.Lock.RLock()
	defer sessionMgr.Lock.RUnlock()

	if session, exists := sessionMgr.MapSession[sessionID]; exists {
		return session, true
	}
	return nil, false
}

// BroadcastToMultiGame 向指定多人游戏的所有会话广播消息
func (sessionMgr *SessionMgr) BroadcastToMultiGame(gameID string, messageData []byte) int {
	sessions := sessionMgr.GetMultiGameSessionsByGameID(gameID)
	broadcastCount := 0

	for _, session := range sessions {
		if session.IsConnected() {
			session.SendPBMsg(messageData)
			broadcastCount++
		}
	}

	core.LogDebug("向多人游戏广播消息", "GameID:", gameID, "BroadcastCount:", broadcastCount)
	return broadcastCount
}

// GetMultiGameStatistics 获取多人游戏统计信息
func (sessionMgr *SessionMgr) GetMultiGameStatistics() map[string]interface{} {
	var totalInGame, waitingCount, playingCount, finishedCount int
	var gameIDs []string
	gameIDMap := make(map[string]bool)

	sessionMgr.MultiGameStates.Range(func(key int, value interface{}) bool {
		state := value.(*MultiGameSessionState)
		if state.IsInMultiGame {
			totalInGame++

			// 统计游戏状态
			switch state.GameStatus {
			case 0:
				waitingCount++
			case 1:
				playingCount++
			case 2:
				finishedCount++
			}

			// 收集唯一的游戏ID
			if !gameIDMap[state.GameID] {
				gameIDMap[state.GameID] = true
				gameIDs = append(gameIDs, state.GameID)
			}
		}
		return true
	})

	return map[string]interface{}{
		"totalInGame":   totalInGame,
		"waitingCount":  waitingCount,
		"playingCount":  playingCount,
		"finishedCount": finishedCount,
		"activeGames":   len(gameIDs),
		"gameIDs":       gameIDs,
	}
}

// CleanupInactiveMultiGameStates 清理不活跃的多人游戏状态
func (sessionMgr *SessionMgr) CleanupInactiveMultiGameStates(maxInactiveTime int64) {
	currentTime := time.Now().Unix()
	var inactiveSessionIDs []int

	sessionMgr.MultiGameStates.Range(func(key int, value interface{}) bool {
		state := value.(*MultiGameSessionState)

		// 检查是否长时间不活跃
		if currentTime-state.LastActivityTime > maxInactiveTime {
			inactiveSessionIDs = append(inactiveSessionIDs, key)
		}

		// 检查对应的会话是否还存在
		if _, exists := sessionMgr.GetSession(int64(key)); !exists {
			inactiveSessionIDs = append(inactiveSessionIDs, key)
		}

		return true
	})

	// 移除不活跃的状态
	for _, sessionID := range inactiveSessionIDs {
		sessionMgr.MultiGameStates.Delete(sessionID)
	}

	if len(inactiveSessionIDs) > 0 {
		core.LogInfo("清理不活跃的多人游戏状态", "Count:", len(inactiveSessionIDs))
	}
}
