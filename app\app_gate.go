package app

import (
	"log"
	"net"
	"net/http"
	"runtime/debug"
	"strings"
	"zone/game/mods"
	"zone/lib/core"
	"zone/lib/network"
	"zone/lib/ratelimit"
	"zone/pb/MessageHandle"

	"github.com/gorilla/websocket"
)

const (
	// VersionOK 版本检查结果：版本正常
	VersionOK = 0
	// VersionTooLow 版本检查结果：版本过低，需要更新
	VersionTooLow = 1
	// AppVersionTooLow 版本检查结果：应用版本过低
	AppVersionTooLow = 2

	// MajorVersionDivisor 主版本号除数
	MajorVersionDivisor = 1000000
)

// GateApp 网关应用程序，负责WebSocket连接管理和消息路由
type GateApp struct {
	// 预留字段：SDK处理器映射
	// sdkHandlers map[string]func(string, int, string, string) (*core.San_Account, string)

	// 流量优化相关
	connectionPool *network.ConnectionPool // 连接池管理器
}

// 网关应用单例实例
var gateAppInstance *GateApp

// GetGateApp 获取网关应用单例实例
func GetGateApp() *GateApp {
	if gateAppInstance == nil {
		gateAppInstance = &GateApp{
			connectionPool: network.NewConnectionPool(1000), // 最大1000个连接
		}
		gateAppInstance.Init()
		// 启动连接池管理
		gateAppInstance.connectionPool.Start()
		core.GateApp = gateAppInstance
	}
	return gateAppInstance
}

// GetConnectionPool 获取连接池管理器
func (gateApp *GateApp) GetConnectionPool() *network.ConnectionPool {
	return gateApp.connectionPool
}

// websocketUpgrader WebSocket连接升级器配置
var websocketUpgrader = websocket.Upgrader{
	CheckOrigin: func(request *http.Request) bool {
		// TODO: 生产环境应该实现更严格的来源检查
		return true
	},
	// 优化缓冲区大小以减少内存使用和提高性能
	ReadBufferSize:  2048, // 适当增加读缓冲区
	WriteBufferSize: 2048, // 适当增加写缓冲区
	// 启用压缩以减少网络流量
	EnableCompression: true,
	// 自定义错误处理函数，防止在已劫持连接上写入响应
	Error: func(w http.ResponseWriter, r *http.Request, status int, reason error) {
		// 使用defer和recover来捕获可能的panic
		defer func() {
			if panicInfo := recover(); panicInfo != nil {
				core.LogError("WebSocket升级错误处理时发生panic:", panicInfo)
			}
		}()

		// 检查响应是否已经被写入
		if ginWriter, ok := w.(interface{ Written() bool }); ok && ginWriter.Written() {
			core.LogDebug("响应已被写入，跳过WebSocket升级错误响应")
			return
		}

		// 安全地写入错误响应
		core.LogError("WebSocket升级失败:", reason, "状态码:", status)
		w.Header().Set("Sec-Websocket-Version", "13")
		w.WriteHeader(status)
		w.Write([]byte(reason.Error()))
	},
}

// GetConnectHandler 获取WebSocket连接处理器
// 返回一个HTTP处理函数，用于处理WebSocket升级请求
func (gateApp *GateApp) GetConnectHandler() http.HandlerFunc {
	return func(responseWriter http.ResponseWriter, request *http.Request) {
		// 检查服务器是否正在关闭
		if core.GetZoneApp().IsClosed() {
			core.LogDebug("服务器正在关闭，拒绝新的WebSocket连接")
			gateApp.safeWriteErrorResponse(responseWriter, http.StatusServiceUnavailable, "Server is shutting down")
			return
		}

		// 获取客户端IP地址（改进的IP获取逻辑）
		clientIP := gateApp.getRealClientIP(request)

		// 频率限制检查
		rateLimitManager := ratelimit.GetGlobalRateLimiterManager()
		if !rateLimitManager.CheckWebSocketRateLimit(clientIP) {
			core.LogDebug("WebSocket连接频率限制触发，IP:", clientIP)
			gateApp.safeWriteErrorResponse(responseWriter, http.StatusTooManyRequests, "WebSocket connection rate limit exceeded")
			return
		}

		// 检查连接池是否可以接受新连接
		if !gateApp.connectionPool.CanAcceptConnection(clientIP) {
			core.LogDebug("连接池拒绝新连接，IP:", clientIP)
			gateApp.safeWriteErrorResponse(responseWriter, http.StatusTooManyRequests, "Too many connections")
			return
		}

		// 简化的连接日志
		core.LogDebug("WebSocket连接请求 - 来源:", clientIP)

		// 升级HTTP连接为WebSocket连接
		websocketConnection, err := websocketUpgrader.Upgrade(responseWriter, request, nil)
		if err != nil {
			core.LogError("WebSocket连接升级失败:", err)
			// 只在升级失败时记录关键头部信息
			core.LogError("Connection:", request.Header.Get("Connection"))
			core.LogError("Upgrade:", request.Header.Get("Upgrade"))
			// 注意：升级失败时，gorilla/websocket已经处理了HTTP响应，不需要再写入
			return
		}

		// 创建新的会话
		session := network.GetSessionMgr().GetNewSession(websocketConnection, request)
		if session == nil {
			core.LogError("创建会话失败，关闭WebSocket连接")
			websocketConnection.Close()
			return
		}

		// 设置会话IP地址
		session.IP = clientIP

		// 将会话添加到连接池
		if !gateApp.connectionPool.AddConnection(session, clientIP) {
			core.LogError("连接池添加会话失败，会话ID:", session.ID)
			session.CloseChan()
			websocketConnection.Close()
			return
		}

		core.LogDebug("新会话已创建，会话ID:", session.ID, "来源:", clientIP)

		// 设置会话事件处理器
		session.SetOnMessage(gateApp.OnRecv)

		// 设置会话关闭回调，确保从连接池中移除
		originalOnClose := gateApp.OnCloseSession
		session.SetOnClose(func(s *network.Session) {
			gateApp.connectionPool.RemoveConnection(s.ID, s.IP)
			if originalOnClose != nil {
				originalOnClose(s)
			}
		})

		// 启动会话处理循环
		session.Run()
	}
}

// Init 初始化网关应用程序
// 设置必要的管理器和处理器
func (gateApp *GateApp) Init() {
	// 直播信息管理器已经在GetLiveInfoMgr()中自动初始化，无需重复调用
	// mods.GetLiveInfoMgr().Init() // 移除重复初始化调用

	// 设置连接池的IP限制策略
	if gateApp.connectionPool != nil {
		// 可以根据需要调整这些值
		// gateApp.connectionPool.SetIPConnectionLimit("127.0.0.1", 50) // 本地测试允许更多连接
	}

	core.LogDebug("网关应用程序初始化完成")
}

// OnCloseSession 处理会话关闭事件
// session: 被关闭的会话对象
func (gateApp *GateApp) OnCloseSession(session *network.Session) {
	core.LogDebug("会话关闭，会话ID:", session.ID)
	// 通知会话房间管理器处理会话关闭
	mods.GetSessionRoomMgr().OnCloseSession(session)
}

// OnRecv 处理接收到的消息数据
// session: 发送消息的会话
// messageData: 接收到的消息字节数据
func (gateApp *GateApp) OnRecv(session *network.Session, messageData []byte) {
	// 使用defer和recover确保消息处理过程中的异常不会导致整个服务崩溃
	defer func() {
		if panicInfo := recover(); panicInfo != nil {
			stackTrace := string(debug.Stack())
			log.Printf("消息处理发生异常: %v\n堆栈信息:\n%s", panicInfo, stackTrace)
			core.LogError("消息处理异常:", panicInfo, "堆栈:", stackTrace)
		}
	}()

	// 检查会话是否已关闭
	if session.ShutDown {
		core.LogDebug("会话已关闭，忽略消息，会话ID:", session.ID)
		return
	}

	// 处理消息
	MessageHandle.HandleMessage(session, messageData)
}

// CheckVer 检查客户端版本是否兼容
// clientVersion: 客户端版本号
// 返回值: 0-版本正常, 1-版本过低需要更新, 2-应用版本过低
func (gateApp *GateApp) CheckVer(clientVersion int) int {
	serverConfig := core.GetZoneApp().GetConfig()
	serverVersion := serverConfig.ServerVer

	// 如果客户端版本为0或者版本号不低于服务器版本，则通过检查
	if clientVersion == 0 || clientVersion >= serverVersion {
		return VersionOK
	}

	// 检查主版本号是否不同（需要强制更新）
	clientMajorVersion := clientVersion / MajorVersionDivisor
	serverMajorVersion := serverVersion / MajorVersionDivisor

	if clientMajorVersion != serverMajorVersion {
		core.LogError("客户端主版本过低，需要强制更新。客户端版本:", clientVersion, "服务器版本:", serverVersion)
		return VersionTooLow
	}

	// 检查次版本号是否不同（应用版本过低）
	clientMinorVersion := clientVersion % MajorVersionDivisor
	serverMinorVersion := serverVersion % MajorVersionDivisor

	if clientMinorVersion != serverMinorVersion {
		core.LogError("客户端应用版本过低。客户端版本:", clientVersion, "服务器版本:", serverVersion)
		return AppVersionTooLow
	}

	// 其他情况下版本检查通过
	core.LogDebug("版本检查通过。客户端版本:", clientVersion, "服务器版本:", serverVersion)
	return VersionOK
}

// getRealClientIP 获取真实的客户端IP地址
// 考虑代理、负载均衡器等情况
func (gateApp *GateApp) getRealClientIP(request *http.Request) string {
	// 检查多个可能的头部字段，按优先级排序
	headers := []string{
		"CF-Connecting-IP",    // Cloudflare
		"True-Client-IP",      // Akamai
		"X-Real-IP",           // Nginx proxy_pass
		"X-Forwarded-For",     // 标准代理头
		"X-Client-IP",         // Apache mod_proxy
		"X-Cluster-Client-IP", // 集群环境
	}

	for _, header := range headers {
		if ip := request.Header.Get(header); ip != "" {
			// X-Forwarded-For 可能包含多个IP，取第一个
			if header == "X-Forwarded-For" {
				if forwardedFor := request.Header.Get("X-Forwarded-For"); forwardedFor != "" {
					// 取第一个IP地址
					if idx := strings.Index(forwardedFor, ","); idx > 0 {
						ip = strings.TrimSpace(forwardedFor[:idx])
					} else {
						ip = strings.TrimSpace(forwardedFor)
					}
				}
			}

			// 验证IP格式
			if validIP := gateApp.validateIP(ip); validIP != "" {
				return validIP
			}
		}
	}

	// 降级到RemoteAddr
	if host, _, err := net.SplitHostPort(request.RemoteAddr); err == nil {
		if validIP := gateApp.validateIP(host); validIP != "" {
			return validIP
		}
	}

	// 最后的降级选项
	return request.RemoteAddr
}

// validateIP 验证IP地址格式
func (gateApp *GateApp) validateIP(ip string) string {
	// 去除空格
	ip = strings.TrimSpace(ip)

	// 检查是否为空
	if ip == "" {
		return ""
	}

	// 解析IP地址
	parsedIP := net.ParseIP(ip)
	if parsedIP == nil {
		return ""
	}

	return ip
}

// safeWriteErrorResponse 安全地写入HTTP错误响应
// 这个方法确保在WebSocket升级过程中不会发生响应头写入冲突
func (gateApp *GateApp) safeWriteErrorResponse(w http.ResponseWriter, statusCode int, message string) {
	// 使用defer和recover来捕获可能的panic
	defer func() {
		if panicInfo := recover(); panicInfo != nil {
			core.LogError("写入HTTP错误响应时发生panic:", panicInfo, "这通常表示连接已被劫持")
		}
	}()

	// 多层安全检查
	if gateApp.isResponseWritten(w) {
		core.LogDebug("响应已被写入，跳过错误响应写入")
		return
	}

	if gateApp.isConnectionHijacked(w) {
		core.LogDebug("连接已被劫持，跳过错误响应写入")
		return
	}

	// 安全地写入响应头和内容
	w.Header().Set("Content-Type", "text/plain; charset=utf-8")
	w.Header().Set("Connection", "close") // 明确指示连接关闭
	w.WriteHeader(statusCode)
	w.Write([]byte(message))
}

// isResponseWritten 检查HTTP响应是否已经被写入
func (gateApp *GateApp) isResponseWritten(w http.ResponseWriter) bool {
	// 尝试类型断言来检查是否是Gin的ResponseWriter
	if ginWriter, ok := w.(interface{ Written() bool }); ok {
		return ginWriter.Written()
	}

	// 对于标准的http.ResponseWriter，我们无法直接检查
	// 但可以通过尝试设置头部来间接检查
	defer func() {
		recover() // 忽略可能的panic
	}()

	// 尝试设置一个测试头部，如果失败说明响应已被写入
	originalValue := w.Header().Get("X-Test-Header")
	w.Header().Set("X-Test-Header", "test")

	// 恢复原始值
	if originalValue == "" {
		w.Header().Del("X-Test-Header")
	} else {
		w.Header().Set("X-Test-Header", originalValue)
	}

	return false // 如果没有panic，说明还可以写入
}

// isConnectionHijacked 检查连接是否已被劫持
func (gateApp *GateApp) isConnectionHijacked(w http.ResponseWriter) bool {
	// 尝试获取Hijacker接口
	_, ok := w.(http.Hijacker)
	if !ok {
		return false // 不支持劫持，说明没有被劫持
	}

	// 使用更安全的方法检查连接状态
	// 不实际劫持连接，而是通过其他方式检查
	defer func() {
		if panicInfo := recover(); panicInfo != nil {
			core.LogDebug("检查连接劫持状态时发生panic，可能连接已被劫持:", panicInfo)
		}
	}()

	// 检查是否是Gin的ResponseWriter，如果是，可以检查其状态
	if ginWriter, ok := w.(interface{ Written() bool }); ok {
		// 如果已经写入响应，可能表示连接状态已改变
		return ginWriter.Written()
	}

	// 对于其他类型的ResponseWriter，使用保守的方法
	// 尝试设置一个无害的头部来测试连接状态
	originalValue := w.Header().Get("X-Connection-Test")
	w.Header().Set("X-Connection-Test", "test")

	// 立即恢复
	if originalValue == "" {
		w.Header().Del("X-Connection-Test")
	} else {
		w.Header().Set("X-Connection-Test", originalValue)
	}

	return false // 如果没有异常，假设连接正常
}
