package storage

import (
	"fmt"
	"sync"
	"zone/lib/core"
)

// ServerUrlService ServerUrl配置服务（简化版）
type ServerUrlService struct {
	manager     *ServerUrlConfigManager
	initialized bool
	mutex       sync.RWMutex
}

var (
	wsConfigService     *ServerUrlService
	wsConfigServiceOnce sync.Once
)

// GetServerUrlService 获取ServerUrl配置服务单例
func GetServerUrlService() *ServerUrlService {
	wsConfigServiceOnce.Do(func() {
		wsConfigService = &ServerUrlService{
			manager:     GetServerUrlConfigManager(),
			initialized: false,
		}
	})
	return wsConfigService
}

// Initialize 初始化配置服务
func (s *ServerUrlService) Initialize() error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if s.initialized {
		core.LogInfo("ServerUrl配置服务已经初始化")
		return nil
	}

	// core.LogInfo("开始初始化ServerUrl配置服务...")

	// 加载所有配置到缓存
	err := s.manager.LoadAllConfigsToCache()
	if err != nil {
		core.LogError("加载ServerUrl配置到缓存失败:", err)
		return err
	}

	s.initialized = true
	// core.LogInfo("ServerUrl配置服务初始化完成")

	return nil
}

// IsInitialized 检查服务是否已初始化
func (s *ServerUrlService) IsInitialized() bool {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	return s.initialized
}

func (s *ServerUrlService) GetConfig(platform int, isLocal bool) *ServerUrlConfig {
	// 确定平台ID
	platformID := s.getPlatformID(platform, isLocal)

	// 获取配置
	config, err := s.manager.GetConfigByPlatform(platformID)
	if err != nil {
		core.LogError(fmt.Sprintf("获取WebSocket URL失败: platform=%d, local=%t, error=%v", platform, isLocal, err))
		return nil
	}

	return config
}

// GetWebSocketURL 获取WebSocket地址
func (s *ServerUrlService) GetWebSocketURL(platform int, isLocal bool) string {
	// 确定平台ID
	platformID := s.getPlatformID(platform, isLocal)

	// 获取配置
	config, err := s.manager.GetConfigByPlatform(platformID)
	if err != nil {
		core.LogError(fmt.Sprintf("获取WebSocket URL失败: platform=%d, local=%t, error=%v", platform, isLocal, err))
		return ""
	}

	return config.WebSocketURL
}

// GetHTTPURL 获取HTTP地址
func (s *ServerUrlService) GetHTTPURL(platform int, isLocal bool) string {
	// 确定平台ID
	platformID := s.getPlatformID(platform, isLocal)

	// 获取配置
	config, err := s.manager.GetConfigByPlatform(platformID)
	if err != nil {
		core.LogError(fmt.Sprintf("获取HTTP URL失败: platform=%d, local=%t, error=%v", platform, isLocal, err))
		return ""
	}

	return config.HttpURL
}

// GetUseEncrypt 获取加密开关配置
func (s *ServerUrlService) GetUseEncrypt(isLocal bool) bool {
	// 本地环境通常不加密，生产环境加密
	if isLocal {
		// 获取本地环境配置
		config, err := s.manager.GetConfigByPlatform(99)
		if err != nil {
			core.LogError(fmt.Sprintf("获取本地环境加密配置失败: %v", err))
			return false // 本地环境默认不加密
		}
		return config.UseEncrypt
	}

	// 生产环境，尝试获取抖音平台配置作为默认
	config, err := s.manager.GetConfigByPlatform(0)
	if err != nil {
		core.LogError(fmt.Sprintf("获取生产环境加密配置失败: %v", err))
		return true // 生产环境默认加密
	}

	return config.UseEncrypt
}

// getPlatformID 根据平台和本地标志确定平台ID
func (s *ServerUrlService) getPlatformID(platform int, isLocal bool) int {
	if isLocal {
		return 99 // 本地开发环境
	}
	return platform // 使用传入的平台ID
}

// RefreshCache 刷新缓存
func (s *ServerUrlService) RefreshCache() error {
	core.LogInfo("开始刷新ServerUrl配置缓存...")

	// 清除管理器缓存
	s.manager.ClearAllCache()

	// 重新加载到管理器缓存
	err := s.manager.LoadAllConfigsToCache()
	if err != nil {
		core.LogError(fmt.Sprintf("重新加载配置到管理器缓存失败: %v", err))
		return err
	}

	core.LogInfo("ServerUrl配置缓存刷新完成")
	return nil
}

// GetServiceStats 获取服务统计信息
func (s *ServerUrlService) GetServiceStats() map[string]interface{} {
	stats := make(map[string]interface{})

	s.mutex.RLock()
	stats["initialized"] = s.initialized
	s.mutex.RUnlock()

	// 获取管理器统计信息
	managerStats := s.manager.GetCacheStats()
	for k, v := range managerStats {
		stats["manager_"+k] = v
	}

	return stats
}

// GetAllConfigs 获取所有配置（用于API接口）
func (s *ServerUrlService) GetAllConfigs() ([]*ServerUrlConfig, error) {
	return s.manager.GetAllConfigs()
}

// GetConfigByPlatform 根据平台ID获取配置（用于API接口）
func (s *ServerUrlService) GetConfigByPlatform(platformID int) (*ServerUrlConfig, error) {
	return s.manager.GetConfigByPlatform(platformID)
}

// CreateConfig 创建配置（用于API接口）
func (s *ServerUrlService) CreateConfig(config *ServerUrlConfig) error {
	return s.manager.CreateConfig(config)
}

// UpdateConfig 更新配置（用于API接口）
func (s *ServerUrlService) UpdateConfig(config *ServerUrlConfig) error {
	return s.manager.UpdateConfig(config)
}

// DeleteConfig 删除配置（用于API接口）
func (s *ServerUrlService) DeleteConfig(platformID int) error {
	return s.manager.DeleteConfig(platformID)
}
