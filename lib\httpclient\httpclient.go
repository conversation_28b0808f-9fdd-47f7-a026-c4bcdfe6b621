package httpclient

import (
	"bytes"
	"context"
	"crypto/rand"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"sync"
	"time"
	"zone/lib/core"
	"zone/lib/utils"
)

// HTTP客户端配置
const (
	DefaultTimeout      = 30 * time.Second
	DefaultKeepAlive    = 30 * time.Second
	MaxIdleConns        = 100
	MaxIdleConnsPerHost = 10
	IdleConnTimeout     = 90 * time.Second
)

// 请求头常量
const (
	HeaderContentType     = "Content-Type"
	HeaderApplicationJSON = "application/json;charset=UTF-8"
	HeaderXToken          = "X-Token"
	HeaderAccessToken     = "access-token"
	HeaderByteAuth        = "Byte-Authorization"
)

// HTTPClient 单例HTTP客户端管理器
type HTTPClient struct {
	client *http.Client
	once   sync.Once
}

var httpClientInstance *HTTPClient

// GetHTTPClient 获取单例HTTP客户端实例
func GetHTTPClient() *http.Client {
	if httpClientInstance == nil {
		httpClientInstance = &HTTPClient{}
	}

	httpClientInstance.once.Do(func() {
		transport := &http.Transport{
			MaxIdleConns:        MaxIdleConns,
			MaxIdleConnsPerHost: MaxIdleConnsPerHost,
			IdleConnTimeout:     IdleConnTimeout,
		}

		httpClientInstance.client = &http.Client{
			Timeout:   DefaultTimeout,
			Transport: transport,
		}
	})

	return httpClientInstance.client
}

// HTTPRequestOptions HTTP请求选项
type HTTPRequestOptions struct {
	Method  string
	URL     string
	Body    io.Reader
	Headers map[string]string
	Timeout time.Duration
}

// DoHTTPRequest 执行HTTP请求的通用函数
// 统一处理HTTP请求、错误处理和响应读取
func DoHTTPRequest(opts HTTPRequestOptions) ([]byte, error) {
	client := GetHTTPClient()

	// 如果指定了超时时间，创建带超时的上下文
	var req *http.Request
	var err error

	if opts.Timeout > 0 {
		ctx, cancel := context.WithTimeout(context.Background(), opts.Timeout)
		defer cancel()
		req, err = http.NewRequestWithContext(ctx, opts.Method, opts.URL, opts.Body)
	} else {
		req, err = http.NewRequest(opts.Method, opts.URL, opts.Body)
	}

	if err != nil {
		core.LogError("创建HTTP请求失败:", err)
		return nil, err
	}

	// 设置请求头
	for key, value := range opts.Headers {
		req.Header.Set(key, value)
	}

	// 执行请求
	resp, err := client.Do(req)
	if err != nil {
		core.LogError("执行HTTP请求失败:", err)
		return nil, err
	}
	defer resp.Body.Close()

	// 读取响应体
	bodyData, err := io.ReadAll(resp.Body)
	if err != nil {
		core.LogError("读取响应体失败:", err)
		return nil, err
	}

	return bodyData, nil
}

// CreateJSONRequest 创建JSON请求的便捷函数
func CreateJSONRequest(method, url string, data interface{}) (HTTPRequestOptions, error) {
	var body io.Reader
	headers := map[string]string{
		HeaderContentType: HeaderApplicationJSON,
	}
	if data != nil {
		jsonData := utils.HF_JtoB(data)
		body = bytes.NewBuffer(jsonData)
		// core.LogInfo(fmt.Sprintf("创建%s请求:", method), url, "数据:", string(jsonData), "请求头:", headers)
	}

	return HTTPRequestOptions{
		Method:  method,
		URL:     url,
		Body:    body,
		Headers: headers,
	}, nil
}

// GenerateSecureRandomString 生成安全的随机字符串
// 使用crypto/rand替代math/rand以提高安全性
func GenerateSecureRandomString() string {
	b := make([]byte, 16)
	if _, err := rand.Read(b); err != nil {
		core.LogError("生成随机字符串失败:", err)
		// 如果加密随机数生成失败，返回基于时间的字符串作为后备
		return fmt.Sprintf("%d", time.Now().UnixNano())
	}
	return hex.EncodeToString(b)
}

// ParseJSONResponse 解析JSON响应的通用函数
func ParseJSONResponse(data []byte, result interface{}) error {
	err := json.Unmarshal(data, result)
	if err != nil {
		core.LogError("JSON解析失败:", err, "数据:", string(data))
		return fmt.Errorf("JSON解析失败: %w", err)
	}
	return nil
}

// CreateAuthHeaders 创建带认证信息的请求头
func CreateAuthHeaders(accessToken string) map[string]string {
	return map[string]string{
		HeaderContentType: HeaderApplicationJSON,
		HeaderAccessToken: accessToken,
	}
}

// CreateXTokenHeaders 创建带X-Token的请求头
func CreateXTokenHeaders(accessToken string) map[string]string {
	return map[string]string{
		HeaderContentType: HeaderApplicationJSON,
		HeaderXToken:      accessToken,
	}
}

// CreateByteAuthHeaders 创建带Byte-Authorization的请求头
func CreateByteAuthHeaders(authorization string) map[string]string {
	return map[string]string{
		HeaderContentType: HeaderApplicationJSON,
		HeaderByteAuth:    authorization,
	}
}

// ReadRequestBody 读取HTTP请求体的通用函数
// 用于Gin处理器中读取请求体数据
func ReadRequestBody(body io.ReadCloser) ([]byte, error) {
	defer body.Close()
	bodyData, err := io.ReadAll(body)
	if err != nil {
		core.LogError("读取请求体失败:", err)
		return nil, err
	}
	return bodyData, nil
}

// BuildQueryURL 构建带查询参数的URL
func BuildQueryURL(baseURL string, params map[string]string) string {
	if len(params) == 0 {
		return baseURL
	}

	url := baseURL + "?"
	first := true
	for key, value := range params {
		if !first {
			url += "&"
		}
		url += fmt.Sprintf("%s=%s", key, value)
		first = false
	}
	return url
}

// HandleHTTPError 处理HTTP错误的通用函数
// 根据错误类型进行分类处理和日志记录
func HandleHTTPError(operation string, err error) {
	if err == nil {
		return
	}

	core.LogError(fmt.Sprintf("%s失败:", operation), err)

	// 可以根据需要添加更多的错误分类处理逻辑
	// 例如：网络超时、连接拒绝、DNS解析失败等
}

// ValidateResponse 验证响应数据的通用函数
// 检查响应是否为空或格式是否正确
func ValidateResponse(data []byte, operation string) error {
	if len(data) == 0 {
		err := fmt.Errorf("%s响应数据为空", operation)
		core.LogError(err.Error())
		return err
	}

	// 简单检查是否为有效的JSON格式
	var temp interface{}
	if err := json.Unmarshal(data, &temp); err != nil {
		core.LogError(fmt.Sprintf("%s响应数据格式无效:", operation), err, "数据:", string(data))
		return fmt.Errorf("%s响应数据格式无效: %w", operation, err)
	}

	return nil
}
