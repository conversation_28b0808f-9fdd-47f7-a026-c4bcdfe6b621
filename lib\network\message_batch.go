package network

import (
	"sync"
	"time"
	"zone/lib/core"
)

// MessageBatch 消息批处理器，用于减少网络流量
type MessageBatch struct {
	session   *Session
	messages  [][]byte
	mutex     sync.Mutex
	ticker    *time.Ticker
	batchSize int           // 批处理大小
	flushTime time.Duration // 刷新时间间隔
	stopChan  chan struct{}
	isRunning bool
}

// NewMessageBatch 创建新的消息批处理器
func NewMessageBatch(session *Session) *MessageBatch {
	return &MessageBatch{
		session:   session,
		messages:  make([][]byte, 0, 10), // 预分配容量
		batchSize: 5,                     // 默认批处理5条消息
		flushTime: 50 * time.Millisecond, // 默认50ms刷新一次
		stopChan:  make(chan struct{}),
	}
}

// SetBatchSize 设置批处理大小
func (mb *MessageBatch) SetBatchSize(size int) {
	mb.mutex.Lock()
	defer mb.mutex.Unlock()
	mb.batchSize = size
}

// SetFlushTime 设置刷新时间间隔
func (mb *MessageBatch) SetFlushTime(duration time.Duration) {
	mb.mutex.Lock()
	defer mb.mutex.Unlock()
	mb.flushTime = duration

	// 如果正在运行，重新创建ticker
	if mb.isRunning && mb.ticker != nil {
		mb.ticker.Stop()
		mb.ticker = time.NewTicker(duration)
	}
}

// Start 启动批处理器
func (mb *MessageBatch) Start() {
	mb.mutex.Lock()
	defer mb.mutex.Unlock()

	if mb.isRunning {
		return
	}

	mb.isRunning = true
	mb.ticker = time.NewTicker(mb.flushTime)

	go mb.batchProcessor()
}

// Stop 停止批处理器
func (mb *MessageBatch) Stop() {
	mb.mutex.Lock()
	defer mb.mutex.Unlock()

	if !mb.isRunning {
		return
	}

	mb.isRunning = false
	close(mb.stopChan)

	if mb.ticker != nil {
		mb.ticker.Stop()
	}

	// 刷新剩余消息
	mb.flushMessages()
}

// AddMessage 添加消息到批处理队列
func (mb *MessageBatch) AddMessage(data []byte) {
	mb.mutex.Lock()
	defer mb.mutex.Unlock()

	if !mb.isRunning {
		// 如果批处理器未运行，直接发送
		mb.sendDirectly(data)
		return
	}

	// 复制数据以避免并发问题
	msgCopy := make([]byte, len(data))
	copy(msgCopy, data)
	mb.messages = append(mb.messages, msgCopy)

	// 如果达到批处理大小，立即刷新
	if len(mb.messages) >= mb.batchSize {
		mb.flushMessages()
	}
}

// batchProcessor 批处理器主循环
func (mb *MessageBatch) batchProcessor() {
	for {
		select {
		case <-mb.stopChan:
			return
		case <-mb.ticker.C:
			mb.mutex.Lock()
			mb.flushMessages()
			mb.mutex.Unlock()
		}
	}
}

// flushMessages 刷新所有待发送的消息（需要在锁内调用）
func (mb *MessageBatch) flushMessages() {
	if len(mb.messages) == 0 {
		return
	}

	// 如果只有一条消息，直接发送
	if len(mb.messages) == 1 {
		mb.sendDirectly(mb.messages[0])
		mb.messages = mb.messages[:0] // 清空切片但保留容量
		return
	}

	// 批量发送多条消息
	mb.sendBatch(mb.messages)
	mb.messages = mb.messages[:0] // 清空切片但保留容量
}

// sendDirectly 直接发送单条消息
func (mb *MessageBatch) sendDirectly(data []byte) {
	if mb.session != nil && !mb.session.ShutDown {
		// 使用Session的SendPBMsg方法发送消息
		mb.session.SendPBMsg(data)
	}
}

// sendBatch 批量发送多条消息
func (mb *MessageBatch) sendBatch(messages [][]byte) {
	if mb.session == nil || mb.session.ShutDown {
		return
	}

	// 计算总大小
	totalSize := 0
	for _, msg := range messages {
		totalSize += len(msg) + 4 // 4字节长度前缀
	}

	// 创建批量消息缓冲区
	batchBuffer := make([]byte, 0, totalSize)

	// 将多条消息打包成一条
	for _, msg := range messages {
		// 添加消息长度前缀（4字节）
		msgLen := len(msg)
		batchBuffer = append(batchBuffer,
			byte(msgLen>>24), byte(msgLen>>16), byte(msgLen>>8), byte(msgLen))
		// 添加消息内容
		batchBuffer = append(batchBuffer, msg...)
	}

	// 发送批量消息（通过SendChan）
	if mb.session.SendChan != nil {
		mb.session.SendChan <- batchBuffer
		core.LogDebug("批量发送消息成功，消息数量:", len(messages), "总大小:", len(batchBuffer))
	} else {
		core.LogError("发送通道为空，批量发送失败")
		// 如果批量发送失败，尝试逐条发送
		for _, msg := range messages {
			mb.sendDirectly(msg)
		}
	}
}

// GetQueueSize 获取当前队列中的消息数量
func (mb *MessageBatch) GetQueueSize() int {
	mb.mutex.Lock()
	defer mb.mutex.Unlock()
	return len(mb.messages)
}

// IsRunning 检查批处理器是否正在运行
func (mb *MessageBatch) IsRunning() bool {
	mb.mutex.Lock()
	defer mb.mutex.Unlock()
	return mb.isRunning
}
