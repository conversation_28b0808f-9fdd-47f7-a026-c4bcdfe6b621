package utils

import (
	"encoding/json"
	"fmt"
	"math/rand"
	"net/http"
	"reflect"
	"runtime/debug"
	"strconv"
	"strings"
	"sync"
	"time"
	"zone/lib/core"
)

func IsNil(i interface{}) bool {
	if i == nil {
		return true
	}

	vi := reflect.ValueOf(i)
	if vi.Kind() == reflect.Ptr {
		return vi.IsNil()
	}
	return false
}

func IsNotNil(i interface{}) bool {
	if i == nil {
		return false
	}

	vi := reflect.ValueOf(i)
	if vi.Kind() == reflect.Ptr {
		return vi.IsNil() == false
	}
	return true
}

// ! 得到ip
func HF_GetHttpIP(req *http.Request) string {
	ip := req.Header.Get("Remote_addr")
	if ip == "" {
		ip = req.RemoteAddr
	}
	return strings.Split(ip, ":")[0]
}

// 获得传入时间的五点
func GetTime(times int64) int64 {
	now := time.Unix(times, 0)

	if now.Hour() < 5 {
		return time.Date(now.Year(), now.Month(), now.Day(), 5, 0, 0, 0, now.Location()).Unix() - core.DAY_SECS
	} else {
		return time.Date(now.Year(), now.Month(), now.Day(), 5, 0, 0, 0, now.Location()).Unix()
	}
}

func GetTimerString() string {
	return fmt.Sprintf("%d-%d-%d %d:%d:%d",
		core.TimeServer().Year(),
		core.TimeServer().Month(),
		core.TimeServer().Day(),
		core.TimeServer().Hour(),
		core.TimeServer().Minute(),
		core.TimeServer().Second())
}

func HF_Atoi(s string) int {
	num, _ := strconv.Atoi(s)
	return num
}

func HF_AtoI64(s string) int64 {
	num, _ := strconv.ParseInt(s, 10, 64)
	return num
}

func SyncMapToJSON(m *sync.Map) (string, error) {
	// 空指针检查
	if m == nil {
		return "{}", nil
	}

	tempMap := make(map[string]interface{})
	// 遍历 sync.Map
	m.Range(func(key, value interface{}) bool {
		// 处理键类型
		stringKey, ok := key.(string)
		if !ok {
			// 非字符串键的处理策略
			stringKey = fmt.Sprintf("%v", key)
		}
		// 处理值类型
		if isJSONSerializable(value) {
			tempMap[stringKey] = value
		} else {
			tempMap[stringKey] = fmt.Sprintf("%+v", value)
		}
		return true
	})
	// 序列化为 JSON
	jsonData, err := json.Marshal(tempMap)
	return string(jsonData), err
}

// JSONToSyncMap 将JSON字符串反序列化为sync.Map
func JSONToSyncMap(jsonStr string, m *sync.Map) error {
	// 空指针检查
	if m == nil {
		return fmt.Errorf("sync.Map is nil")
	}

	// 处理空字符串情况
	if jsonStr == "" || jsonStr == "{}" {
		return nil
	}

	// 解析JSON到临时map
	var tempMap map[string]interface{}
	if err := json.Unmarshal([]byte(jsonStr), &tempMap); err != nil {
		return fmt.Errorf("JSON解析失败: %v", err)
	}

	// 将数据加载到sync.Map
	for key, value := range tempMap {
		m.Store(key, value)
	}

	return nil
}

// SyncMapInt64ToJSON 将存储int64值的sync.Map序列化为JSON字符串
func SyncMapInt64ToJSON(m *sync.Map) (string, error) {
	// 空指针检查
	if m == nil {
		return "{}", nil
	}

	tempMap := make(map[string]int64)
	// 遍历 sync.Map
	m.Range(func(key, value interface{}) bool {
		// 处理键类型
		stringKey, ok := key.(string)
		if !ok {
			// 非字符串键的处理策略
			stringKey = fmt.Sprintf("%v", key)
		}

		// 处理值类型，确保转换为int64
		var int64Value int64
		switch v := value.(type) {
		case int64:
			int64Value = v
		case float64:
			int64Value = int64(v)
		case int:
			int64Value = int64(v)
		case int32:
			int64Value = int64(v)
		default:
			// 对于无法转换的类型，记录错误并跳过
			core.LogError("SyncMapInt64ToJSON: 无法转换为int64", "Key:", stringKey, "Value:", value, "Type:", fmt.Sprintf("%T", value))
			return true // 继续遍历
		}

		tempMap[stringKey] = int64Value
		return true
	})

	// 序列化为 JSON
	jsonData, err := json.Marshal(tempMap)
	return string(jsonData), err
}

// JSONToSyncMapInt64 将JSON字符串反序列化为存储int64值的sync.Map
func JSONToSyncMapInt64(jsonStr string, m *sync.Map) error {
	// 空指针检查
	if m == nil {
		return fmt.Errorf("sync.Map is nil")
	}

	// 处理空字符串情况
	if jsonStr == "" || jsonStr == "{}" {
		return nil
	}

	// 解析JSON到临时map
	var tempMap map[string]interface{}
	if err := json.Unmarshal([]byte(jsonStr), &tempMap); err != nil {
		return fmt.Errorf("JSON解析失败: %v", err)
	}

	// 将数据加载到sync.Map，确保值为int64类型
	for key, value := range tempMap {
		var int64Value int64
		switch v := value.(type) {
		case float64:
			int64Value = int64(v)
		case int64:
			int64Value = v
		case int:
			int64Value = int64(v)
		case int32:
			int64Value = int64(v)
		default:
			return fmt.Errorf("无法将值转换为int64: key=%s, value=%v, type=%T", key, value, value)
		}
		m.Store(key, int64Value)
	}

	return nil
}

// SyncMapIntToJSON 将存储int值的sync.Map序列化为JSON字符串
func SyncMapIntToJSON(m *sync.Map) (string, error) {
	// 空指针检查
	if m == nil {
		return "{}", nil
	}

	tempMap := make(map[string]int)
	// 遍历 sync.Map
	m.Range(func(key, value interface{}) bool {
		// 处理键类型
		stringKey, ok := key.(string)
		if !ok {
			// 非字符串键的处理策略
			stringKey = fmt.Sprintf("%v", key)
		}

		// 处理值类型，确保转换为int
		var intValue int
		switch v := value.(type) {
		case int:
			intValue = v
		case float64:
			intValue = int(v)
		case int64:
			intValue = int(v)
		case int32:
			intValue = int(v)
		default:
			// 对于无法转换的类型，记录错误并跳过
			core.LogError("SyncMapIntToJSON: 无法转换为int", "Key:", stringKey, "Value:", value, "Type:", fmt.Sprintf("%T", value))
			return true // 继续遍历
		}

		tempMap[stringKey] = intValue
		return true
	})

	// 序列化为 JSON
	jsonData, err := json.Marshal(tempMap)
	return string(jsonData), err
}

// JSONToSyncMapInt 将JSON字符串反序列化为存储int值的sync.Map
func JSONToSyncMapInt(jsonStr string, m *sync.Map) error {
	// 空指针检查
	if m == nil {
		return fmt.Errorf("sync.Map is nil")
	}

	// 处理空字符串情况
	if jsonStr == "" || jsonStr == "{}" {
		return nil
	}

	// 解析JSON到临时map
	var tempMap map[string]interface{}
	if err := json.Unmarshal([]byte(jsonStr), &tempMap); err != nil {
		return fmt.Errorf("JSON解析失败: %v", err)
	}

	// 将数据加载到sync.Map，确保值为int类型
	for key, value := range tempMap {
		var intValue int
		switch v := value.(type) {
		case float64:
			intValue = int(v)
		case int:
			intValue = v
		case int64:
			intValue = int(v)
		case int32:
			intValue = int(v)
		default:
			return fmt.Errorf("无法将值转换为int: key=%s, value=%v, type=%T", key, value, value)
		}
		m.Store(key, intValue)
	}

	return nil
}

// 检查值是否可序列化
func isJSONSerializable(v interface{}) bool {
	_, err := json.Marshal(v)
	return err == nil
}

func HF_JtoA(v interface{}) string {
	s, err := json.Marshal(v)
	if err != nil {
		core.LogError("HF_JtoA err:", string(debug.Stack()))
	}
	return string(s)
}

func HF_JtoB(v interface{}) []byte {
	s, err := json.Marshal(v)
	if err != nil {
		core.LogError("HF_JtoB err:", string(debug.Stack()))
	}
	return s
}

func HF_Atof(s string) float32 {
	num, _ := strconv.ParseFloat(s, 32)
	return float32(num)
}

func HF_Atof64(s string) float64 {
	num, _ := strconv.ParseFloat(s, 64)
	return num
}

func HF_Itof64(v int) float64 {

	stringfight := strconv.Itoa(v)
	int64fight, _ := strconv.ParseFloat(stringfight, 64)
	return int64fight
}
func HF_Itoi64(v int) int64 {

	stringfight := strconv.Itoa(v)
	int64fight, _ := strconv.ParseInt(stringfight, 10, 64)
	return int64fight
}

// ! 得到一个随机数[0, num-1]
func HF_GetRandom(num int) int {
	if num == 0 {
		defer func() {
			core.LogError("出现了一个0随机", string(debug.Stack()))
		}()
		return 0
	}
	return rand.New(rand.NewSource(time.Now().UnixNano() + rand.Int63n(1000))).Intn(num)
}

// ! int取最小
func HF_MinInt(a int, b int) int {
	if a < b {
		return a
	}

	return b
}

// ! int取最大
func HF_MaxInt(a int, b int) int {
	if a > b {
		return a
	}

	return b
}

func HF_AbsInt(a int) int {
	if a > 0 {
		return a
	}
	return a * -1
}

func HasPrefix(s, prefix string) (ok bool, value string) {
	if len(s) < len(prefix) {
		return false, ""
	}

	return s[0:len(prefix)] == prefix, s[len(prefix):]
}

func HasPrefixOnly(s, prefix string) bool {
	if len(s) < len(prefix) {
		return false
	}

	return s[0:len(prefix)] == prefix
}

func ClampInt(value, min, max int) int {
	if min > max { // 自动校正颠倒的区间
		min, max = max, min
	}

	switch {
	case value < min:
		return min
	case value > max:
		return max
	default:
		return value
	}
}
