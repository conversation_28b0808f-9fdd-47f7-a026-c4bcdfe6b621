// JoysGames copyrights this specification. No part of this specification may be
// reproduced in any form or means, without the prior written consent of JoysGames.
//
// This specification is preliminary and is subject to change at any time without notice.
// JoysGames assumes no responsibility for any errors contained herein.
//
// The above copyright notice and this permission notice shall be included
// in all copies or substantial portions of the Software.
// @package JGServer
// @copyright joysgames.cn All rights reserved.
// @version v1.0

package core

import (
	"fmt"
	"reflect"
	"runtime/debug"
	"strconv"
	"strings"
	"sync"
	"sync/atomic"
	"time"
)

// 加载器映射
var (
	AddrMap      sync.Map // 地址映射实例
	GoroutineMap sync.Map // 协程映射地址
)

// 模块助手（非线程安全）
type ModLoader struct {
	ModMap      sync.Map       // 模块映射
	TaskChan    chan *ChanTask // 任务通道
	Stack       atomic.Value   // 栈信息
	goroutineID uint64         // 协程ID
	addr        string         // 协程地址
	taskCount   int32          // 剩余任务数量
	saveDirty   int32          // 存档脏标记
	taskFlag    int32          // 执行标记,当>0表明当前有任务阻塞，直接调用
}

// 初始化
func (that *ModLoader) Init() {
	that.SetGoroutineID()
	if that.TaskChan == nil {
		that.TaskChan = make(chan *ChanTask, CHAN_TASK_SIZE)
	}
}

// 读档
func (that *ModLoader) Load() {
	that.ModMap.Range(func(_, value interface{}) bool {
		mod, ok := value.(IModule)
		if ok {
			mod.Load()
		}
		return true
	})
}

// 读档后处理
func (that *ModLoader) LaterLoad() {
	that.ModMap.Range(func(_, value interface{}) bool {
		mod, ok := value.(IModule)
		if ok {
			mod.LaterLoad()
		}
		return true
	})
}

// 启动模块
func (that *ModLoader) OnStart() {
	that.ModMap.Range(func(_, value interface{}) bool {
		mod, ok := value.(IModule)
		if ok {
			mod.OnStart()
		}
		return true
	})
}

// 存档
func (that *ModLoader) Save() {
	that.ModMap.Range(func(_, value interface{}) bool {
		mod, ok := value.(IModule)
		if ok {
			mod.Save()
		}
		return true
	})
}

// 逻辑帧更新
func (that *ModLoader) Update(dt int64) {
	that.ModMap.Range(func(_, value interface{}) bool {
		mod, ok := value.(IModule)
		if ok {
			mod.Update(dt)
			if mod.HasSaveDirty() {
				mod.Save()
				mod.SetSaveDirty(false)
			}
		}
		return true
	})
	if atomic.LoadInt32(&that.saveDirty) == 1 {
		that.Save()
		atomic.StoreInt32(&that.saveDirty, 0)
	}
}

// 每日五点更新
func (that *ModLoader) OnRefresh() {
	that.ModMap.Range(func(_, value interface{}) bool {
		mod, ok := value.(IModule)
		if ok {
			mod.OnRefresh()
		}
		return true
	})
}

// 同步消息
func (that *ModLoader) SendInfo() {
	that.ModMap.Range(func(_, value interface{}) bool {
		mod, ok := value.(IModule)
		if ok {
			mod.SendInfo()
		}
		return true
	})
}

// 重置任务通道（谨慎使用）
func (that *ModLoader) ResetTaskChan() {
	if that.TaskChan == nil {
		that.TaskChan = make(chan *ChanTask, CHAN_TASK_SIZE)
	}
	for len(that.TaskChan) > 0 {
		task := <-that.TaskChan // 清空防止内存泄漏
		task.Done()
	}
	atomic.StoreInt32(&that.taskCount, 0)
}

// 获取任务通道
func (that *ModLoader) GetTaskChan() chan *ChanTask {
	Await(that.ResetTaskChan)
	return that.TaskChan
}

// 执行多线程任务
func (that *ModLoader) DoTask(task *ChanTask) {
	atomic.AddInt32(&that.taskFlag, 1)
	that.Stack.Store(task.Stack)
	task.Result, task.Err = modInvokeInternal(that, task.Module, task.FunName, task.Args...)
	atomic.AddInt32(&that.taskFlag, -1)
	task.Done()
	that.Stack.Store([]string{})
}

// 获取任务间隔时间
func (that *ModLoader) getTaskInerval(taskCount int32) time.Duration {
	if IsDebug {
		return time.Duration(60000 * taskCount) // 60秒超时
	} else {
		return time.Duration(500 * taskCount) // 500ms
	}
}

// 添加多线程任务
func (that *ModLoader) AddTask(task *ChanTask) ([]reflect.Value, error) {
	if len(that.TaskChan) >= CHAN_TASK_SIZE {
		return nil, fmt.Errorf("AddTask: Chan task overlap %d", CHAN_TASK_SIZE)
	}
	task.Add(1)
	taskCount := atomic.AddInt32(&that.taskCount, 1)
	LogInfo("%s_%s 当前任务数量%d", task.Module, task.FunName, taskCount)
	that.TaskChan <- task // 通道切换任务消耗15ms左右
	interval := that.getTaskInerval(taskCount)
	ch := make(chan struct{})
	go func() {
		task.Wait()
		// Logger.Errorf("任务%s_%s等待", task.Module, task.FunName)
		if atomic.LoadInt32(&task.Failed) == 0 {
			ch <- struct{}{}
		} else {
			task.Free()
		}
	}()

	select {
	case <-ch:
		defer task.Free()
		// Logger.Infof("任务%s_%s结束", task.Module, task.FunName)
	case <-time.After(time.Millisecond * interval):
		atomic.StoreInt32(&task.Failed, 1)
		task.Err = fmt.Errorf("%s_%s timeout", task.Module, task.FunName)
		LogError("任务%s_%s超时", task.Module, task.FunName)
	}
	atomic.AddInt32(&that.taskCount, -1)
	return task.Result, task.Err
}

// 获取模块
func (that *ModLoader) GetModule(name string) IModule {
	value, ok := that.ModMap.Load(name)
	if ok {
		return value.(IModule)
	}
	return DefMod
}

// 设置模块
func (that *ModLoader) AddModule(mod IModule, host interface{}) IModule {
	if mod == nil || host == nil {
		LogError("addModule invalid")
		return mod
	}
	metaName := fmt.Sprint(reflect.TypeOf(mod))
	metaArr := strings.Split(metaName, ".")
	mod.Init(metaArr[1], host)
	that.ModMap.Store(mod.GetName(), mod)
	return mod
}

// 设置模块
func (that *ModLoader) RemoveModule(name string) {
	that.ModMap.Delete(name)
}

// 获取模块
func (that *ModLoader) ForEach(cb func(manager IModule)) {
	that.ModMap.Range(func(key, value interface{}) bool {
		manager, ok := value.(IModule)
		if ok && cb != nil {
			cb(manager)
		}
		return true
	})
}

// 设置模块
func (that *ModLoader) SetSaveDirty() {
	atomic.StoreInt32(&that.saveDirty, 1)
}

// 设置模块
func (that *ModLoader) SetGoroutineID() {
	if that.addr == "" {
		that.addr = "0x" + strconv.FormatInt(int64(reflect.ValueOf(that).Pointer()), 16)
		AddrMap.Store(that.addr, that)
	}
	if that.goroutineID > 0 {
		GoroutineMap.Delete(that.goroutineID)
	}
	that.goroutineID = GetGoroutineID()
	GoroutineMap.Store(that.goroutineID, that.addr)
}

// 设置模块
func (that *ModLoader) GetGoroutineID() uint64 {
	return that.goroutineID
}

// 销毁
func (that *ModLoader) OnDestory() {
	that.ModMap.Range(func(_, value interface{}) bool {
		mod, ok := value.(IModule)
		if ok {
			mod.OnDestory()
		}
		return true
	})
}

// 判定循环死锁条件如下：
// 栈列表不算引用计数，栈列表最后一位必须为运行中loader，且有计数
func (that *ModLoader) checkDeadLock(top string) ([]string, bool) {
	val, ok := AddrMap.Load(top)
	if !ok {
		LogError("堆栈地址%s映射加载器丢失", top)
		return nil, false
	}

	// 两层任务以上加入堆栈队列
	taskStack, ok := val.(*ModLoader).Stack.Load().([]string)
	if ok && len(taskStack) > 0 {
		// 如果递归出现则死锁
		for _, v := range taskStack {
			if v == that.addr {
				return nil, true
			}
		}
		taskStack = append(taskStack, that.addr)
	} else {
		taskStack = []string{top, that.addr}
	}

	// 当前加载器无运行中任务，则直接加入队列
	if atomic.LoadInt32(&that.taskFlag) < 1 {
		return taskStack, false
	}
	for i := 0; i < len(taskStack)-1; i++ {
		addr := taskStack[i]
		val, ok = AddrMap.Load(addr)
		if !ok {
			continue
		}

		// 加上自身计数,引用次数超过1次代表循环死锁
		loader := val.(*ModLoader)
		if atomic.LoadInt32(&loader.taskCount) > 1 {
			LogInfo("检测到任务%s循环死锁", that.addr)
			return nil, true
		}
	}
	return taskStack, false
}

// 检查是否可直接调用
func (that *ModLoader) CheckCall() ([]string, bool) {
	curGoroutineID := GetGoroutineID()
	if curGoroutineID == that.goroutineID {
		return nil, true
	}
	if that.goroutineID < 1 {
		LogError("未初始化协程")
		return nil, true
	}

	// 栈顶则直接加入
	var top string
	val, ok := GoroutineMap.Load(curGoroutineID)
	if ok {
		top = val.(string)
	} else {
		stack := string(debug.Stack())
		offset := strings.Index(stack, "(*ModLoader).DoTask")
		if offset < 0 {
			return nil, false
		}
		top = stack[offset+20 : offset+32]
	}

	// 循环死锁则直接执行任务，打断死锁条件
	return that.checkDeadLock(top)
}

// ----------------------------------------------------
// 内置调用模块函数（不可与ModInvoke合并，避免递归问题）
func modInvokeInternal(that *ModLoader, modName string, funcName string, args ...interface{}) ([]reflect.Value, error) {
	if GetGoroutineID() != that.GetGoroutineID() {
		LogError("当期任务协程未切换,modName=%s,funcName=%s", modName, funcName)
	}
	mod := that.GetModule(modName)
	return mod.Invoker(funcName, args...)
}

// 调用模块函数（同步模型，强制等待切回当前协程）
func ModInvoke(that *ModLoader, modName string, funcName string, args ...interface{}) ([]reflect.Value, error) {
	// 当相同线程或者线程被阻塞的时候，直接调用将可避开多线程冲突
	// 线程被阻塞将阻塞update逻辑和消息进入
	stack, check := that.CheckCall()
	if check {
		return modInvokeInternal(that, modName, funcName, args...)
	} else {
		task := NewChanTask(modName, funcName, args)
		task.Stack = stack // 携带堆栈
		return that.AddTask(task)
	}
}

// 调用模块函数（同步模型，可多线程安全访问的数据）
func ModInvokeSafe(that *ModLoader, modName string, funcName string, args ...interface{}) ([]reflect.Value, error) {
	mod := that.GetModule(modName)
	return mod.Invoker(funcName, args...)
}
