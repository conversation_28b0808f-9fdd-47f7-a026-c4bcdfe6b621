package api

import (
	"net/http"
	"strconv"
	"zone/lib/core"
	"zone/lib/storage"

	"github.com/gin-gonic/gin"
)

// ServerUrlConfigAPI ServerUrl配置管理API（简化版）
type ServerUrlConfigAPI struct {
	configService *storage.ServerUrlService
}

// NewServerUrlConfigAPI 创建ServerUrl配置API实例
func NewServerUrlConfigAPI() *ServerUrlConfigAPI {
	return &ServerUrlConfigAPI{
		configService: storage.GetServerUrlService(),
	}
}

// CreateConfigRequest 创建配置请求（简化版）
type CreateConfigRequest struct {
	ID           int    `json:"id" binding:"required"`            // 平台编号
	WebSocketURL string `json:"websocket_url" binding:"required"` // WebSocket地址
	HttpURL      string `json:"http_url" binding:"required"`      // HTTP地址
	UseEncrypt   bool   `json:"use_encrypt"`                      // 是否启用加密
}

// UpdateConfigRequest 更新配置请求（简化版）
type UpdateConfigRequest struct {
	WebSocketURL string `json:"websocket_url" binding:"required"` // WebSocket地址
	HttpURL      string `json:"http_url" binding:"required"`      // HTTP地址
	UseEncrypt   bool   `json:"use_encrypt"`                      // 是否启用加密
}

// ConfigResponse 配置响应
type ConfigResponse struct {
	*storage.ServerUrlConfig
}

// ConfigListResponse 配置列表响应
type ConfigListResponse struct {
	Configs []*storage.ServerUrlConfig `json:"configs"`
	Total   int                        `json:"total"`
}

// CreateConfig 创建新配置
func (api *ServerUrlConfigAPI) CreateConfig(c *gin.Context) {
	var req CreateConfigRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		core.LogError("创建配置请求参数错误:", err)
		c.JSON(http.StatusBadRequest, APIResponse{
			Code:    400,
			Message: "请求参数错误: " + err.Error(),
		})
		return
	}

	// 验证平台ID
	if !isValidPlatformID(req.ID) {
		c.JSON(http.StatusBadRequest, APIResponse{
			Code:    400,
			Message: "无效的平台ID，支持: 0-抖音，1-快手，99-本地开发环境",
		})
		return
	}

	// 创建配置对象
	config := &storage.ServerUrlConfig{
		ID:           req.ID,
		WebSocketURL: req.WebSocketURL,
		HttpURL:      req.HttpURL,
		UseEncrypt:   req.UseEncrypt,
	}

	// 创建配置
	err := api.configService.CreateConfig(config)
	if err != nil {
		core.LogError("创建配置失败:", err)
		c.JSON(http.StatusInternalServerError, APIResponse{
			Code:    500,
			Message: "创建配置失败: " + err.Error(),
		})
		return
	}

	core.LogInfo("创建配置成功: 平台ID=", config.ID)
	c.JSON(http.StatusOK, APIResponse{
		Code:    200,
		Message: "创建配置成功",
		Data:    &ConfigResponse{config},
	})
}

// UpdateConfig 更新配置
func (api *ServerUrlConfigAPI) UpdateConfig(c *gin.Context) {
	platformIDStr := c.Param("platform_id")
	platformID, err := strconv.Atoi(platformIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, APIResponse{
			Code:    400,
			Message: "无效的平台ID",
		})
		return
	}

	var req UpdateConfigRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		core.LogError("更新配置请求参数错误: %v", err)
		c.JSON(http.StatusBadRequest, APIResponse{
			Code:    400,
			Message: "请求参数错误: " + err.Error(),
		})
		return
	}

	// 验证平台ID
	if !isValidPlatformID(platformID) {
		c.JSON(http.StatusBadRequest, APIResponse{
			Code:    400,
			Message: "无效的平台ID",
		})
		return
	}

	// 创建更新的配置对象
	config := &storage.ServerUrlConfig{
		ID:           platformID,
		WebSocketURL: req.WebSocketURL,
		HttpURL:      req.HttpURL,
		UseEncrypt:   req.UseEncrypt,
	}

	// 更新配置
	err = api.configService.UpdateConfig(config)
	if err != nil {
		core.LogError("更新配置失败: ", err)
		c.JSON(http.StatusInternalServerError, APIResponse{
			Code:    500,
			Message: "更新配置失败: " + err.Error(),
		})
		return
	}

	core.LogInfo("更新配置成功: 平台ID=", platformID)
	c.JSON(http.StatusOK, APIResponse{
		Code:    200,
		Message: "更新配置成功",
		Data:    &ConfigResponse{config},
	})
}

// DeleteConfig 删除配置
func (api *ServerUrlConfigAPI) DeleteConfig(c *gin.Context) {
	platformIDStr := c.Param("platform_id")
	platformID, err := strconv.Atoi(platformIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, APIResponse{
			Code:    400,
			Message: "无效的平台ID",
		})
		return
	}

	// 验证平台ID
	if !isValidPlatformID(platformID) {
		c.JSON(http.StatusBadRequest, APIResponse{
			Code:    400,
			Message: "无效的平台ID",
		})
		return
	}

	// 删除配置
	err = api.configService.DeleteConfig(platformID)
	if err != nil {
		core.LogError("删除配置失败:", err)
		c.JSON(http.StatusInternalServerError, APIResponse{
			Code:    500,
			Message: "删除配置失败: " + err.Error(),
		})
		return
	}

	core.LogInfo("删除配置成功: 平台ID", platformID)
	c.JSON(http.StatusOK, APIResponse{
		Code:    200,
		Message: "删除配置成功",
	})
}

// GetConfig 获取单个配置
func (api *ServerUrlConfigAPI) GetConfig(c *gin.Context) {
	platformIDStr := c.Param("platform_id")
	platformID, err := strconv.Atoi(platformIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, APIResponse{
			Code:    400,
			Message: "无效的平台ID",
		})
		return
	}

	// 获取配置
	config, err := api.configService.GetConfigByPlatform(platformID)
	if err != nil {
		core.LogError("获取配置失败:", err)
		c.JSON(http.StatusNotFound, APIResponse{
			Code:    404,
			Message: "配置不存在",
		})
		return
	}

	c.JSON(http.StatusOK, APIResponse{
		Code:    200,
		Message: "获取配置成功",
		Data:    &ConfigResponse{config},
	})
}

// GetAllConfigs 获取所有配置
func (api *ServerUrlConfigAPI) GetAllConfigs(c *gin.Context) {
	configs, err := api.configService.GetAllConfigs()
	if err != nil {
		core.LogError("获取所有配置失败:", err)
		c.JSON(http.StatusInternalServerError, APIResponse{
			Code:    500,
			Message: "获取配置失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, APIResponse{
		Code:    200,
		Message: "获取配置成功",
		Data: &ConfigListResponse{
			Configs: configs,
			Total:   len(configs),
		},
	})
}

// RefreshCache 刷新配置缓存
func (api *ServerUrlConfigAPI) RefreshCache(c *gin.Context) {
	operatorName := c.Query("operator")

	core.LogInfo("开始刷新配置缓存, 操作者:", getOperatorName(operatorName))

	// 刷新配置服务缓存
	err := api.configService.RefreshCache()
	if err != nil {
		core.LogError("刷新配置缓存失败:", err)
		c.JSON(http.StatusInternalServerError, APIResponse{
			Code:    500,
			Message: "刷新配置缓存失败: " + err.Error(),
		})
		return
	}

	core.LogInfo("配置缓存刷新成功, 操作者:", getOperatorName(operatorName))
	c.JSON(http.StatusOK, APIResponse{
		Code:    200,
		Message: "配置缓存刷新成功",
	})
}

// GetCacheStats 获取缓存统计信息
func (api *ServerUrlConfigAPI) GetCacheStats(c *gin.Context) {
	stats := api.configService.GetServiceStats()

	c.JSON(http.StatusOK, APIResponse{
		Code:    200,
		Message: "获取缓存统计成功",
		Data:    stats,
	})
}

// TestConfig 测试配置连通性
func (api *ServerUrlConfigAPI) TestConfig(c *gin.Context) {
	platformStr := c.DefaultQuery("platform", "0")
	isLocalStr := c.DefaultQuery("local", "false")

	platform, _ := strconv.Atoi(platformStr)
	isLocal := isLocalStr == "true"

	// 测试获取配置
	wsURL := api.configService.GetWebSocketURL(platform, isLocal)
	httpURL := api.configService.GetHTTPURL(platform, isLocal)
	useEncrypt := api.configService.GetUseEncrypt(isLocal)

	testResult := map[string]interface{}{
		"websocket_url": wsURL,
		"http_url":      httpURL,
		"use_encrypt":   useEncrypt,
		"platform":      platform,
		"is_local":      isLocal,
		"test_time":     "2025-06-25 15:04:05",
	}

	// 检查配置是否有效
	if wsURL == "" || httpURL == "" {
		c.JSON(http.StatusOK, APIResponse{
			Code:    500,
			Message: "配置测试失败：获取到空的URL配置",
			Data:    testResult,
		})
		return
	}

	c.JSON(http.StatusOK, APIResponse{
		Code:    200,
		Message: "配置测试成功",
		Data:    testResult,
	})
}

// isValidPlatformID 验证平台ID是否有效
func isValidPlatformID(platformID int) bool {
	validIDs := []int{0, 1, 99} // 0-抖音，1-快手，99-本地开发环境
	for _, validID := range validIDs {
		if platformID == validID {
			return true
		}
	}
	return false
}

// getOperatorName 获取操作者名称，如果为空则返回默认值
func getOperatorName(operator string) string {
	if operator == "" {
		return "system"
	}
	return operator
}

// RegisterServerUrlConfigRoutes 注册ServerUrl配置管理路由
func RegisterServerUrlConfigRoutes(router *gin.Engine) {
	api := NewServerUrlConfigAPI()

	// ServerUrl配置管理路由组
	configGroup := router.Group("/api/serverurlconfig")
	{
		// 配置CRUD操作
		configGroup.POST("/", api.CreateConfig)               // 创建配置
		configGroup.GET("/", api.GetAllConfigs)               // 获取所有配置
		configGroup.GET("/:platform_id", api.GetConfig)       // 获取单个配置
		configGroup.PUT("/:platform_id", api.UpdateConfig)    // 更新配置
		configGroup.DELETE("/:platform_id", api.DeleteConfig) // 删除配置

		// 配置管理
		configGroup.POST("/cache/refresh", api.RefreshCache) // 刷新缓存
		configGroup.GET("/cache/stats", api.GetCacheStats)   // 获取缓存统计
		configGroup.GET("/test", api.TestConfig)             // 测试配置
	}

	core.LogInfo("ServerUrl配置管理API路由注册完成")
}
