package mods

import (
	"encoding/json"
	"zone/game/Request"
	"zone/game/platform/bytedance"
	"zone/game/platform/kuaishou"
	"zone/lib/core"
	"zone/lib/db"
	"zone/lib/network"
	"zone/lib/payload"
	"zone/lib/utils"
	"zone/pb/Message"
)

// LiveGame 单人直播游戏逻辑管理器
type LiveGame struct {
	// 关联的房间
	Room *LiveRoom

	// 游戏状态相关
	RoundID    string
	LogicFrame int32 // 逻辑帧
	PlayState  int   // 游戏状态
	StartTime  int64 // 开始时间
	// PlayTime   int   // 游戏时间
	EndTime int64 // 结束时间
	IsOver  bool

	// 暂停机制相关
	IsPaused        bool  // 是否暂停
	PauseStartTime  int64 // 暂停开始时间
	TotalPausedTime int64 // 总暂停时间
	PausedPlayState int   // 暂停前的游戏状态
	PausedEndTime   int64 // 暂停前的结束时间

	// 积分系统
	ScorePool     int64 // 积分池
	WinStreakPool int32 // 连胜池

	// 游戏事件队列
	MapComment *utils.SafeSlice[[]byte]
	MapGift    *utils.SafeSlice[[]*payload.PayloadData]
	MapFans    *utils.SafeSlice[[]*payload.PayloadData]
	MapLike    *utils.SafeSlice[[]byte]
}

// NewLiveGame 创建新的游戏逻辑管理器
func NewLiveGame(room *LiveRoom) *LiveGame {
	game := &LiveGame{
		Room: room,
	}
	game.Init()
	return game
}

// Init 初始化游戏逻辑
func (game *LiveGame) Init() {
	game.LogicFrame = 0

	game.MapComment = utils.NewSafeSlice[[]byte]()
	game.MapGift = utils.NewSafeSlice[[]*payload.PayloadData]()
	game.MapFans = utils.NewSafeSlice[[]*payload.PayloadData]()
	game.MapLike = utils.NewSafeSlice[[]byte]()
	game.WinStreakPool = 0
	game.ScorePool = 0
	game.IsOver = false
}

// Reset 重置游戏状态
func (game *LiveGame) Reset(close bool) {
	if close {
		game.PlayState = PLAY_STATE_WAIT
	}

	game.IsOver = true

	// 重置暂停状态
	game.IsPaused = false
	game.PauseStartTime = 0
	game.TotalPausedTime = 0
	game.PausedPlayState = 0
	game.PausedEndTime = 0

	// 重新初始化所有数据结构
	game.LogicFrame = 0

	game.MapComment = utils.NewSafeSlice[[]byte]()
	game.MapGift = utils.NewSafeSlice[[]*payload.PayloadData]()
	game.MapFans = utils.NewSafeSlice[[]*payload.PayloadData]()
	game.MapLike = utils.NewSafeSlice[[]byte]()
	game.WinStreakPool = 0

	// 更新积分池
	game.ScorePool = game.ScorePool * 3 / 10
	game.Room.AnchorData.ScorePool = game.ScorePool
}

// IsSessionNil 检查会话是否为空
func (game *LiveGame) IsSessionNil() bool {
	return game.Room.IsSerssionNil()
}

// GetRoomId 获取房间ID
func (game *LiveGame) GetRoomId() int32 {
	return game.Room.RoomId
}

// GetRoomPlatformId 获取平台房间ID
func (game *LiveGame) GetRoomPlatformId() string {
	return game.Room.RoomPlatformId
}

// GetAnchorId 获取主播ID
func (game *LiveGame) GetAnchorId() string {
	return game.Room.AnchorId
}

// GetSession 获取网络会话
func (game *LiveGame) GetSession() *network.Session {
	return game.Room.Session
}

// ==================== 游戏状态管理方法 ====================

// 基本游戏信息访问器
func (game *LiveGame) GetPlayState() int {
	return game.PlayState
}

func (game *LiveGame) SetPlayState(state int) {
	game.PlayState = state
}

func (game *LiveGame) GetScorePool() int64 {
	return game.ScorePool
}

func (game *LiveGame) SetScorePool(pool int64) {
	game.ScorePool = pool
}

func (game *LiveGame) GetWinStreakPool() int32 {
	return game.WinStreakPool
}

func (game *LiveGame) SetWinStreakPool(pool int32) {
	game.WinStreakPool = pool
}

// ==================== 暂停/恢复机制 ====================

// Pause 暂停游戏
func (game *LiveGame) Pause() {
	if game.IsPaused {
		return // 已经暂停，无需重复操作
	}

	core.LogInfo("LiveGame暂停", "RoomId:", game.Room.RoomId, "PlayState:", game.PlayState)

	// 记录暂停状态
	game.IsPaused = true
	game.PauseStartTime = core.TimeServer().Unix()
	game.PausedPlayState = game.PlayState
	game.PausedEndTime = game.EndTime

	// 设置为暂停状态
	game.PlayState = PLAY_STATE_PAUSE
}

// Resume 恢复游戏
func (game *LiveGame) Resume() {
	if !game.IsPaused {
		return // 没有暂停，无需恢复
	}

	core.LogInfo("LiveGame恢复", "RoomId:", game.Room.RoomId, "PausedPlayState:", game.PausedPlayState)

	// 计算暂停时间
	pauseDuration := core.TimeServer().Unix() - game.PauseStartTime
	game.TotalPausedTime += pauseDuration

	// 恢复游戏状态
	game.PlayState = game.PausedPlayState

	// 调整结束时间（延长暂停的时间）
	if game.PausedEndTime > 0 {
		game.EndTime = game.PausedEndTime + pauseDuration
	}

	// 重置暂停标记
	game.IsPaused = false
	game.PauseStartTime = 0
	game.PausedPlayState = 0
	game.PausedEndTime = 0
}

// IsPausedState 检查是否处于暂停状态
func (game *LiveGame) IsPausedState() bool {
	return game.IsPaused
}

// GetTotalPausedTime 获取总暂停时间
func (game *LiveGame) GetTotalPausedTime() int64 {
	totalPaused := game.TotalPausedTime
	if game.IsPaused && game.PauseStartTime > 0 {
		// 如果当前正在暂停，加上当前暂停的时间
		totalPaused += core.TimeServer().Unix() - game.PauseStartTime
	}
	return totalPaused
}

// GetEffectivePlayTime 获取有效游戏时间（排除暂停时间）
func (game *LiveGame) GetEffectivePlayTime() int64 {
	if game.StartTime == 0 {
		return 0
	}

	currentTime := core.TimeServer().Unix()
	totalTime := currentTime - game.StartTime
	return totalTime - game.GetTotalPausedTime()
}

func (game *LiveGame) GetLogicFrame() int32 {
	return game.LogicFrame
}

func (game *LiveGame) SetLogicFrame(frame int32) {
	game.LogicFrame = frame
}

func (game *LiveGame) GetStartTime() int64 {
	return game.StartTime
}

func (game *LiveGame) SetStartTime(time int64) {
	game.StartTime = time
}

func (game *LiveGame) GetEndTime() int64 {
	return game.EndTime
}

func (game *LiveGame) SetEndTime(time int64) {
	game.EndTime = time
}

func (game *LiveGame) GetIsOver() bool {
	return game.IsOver
}

func (game *LiveGame) SetIsOver(over bool) {
	game.IsOver = over
}

func (game *LiveGame) GetRoundID() string {
	return game.RoundID
}

func (game *LiveGame) SetRoundID(id string) {
	game.RoundID = id
}

// Create 创建游戏
func (game *LiveGame) Create(createGame *Message.CreateGameC2S) {
	// mapId := createGame.MapId
	// mapConfig := configs.GetMapConfig().GetMapConfig(int(mapId))
	// if mapConfig != nil {
	// 	game.PlayTime = int(mapConfig.Time)
	// }
	game.ScorePool = game.Room.AnchorData.ScorePool

	if !game.IsSessionNil() {
		Request.CreateGameRequest(game.GetSession(), game.GetRoomId(), game.ScorePool, 0, 0, 0)
	}
}

// InitGameOver 初始化游戏结束
func (game *LiveGame) InitGameOver() {
	game.PlayState = PLAY_STATE_READY

	game.RoundID = GetRoomMgr().GetRoomRoundID()
	core.LogInfo("InitGameOver", game.GetRoomId(), "RoundID:", game.RoundID)
	if network.Platform == network.Platform_DouYin {
		myCallback := func(re any) {
			suc := re.(bool)
			if !suc {
				if !game.IsSessionNil() {
					Request.ErrorCodeRequest(game.GetSession(), 0, 0, "初始化游戏失败，请重启直播间")
				}
				return
			} else {
				if !game.IsSessionNil() {
					Request.InitGameOverRequest(game.GetSession())
				}
			}
		}
		GetLiveInfoMgr().AddDYLiveReq(game.GetRoomPlatformId(), myCallback, nil, bytedance.LIVE_MSG_TYPE_GIFT, Type_LiveStart)
		GetLiveInfoMgr().AddDYLiveReq(game.GetRoomPlatformId(), nil, nil, bytedance.LIVE_MSG_TYPE_COMMENT, Type_LiveStart)
		GetLiveInfoMgr().AddDYLiveReq(game.GetRoomPlatformId(), nil, nil, bytedance.LIVE_MSG_TYPE_LIKE, Type_LiveStart)
		GetLiveInfoMgr().AddDYLiveReq(game.GetRoomPlatformId(), nil, nil, bytedance.LIVE_MSG_TYPE_FANS, Type_LiveStart)
	} else {
		myCallback := func(re any) {
			result := re.(bool)
			if !result {
				if !game.IsSessionNil() {
					Request.ErrorCodeRequest(game.GetSession(), 0, 0, "初始化游戏失败，请重启直播间")
				}
				return
			} else {
				if !game.IsSessionNil() {
					Request.InitGameOverRequest(game.GetSession())
				}
			}
		}
		GetLiveInfoMgr().AddKSLiveReq(game.GetRoomPlatformId(), myCallback, nil, Type_Round, "start", &kuaishou.RoundData{
			RoundId:   game.RoundID,
			RoundType: "singleNotGroup",
		})

		GetLiveInfoMgr().AddKSLiveReq(game.GetRoomPlatformId(), nil, nil, Type_TopGift, "top", nil)
		GetLiveInfoMgr().AddKSLiveReq(game.GetRoomPlatformId(), nil, nil, Type_QComment, game.RoundID, nil)
	}
}

// GameStart 开始游戏
func (game *LiveGame) GameStart() {
	game.StartTime = core.TimeServer().Unix()
	// game.EndTime = game.StartTime + int64(game.PlayTime)
	game.PlayState = PLAY_STATE_START
	game.IsOver = false

	if !game.IsSessionNil() {
		Request.GameStartRequest(game.GetSession())
	}
}

// GameOver 游戏结束
func (game *LiveGame) GameOver(gameOverC2S *Message.GameOverC2S) {
	game.PlayState = PLAY_STATE_END

	GetLiveTopMgr().RankSort()

	rank := new(network.RankList)
	Settlement := make([]*Message.PlayerSettlementDTO, 0)

	if !game.IsSessionNil() {
		Request.GameOverRequest(game.GetSession(), Settlement)
	}

	game.Reset(false)
	//! 行为日志-直播结束
	db.GetLogMgr().SqlBeLog(&db.SQL_BeLog{
		Id:     0,
		Time:   core.TimeServer().Unix(),
		Type:   core.LOG_LIVE_STOP_GAME,
		Value:  game.GetRoomId(),
		Param1: 0,
		Param2: 0,
		Dec:    "结束游戏-" + game.GetRoomPlatformId(),
		Cur:    0,
		Param3: 0,
		Level:  0,
		Vip:    0,
		Fight:  0,
	})

	if network.Platform == network.Platform_DouYin {
		GetLiveInfoMgr().AddDYLiveReq(game.GetRoomPlatformId(), nil, nil, bytedance.LIVE_MSG_TYPE_GIFT, Type_LiveStop)
		GetLiveInfoMgr().AddDYLiveReq(game.GetRoomPlatformId(), nil, nil, bytedance.LIVE_MSG_TYPE_COMMENT, Type_LiveStop)
		GetLiveInfoMgr().AddDYLiveReq(game.GetRoomPlatformId(), nil, nil, bytedance.LIVE_MSG_TYPE_LIKE, Type_LiveStop)
		GetLiveInfoMgr().AddDYLiveReq(game.GetRoomPlatformId(), nil, nil, bytedance.LIVE_MSG_TYPE_FANS, Type_LiveStop)
		rankList := make([]*network.RankList, 0)
		rankList = append(rankList, rank)
		bytedance.GameInfo(game.GetRoomPlatformId(), game.Room.AnchorData.OpenId, game.RoundID, game.StartTime, game.EndTime, 1, rankList)
	} else {
		GetLiveInfoMgr().AddKSLiveReq(game.GetRoomPlatformId(), nil, nil, Type_Round, "stop", &kuaishou.RoundData{
			RoundId:   game.RoundID,
			RoundType: "singleNotGroup",
			RoundResult: &kuaishou.RoundResult{
				SingleNotGroupRoundResult: "winPlayerName",
			},
		})
	}
}

// PlayerKillCount 玩家击杀数量
func (game *LiveGame) PlayerKillCount(playerKillCount *Message.PlayerKillCountC2S) {

}

// AddComment 添加评论
func (game *LiveGame) AddComment(comment []byte) {
	if network.Platform == network.Platform_KuaiShou {
		core.LogInfo("AddComment:", string(comment))
	}
	game.MapComment.Append(comment)
}

// AddGift 添加礼物
func (game *LiveGame) AddGift(comment []*payload.PayloadData, msgId string) {
	for i := 0; i < len(comment); i++ {
		comment[i].UMsgId = msgId
		GetGiftRecordMgr().CreateRecord(game.GetRoomId(), game.GetAnchorId(), comment[i])
	}
	game.MapGift.Append(comment)
}

// AddFans 添加粉丝
func (game *LiveGame) AddFans(comment []*payload.PayloadData, msgId string) {
	for i := 0; i < len(comment); i++ {
		comment[i].UMsgId = msgId
		GetGiftRecordMgr().CreateRecord(game.GetRoomId(), game.GetAnchorId(), comment[i])
	}
	game.MapFans.Append(comment)
}

// AddLike 添加点赞
func (game *LiveGame) AddLike(comment []byte) {
	game.MapLike.Append(comment)
}

// ExComment 处理评论事件
func (game *LiveGame) ExComment() {
	// 如果游戏暂停，不处理评论事件
	if game.IsPaused {
		return
	}

	count := game.MapComment.Len()
	count = min(count, EX_COMMENT_PERSECOND)
	if network.Platform == network.Platform_DouYin {
		for i := 0; i < count; i++ {
			comment, err := game.MapComment.Get(0)
			if err == nil {
				var comments []*bytedance.Payload
				err1 := json.Unmarshal(comment, &comments)
				if err1 == nil {
					var ret = payload.ConvertDYPayloadData(comments)
					for i := 0; i < len(ret); i++ {
						game.DoComment(ret[i])
					}
					game.MapComment.RemoveAt(0)
				}
			}
		}
	} else {
		for i := 0; i < count; i++ {
			comment, err := game.MapComment.Get(0)
			if err == nil {
				var comments []*kuaishou.Payload
				err1 := json.Unmarshal(comment, &comments)
				if err1 == nil {
					var ret = payload.ConvertKSPayloadData(comments)
					for i := 0; i < len(ret); i++ {
						game.DoComment(ret[i])
					}
					game.MapComment.RemoveAt(0)
				}
			}
		}
	}
}

// ExGift 处理礼物事件
func (game *LiveGame) ExGift() {
	// 如果游戏暂停，不处理礼物事件
	if game.IsPaused {
		return
	}

	count := game.MapGift.Len()
	count = min(count, EX_GIFT_PERSECOND)
	for i := 0; i < count; i++ {
		comments, err := game.MapGift.Get(0)
		if err == nil {
			for i := 0; i < len(comments); i++ {
				game.DoGift(comments[i])
			}
			game.MapGift.RemoveAt(0)
		}
	}
}

// ExFans 处理粉丝事件
func (game *LiveGame) ExFans() {
	// 如果游戏暂停，不处理粉丝事件
	if game.IsPaused {
		return
	}

	count := game.MapFans.Len()
	count = min(count, EX_FANS_PERSECOND)
	for i := 0; i < count; i++ {
		comments, err := game.MapFans.Get(0)
		if err == nil {
			for i := 0; i < len(comments); i++ {
				game.DoFans(comments[i])
			}
			game.MapFans.RemoveAt(0)
		}
	}
}

// ExLike 处理点赞事件
func (game *LiveGame) ExLike() {
	// 如果游戏暂停，不处理点赞事件
	if game.IsPaused {
		return
	}

	if game.GetSession() == nil {
		return
	}
	count := game.MapLike.Len()
	count = min(count, EX_LIKE_PERSECOND)
	if network.Platform == network.Platform_DouYin {
		for i := 0; i < count; i++ {
			comment, err := game.MapLike.Get(0)
			if err == nil {
				var comments []*bytedance.Payload
				err1 := json.Unmarshal(comment, &comments)
				if err1 == nil {
					var ret = payload.ConvertDYPayloadData(comments)
					for i := 0; i < len(ret); i++ {
						game.DoLike(ret[i])
					}
					game.MapLike.RemoveAt(0)
				}
			}
		}
	} else {
		for i := 0; i < count; i++ {
			comment, err := game.MapLike.Get(0)
			if err == nil {
				var comments []*kuaishou.Payload
				err1 := json.Unmarshal(comment, &comments)
				if err1 == nil {
					var ret = payload.ConvertKSPayloadData(comments)
					for i := 0; i < len(ret); i++ {
						game.DoLike(ret[i])
					}
					game.MapLike.RemoveAt(0)
				}
			}
		}
	}
}
