package announcement

import (
	"fmt"
	"sync"
	"sync/atomic"
	"time"
	"zone/game/models"
	"zone/lib/core"
	"zone/lib/db"
	"zone/lib/storage"
)

// MySQLCleanupManager MySQL清理管理器
type MySQLCleanupManager struct {
	// 配置参数
	cleanupInterval   time.Duration // 清理间隔
	retentionPeriod   time.Duration // 保留期限（过期后多久删除）
	batchSize         int           // 批量删除大小
	enableAutoCleanup bool          // 是否启用自动清理

	// 统计信息
	totalCleaned    int64 // 总清理数量
	lastCleanupTime time.Time
	cleanupCount    int64 // 清理次数

	// 控制
	stopChan  chan bool
	isRunning bool
	mutex     sync.RWMutex
}

// MySQLCleanupStats MySQL清理统计
type MySQLCleanupStats struct {
	TotalCleaned    int64         `json:"total_cleaned"`
	LastCleanupTime time.Time     `json:"last_cleanup_time"`
	CleanupCount    int64         `json:"cleanup_count"`
	CleanupDuration time.Duration `json:"cleanup_duration"`
	DeletedCount    int64         `json:"deleted_count"`
	ErrorCount      int64         `json:"error_count"`
}

// NewMySQLCleanupManager 创建MySQL清理管理器
func NewMySQLCleanupManager() *MySQLCleanupManager {
	return &MySQLCleanupManager{
		cleanupInterval:   30 * time.Minute, // 每30分钟清理一次
		retentionPeriod:   24 * time.Hour,   // 过期后24小时删除
		batchSize:         100,              // 每批删除100条
		enableAutoCleanup: true,             // 默认启用自动清理
		stopChan:          make(chan bool, 1),
		isRunning:         false,
	}
}

// Start 启动MySQL清理管理器
func (m *MySQLCleanupManager) Start() {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if m.isRunning {
		core.LogInfo("MySQL清理管理器已在运行")
		return
	}

	if !m.enableAutoCleanup {
		core.LogInfo("MySQL自动清理已禁用")
		return
	}

	m.isRunning = true
	go m.cleanupLoop()

	core.LogInfo("公告清理管理器启动成功",
		"清理间隔:", m.cleanupInterval,
		"保留期限:", m.retentionPeriod,
		"批量大小:", m.batchSize)
}

// Stop 停止MySQL清理管理器
func (m *MySQLCleanupManager) Stop() {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if !m.isRunning {
		return
	}

	m.isRunning = false
	select {
	case m.stopChan <- true:
	default:
	}

	core.LogInfo("MySQL清理管理器已停止")
}

// CleanupExpiredAnnouncements 清理过期公告
func (m *MySQLCleanupManager) CleanupExpiredAnnouncements() *MySQLCleanupStats {
	startTime := time.Now()
	stats := &MySQLCleanupStats{
		LastCleanupTime: startTime,
	}

	if !m.enableAutoCleanup {
		core.LogDebug("MySQL自动清理已禁用，跳过清理")
		return stats
	}

	// core.LogInfo("开始清理过期公告")

	// 计算清理截止时间（过期时间 + 保留期限）
	cutoffTime := core.TimeServer().Add(-m.retentionPeriod)
	cutoffTimestamp := cutoffTime.Unix()

	// 查找过期公告
	expiredAnnouncements := m.findExpiredAnnouncements(cutoffTimestamp)
	if len(expiredAnnouncements) == 0 {
		// core.LogDebug("没有找到需要清理的过期公告")
		stats.CleanupDuration = time.Since(startTime)
		return stats
	}

	core.LogInfo("找到过期公告", "数量:", len(expiredAnnouncements), "截止时间:", cutoffTime.Format("2006-01-02 15:04:05"))

	// 批量删除过期公告
	deletedCount := m.batchDeleteAnnouncements(expiredAnnouncements)

	// 更新统计信息
	atomic.AddInt64(&m.totalCleaned, int64(deletedCount))
	atomic.AddInt64(&m.cleanupCount, 1)
	m.lastCleanupTime = startTime

	stats.TotalCleaned = atomic.LoadInt64(&m.totalCleaned)
	stats.CleanupCount = atomic.LoadInt64(&m.cleanupCount)
	stats.DeletedCount = int64(deletedCount)
	stats.CleanupDuration = time.Since(startTime)

	core.LogInfo("过期公告清理完成",
		"删除数量:", deletedCount,
		"总清理数量:", stats.TotalCleaned,
		"耗时:", stats.CleanupDuration)

	return stats
}

// findExpiredAnnouncements 查找过期公告
func (m *MySQLCleanupManager) findExpiredAnnouncements(cutoffTimestamp int64) []models.AnnouncementDB {
	query := fmt.Sprintf(`
		SELECT * FROM %s 
		WHERE end_time <= %d
		ORDER BY end_time ASC
		LIMIT %d`,
		storage.TABLE_Announcement,
		cutoffTimestamp,
		m.batchSize*2) // 查询更多以便批量处理

	// core.LogDebug("查找过期公告", "截止时间戳:", cutoffTimestamp, "查询:", query)

	var announcementTemplate models.AnnouncementDB
	results := db.GetDBMgr().DBUser.GetAllData(query, &announcementTemplate)
	if results == nil {
		core.LogDebug("没有找到过期公告")
		return nil
	}

	// 转换结果
	announcements := make([]models.AnnouncementDB, len(results))
	for i, result := range results {
		announcements[i] = *result.(*models.AnnouncementDB)
	}

	return announcements
}

// batchDeleteAnnouncements 批量删除公告
func (m *MySQLCleanupManager) batchDeleteAnnouncements(announcements []models.AnnouncementDB) int {
	if len(announcements) == 0 {
		return 0
	}

	deletedCount := 0

	// 分批删除
	for i := 0; i < len(announcements); i += m.batchSize {
		end := i + m.batchSize
		if end > len(announcements) {
			end = len(announcements)
		}

		batch := announcements[i:end]
		batchDeleted := m.deleteBatch(batch)
		deletedCount += batchDeleted

		// 避免过于频繁的数据库操作
		if len(batch) == m.batchSize {
			time.Sleep(100 * time.Millisecond)
		}
	}

	return deletedCount
}

// deleteBatch 删除一批公告
func (m *MySQLCleanupManager) deleteBatch(announcements []models.AnnouncementDB) int {
	if len(announcements) == 0 {
		return 0
	}

	// 构建批量删除SQL
	ids := make([]string, len(announcements))
	for i, ann := range announcements {
		ids[i] = fmt.Sprintf("%d", ann.Id)
	}

	deleteQuery := fmt.Sprintf(`DELETE FROM %s WHERE id IN (%s)`,
		storage.TABLE_Announcement,
		joinStrings(ids, ","))

	core.LogDebug("批量删除公告", "数量:", len(announcements), "查询:", deleteQuery)

	_, rowsAffected, success := db.GetDBMgr().DBUser.Exec(deleteQuery)
	if !success {
		core.LogError("批量删除公告失败", "查询:", deleteQuery)
		return 0
	}

	core.LogInfo("批量删除公告成功", "删除数量:", rowsAffected)
	return int(rowsAffected)
}

// joinStrings 连接字符串数组
func joinStrings(strs []string, sep string) string {
	if len(strs) == 0 {
		return ""
	}
	if len(strs) == 1 {
		return strs[0]
	}

	result := strs[0]
	for i := 1; i < len(strs); i++ {
		result += sep + strs[i]
	}
	return result
}

// GetStats 获取清理统计信息
func (m *MySQLCleanupManager) GetStats() map[string]interface{} {
	return map[string]interface{}{
		"total_cleaned":       atomic.LoadInt64(&m.totalCleaned),
		"cleanup_count":       atomic.LoadInt64(&m.cleanupCount),
		"last_cleanup_time":   m.lastCleanupTime.Format("2006-01-02 15:04:05"),
		"cleanup_interval":    m.cleanupInterval.String(),
		"retention_period":    m.retentionPeriod.String(),
		"batch_size":          m.batchSize,
		"enable_auto_cleanup": m.enableAutoCleanup,
		"is_running":          m.isRunning,
	}
}

// SetConfig 设置配置参数
func (m *MySQLCleanupManager) SetConfig(cleanupInterval, retentionPeriod time.Duration, batchSize int, enableAutoCleanup bool) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	m.cleanupInterval = cleanupInterval
	m.retentionPeriod = retentionPeriod
	m.batchSize = batchSize
	m.enableAutoCleanup = enableAutoCleanup

	core.LogInfo("MySQL清理配置已更新",
		"清理间隔:", cleanupInterval,
		"保留期限:", retentionPeriod,
		"批量大小:", batchSize,
		"启用自动清理:", enableAutoCleanup)
}

// ForceCleanup 强制执行清理
func (m *MySQLCleanupManager) ForceCleanup() *MySQLCleanupStats {
	core.LogInfo("强制执行MySQL清理")
	return m.CleanupExpiredAnnouncements()
}

// cleanupLoop 清理循环
func (m *MySQLCleanupManager) cleanupLoop() {
	ticker := time.NewTicker(m.cleanupInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			m.CleanupExpiredAnnouncements()
		case <-m.stopChan:
			core.LogInfo("MySQL清理管理器清理循环退出")
			return
		}
	}
}

// IsAvailable 检查清理管理器是否可用
func (m *MySQLCleanupManager) IsAvailable() bool {
	// 检查数据库连接是否可用
	return db.GetDBMgr() != nil
}
