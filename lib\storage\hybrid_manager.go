package storage

import (
	"context"
	"fmt"
	"path/filepath"
	"sync"
	"time"
	"zone/lib/core"
)

// HybridTaskManager 混合任务管理器
// 结合内存和Redis的优势，提供高性能的任务去重功能
type HybridTaskManager struct {
	// 内存缓存层 - 用于高频访问
	memoryCache *sync.Map

	// Redis客户端接口 - 用于持久化和跨服务器共享
	redisEnabled bool
	redisClient  RedisInterface

	// 配置参数
	keyPrefix      string
	defaultTTL     time.Duration
	syncInterval   time.Duration
	maxMemoryItems int

	// 同步控制
	ctx        context.Context
	cancel     context.CancelFunc
	syncTicker *time.Ticker

	// 统计信息
	stats      *TaskManagerStats
	statsMutex sync.RWMutex
}

// RedisInterface Redis客户端接口，便于测试和替换
type RedisInterface interface {
	SetNX(ctx context.Context, key string, value interface{}, expiration time.Duration) (bool, error)
	Get(ctx context.Context, key string) (string, error)
	Del(ctx context.Context, keys ...string) (int64, error)
	Scan(ctx context.Context, cursor uint64, match string, count int64) ([]string, uint64, error)
	Ping(ctx context.Context) error
}

// TaskManagerStats 任务管理器统计信息
type TaskManagerStats struct {
	MemoryHits      int64 // 内存命中次数
	MemoryMisses    int64 // 内存未命中次数
	RedisHits       int64 // Redis命中次数
	RedisMisses     int64 // Redis未命中次数
	RedisErrors     int64 // Redis错误次数
	SyncOperations  int64 // 同步操作次数
	TotalChecks     int64 // 总检查次数
	BatchOperations int64 // 批量操作次数
}

// NewHybridTaskManager 创建混合任务管理器
func NewHybridTaskManager(redisClient RedisInterface) *HybridTaskManager {
	ctx, cancel := context.WithCancel(context.Background())

	manager := &HybridTaskManager{
		memoryCache:    &sync.Map{},
		redisEnabled:   redisClient != nil,
		redisClient:    redisClient,
		keyPrefix:      "live_task",
		defaultTTL:     time.Hour * 2,
		syncInterval:   time.Minute * 5,
		maxMemoryItems: 10000, // 最大内存缓存项数
		ctx:            ctx,
		cancel:         cancel,
		stats:          &TaskManagerStats{},
	}

	// 启动后台同步任务
	if manager.redisEnabled {
		manager.startBackgroundSync()
	}

	return manager
}

// CheckAndMarkTask 检查并标记任务（通用方法）
func (h *HybridTaskManager) CheckAndMarkTask(taskType string, roomId int, msgId string) bool {
	h.updateStats(func(s *TaskManagerStats) { s.TotalChecks++ })

	key := fmt.Sprintf("%s:%s:%d:%s", h.keyPrefix, taskType, roomId, msgId)

	// 1. 首先检查内存缓存
	if _, exists := h.memoryCache.Load(key); exists {
		h.updateStats(func(s *TaskManagerStats) { s.MemoryHits++ })
		return false // 任务已存在
	}

	h.updateStats(func(s *TaskManagerStats) { s.MemoryMisses++ })

	// 2. 检查Redis（如果启用）
	if h.redisEnabled {
		if exists, err := h.checkRedisTask(key); err == nil {
			if exists {
				// Redis中存在，同步到内存缓存
				h.memoryCache.Store(key, time.Now())
				h.updateStats(func(s *TaskManagerStats) { s.RedisHits++ })
				return false
			}
			h.updateStats(func(s *TaskManagerStats) { s.RedisMisses++ })
		} else {
			h.updateStats(func(s *TaskManagerStats) { s.RedisErrors++ })
			core.LogError("Redis检查任务失败:", err, "key:", key)
		}
	}

	// 3. 任务不存在，标记为已处理
	now := time.Now()
	h.memoryCache.Store(key, now)

	// 4. 异步同步到Redis
	if h.redisEnabled {
		go h.syncToRedis(key, now)
	}

	// 5. 检查内存缓存大小，必要时清理
	h.checkAndCleanMemory()

	return true // 新任务
}

// 具体任务类型的便捷方法
func (h *HybridTaskManager) CheckAndMarkGiftTask(roomId int, msgId string) bool {
	return h.CheckAndMarkTask("gift", roomId, msgId)
}

func (h *HybridTaskManager) CheckAndMarkCommentTask(roomId int, msgId string) bool {
	return h.CheckAndMarkTask("comment", roomId, msgId)
}

func (h *HybridTaskManager) CheckAndMarkLikeTask(roomId int, msgId string) bool {
	return h.CheckAndMarkTask("like", roomId, msgId)
}

func (h *HybridTaskManager) CheckAndMarkFansTask(roomId int, msgId string) bool {
	return h.CheckAndMarkTask("fans", roomId, msgId)
}

// checkRedisTask 检查Redis中的任务
func (h *HybridTaskManager) checkRedisTask(key string) (bool, error) {
	if !h.redisEnabled {
		return false, nil
	}

	// 使用SetNX检查并设置
	result, err := h.redisClient.SetNX(h.ctx, key, 1, h.defaultTTL)
	if err != nil {
		return false, err
	}

	// SetNX返回true表示键不存在（新任务），false表示键已存在
	return !result, nil
}

// syncToRedis 异步同步到Redis
func (h *HybridTaskManager) syncToRedis(key string, timestamp time.Time) {
	if !h.redisEnabled {
		return
	}

	_, err := h.redisClient.SetNX(h.ctx, key, timestamp.Unix(), h.defaultTTL)
	if err != nil {
		core.LogError("同步任务到Redis失败:", err, "key:", key)
		h.updateStats(func(s *TaskManagerStats) { s.RedisErrors++ })
	}
}

// checkAndCleanMemory 检查并清理内存缓存
func (h *HybridTaskManager) checkAndCleanMemory() {
	// 简单的计数检查
	count := 0
	h.memoryCache.Range(func(key, value interface{}) bool {
		count++
		return count < h.maxMemoryItems
	})

	if count >= h.maxMemoryItems {
		go h.cleanOldMemoryItems()
	}
}

// cleanOldMemoryItems 清理旧的内存项
func (h *HybridTaskManager) cleanOldMemoryItems() {
	cutoff := time.Now().Add(-h.defaultTTL)
	keysToDelete := make([]interface{}, 0)

	h.memoryCache.Range(func(key, value interface{}) bool {
		if timestamp, ok := value.(time.Time); ok {
			if timestamp.Before(cutoff) {
				keysToDelete = append(keysToDelete, key)
			}
		}
		return true
	})

	// 删除过期项
	for _, key := range keysToDelete {
		h.memoryCache.Delete(key)
	}

	if len(keysToDelete) > 0 {
		core.LogDebug("清理过期内存缓存项:", len(keysToDelete))
	}
}

// startBackgroundSync 启动后台同步任务
func (h *HybridTaskManager) startBackgroundSync() {
	h.syncTicker = time.NewTicker(h.syncInterval)

	go func() {
		defer h.syncTicker.Stop()

		for {
			select {
			case <-h.ctx.Done():
				return
			case <-h.syncTicker.C:
				h.performBackgroundSync()
			}
		}
	}()
}

// performBackgroundSync 执行后台同步
func (h *HybridTaskManager) performBackgroundSync() {
	h.updateStats(func(s *TaskManagerStats) { s.SyncOperations++ })

	// 清理过期的内存项
	h.cleanOldMemoryItems()

	// 健康检查Redis连接
	if h.redisEnabled {
		if err := h.redisClient.Ping(h.ctx); err != nil {
			core.LogError("Redis连接检查失败:", err)
			h.updateStats(func(s *TaskManagerStats) { s.RedisErrors++ })
		}
	}
}

// ClearRoomTasks 清理房间任务
func (h *HybridTaskManager) ClearRoomTasks(roomId int) error {
	pattern := fmt.Sprintf("%s:*:%d:*", h.keyPrefix, roomId)

	// 清理内存缓存
	keysToDelete := make([]interface{}, 0)
	h.memoryCache.Range(func(key, value interface{}) bool {
		if keyStr, ok := key.(string); ok {
			if matched, _ := filepath.Match(pattern, keyStr); matched {
				keysToDelete = append(keysToDelete, key)
			}
		}
		return true
	})

	for _, key := range keysToDelete {
		h.memoryCache.Delete(key)
	}

	// 清理Redis
	if h.redisEnabled {
		keys, _, err := h.redisClient.Scan(h.ctx, 0, pattern, 1000)
		if err != nil {
			return fmt.Errorf("扫描Redis键失败: %v", err)
		}

		if len(keys) > 0 {
			_, err := h.redisClient.Del(h.ctx, keys...)
			if err != nil {
				return fmt.Errorf("删除Redis键失败: %v", err)
			}
		}
	}

	core.LogDebug("清理房间任务完成，房间ID:", roomId, "内存清理:", len(keysToDelete))
	return nil
}

// GetStats 获取统计信息
func (h *HybridTaskManager) GetStats() TaskManagerStats {
	h.statsMutex.RLock()
	defer h.statsMutex.RUnlock()
	return *h.stats
}

// updateStats 更新统计信息
func (h *HybridTaskManager) updateStats(updater func(*TaskManagerStats)) {
	h.statsMutex.Lock()
	defer h.statsMutex.Unlock()
	updater(h.stats)
}

// LogStats 记录统计信息
func (h *HybridTaskManager) LogStats() {
	stats := h.GetStats()

	core.LogInfo("=== 混合任务管理器统计 ===")
	core.LogInfo("总检查次数:", stats.TotalChecks)
	core.LogInfo("内存命中率:", fmt.Sprintf("%.2f%%", float64(stats.MemoryHits)/float64(stats.TotalChecks)*100))

	if h.redisEnabled {
		core.LogInfo("Redis命中率:", fmt.Sprintf("%.2f%%", float64(stats.RedisHits)/float64(stats.RedisMisses+stats.RedisHits)*100))
		core.LogInfo("Redis错误次数:", stats.RedisErrors)
	}

	core.LogInfo("同步操作次数:", stats.SyncOperations)
	core.LogInfo("=== 统计结束 ===")
}

// Close 关闭管理器
func (h *HybridTaskManager) Close() error {
	h.cancel()
	if h.syncTicker != nil {
		h.syncTicker.Stop()
	}
	return nil
}
