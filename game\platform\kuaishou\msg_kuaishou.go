package kuaishou

import (
	"crypto/md5"
	"encoding/json"
	"fmt" // 新增url包处理参数编码
	"sort"
	"strconv"
	"strings"
	"time"
	"zone/lib/core"
	"zone/lib/httpclient"
	"zone/lib/network"
	"zone/lib/utils"
)

const (
	LIVE_MSG_TYPE_COMMENT = "liveComment" //! 直播评论
	LIVE_MSG_TYPE_GIFT    = "giftSend"    //! 直播礼物
	LIVE_MSG_TYPE_LIKE    = "liveLike"    //! 直播点赞
	LIVE_MSG_TYPE_FANS    = "fansGroupJoin"

	LIVE_MSG_COMMENT_SECRET = "ayfsabc321"
	LIVE_MSG_GIFT_SECRET    = "ayfsabc321"
	LIVE_MSG_LIKE_SECRET    = "ayfsabc321"

	LIVE_APP_ID     = "ks661184721154144257"
	LIVE_SECRET     = "Wc1aMKPZlkcjj8mGq2pXMA"
	LIVE_GRANT_TYPE = "client_credentials"

	// API URL 常量
	URLGetToken     = "https://open.kuaishou.com/oauth2/access_token"
	URLLiveBind     = "https://open.kuaishou.com/openapi/developer/live/smallPlay/bind"
	URLRoundAPI     = "https://open.kuaishou.com/openapi/developer/live/smallPlay/round"
	URLQuickComment = "https://open.kuaishou.com/openapi/developer/live/data/interactive/start"
	URLSetGift      = "https://open.kuaishou.com/openapi/developer/live/smallPlay/gift"
	URLQueryGifts   = "https://open.kuaishou.com/openapi/developer/live/data/interactive/pushdata/query"
	URLReceiveAck   = "https://open.kuaishou.com/openapi/developer/live/data/interactive/ack/receive"
	URLShowAck      = "https://open.kuaishou.com/openapi/developer/live/data/interactive/ack/show"
)

var excludedSignatureParams = map[string]bool{
	"sign":         true, // 必须排除的签名参数
	"access_token": true, // 鉴权参数单独传输
}

type ResultHeader struct {
	Result    int    `json:"result"`
	ErrorMsg  string `json:"errorMsg"`
	Timestamp int64  `json:"timestamp"`
}

type AckResponse struct {
	Result   int    `json:"result"`
	ErrorMsg string `json:"errorMsg"`
}

// 修改请求结构体标签以匹配快手文档
type JsTokenReq struct {
	AppId     string `json:"app_id"`
	Secret    string `json:"app_secret"`
	GrantType string `json:"grant_type"`
}

type JsTokenResultData struct {
	Result      int    `json:"result"`
	AccessToken string `json:"access_token"` // 直接顶层字段
	ExpiresIn   int64  `json:"expires_in"`
	TokenType   string `json:"token_type"` // 修正字段类型为string
}

var GetAccessTokenTimer *time.Timer = nil

func DoGetAccessToken() *time.Timer {

	timer := time.NewTimer(time.Second * time.Duration(3600))
	go func() {
		<-timer.C
		GetAccessToken()
	}()
	return timer
}

func GetAccessToken() {
	if network.IsTestServer {
		return
	}

	// 构造GET请求参数
	params := map[string]string{
		"app_id":     LIVE_APP_ID,
		"app_secret": LIVE_SECRET,
		"grant_type": LIVE_GRANT_TYPE,
	}

	// 使用通用HTTP请求函数构建URL
	reqUrl := httpclient.BuildQueryURL(URLGetToken, params)

	// 使用通用HTTP请求函数
	opts := httpclient.HTTPRequestOptions{
		Method:  "GET",
		URL:     reqUrl,
		Headers: map[string]string{},
	}
	opts.Headers["content-type"] = "x-www-form-urlencoded"

	bodyData, err := httpclient.DoHTTPRequest(opts)
	if err != nil {
		core.LogError("执行GetAccessToken请求失败:", err)
		return
	}

	var ret JsTokenResultData
	if err := httpclient.ParseJSONResponse(bodyData, &ret); err != nil {
		core.LogError("解析GetAccessToken响应失败:", err)
		return
	}

	// 保持原错误处理逻辑
	if ret.Result != 1 {
		errorMsg := getError(ret.Result)
		core.LogError("获取Token失败 错误码:", ret.Result, "错误信息:", errorMsg)
		return
	}

	// 保持原Token处理逻辑
	network.AccessToken = ret.AccessToken
	network.ExpiresIn = ret.ExpiresIn
	core.LogInfo("kuaishou GetAccessToken:", network.AccessToken, "network.ExpiresIn:", network.ExpiresIn, "获取时间：", utils.GetTimerString())

	if GetAccessTokenTimer != nil {
		GetAccessTokenTimer.Stop()
	}
	GetAccessTokenTimer = DoGetAccessToken()
}

// 新增错误码映射
func getError(code int) string {
	switch code {
	case 100200100:
		return "参数错误：请检查app_id和secret格式"
	case 100200101:
		return "验证失败：请确认app_id和secret有效性"
	default:
		return "未知错误"
	}
}

// 请求结构体
type BindRequest struct {
	RoomCode   string `json:"roomCode"` // 修正为文档要求的字段名
	Timestamp  int64  `json:"timestamp"`
	ModuleType string `json:"moduleType"`
	ActionType string `json:"actionType"`
	Data       string `json:"data"`
	Sign       string `json:"sign"`
}

type DataInfo struct {
	AnchorOpenId string `json:"userId"`
	NickName     string `json:"userName"`
	AvatarUrl    string `json:"headUrl"`
	Status       int    `json:"status"`
	RoomCode     string `json:"roomCode"`
}

// 响应结构体
type BindResponse struct {
	Result   int    `json:"result"`
	ErrorMsg string `json:"errorMsg"`
	Data     string `json:"data"`
}

func KS_URL(url string) string {
	return fmt.Sprintf("%s?app_id=%s&access_token=%s", url, LIVE_APP_ID, network.AccessToken)
}

type BindData struct {
	CallBackUrl string `json:"callBackUrl"`
}

// 开始直播
func KS_Bind(roomCode, actionType string) (*DataInfo, error) {

	core.LogInfo(fmt.Sprintf("KS_Bind: roomCode:%s actionType:%s", roomCode, actionType))
	// 参数校验
	if roomCode == "" {
		core.LogError(fmt.Sprintf("必要参数为空: roomCode:%s", roomCode))
		return nil, fmt.Errorf("KS_Bind失败: roomCode为空")
	}
	// 测试环境数据处理
	if network.IsTestServer {
		dataInfo := &DataInfo{
			AnchorOpenId: "hfdusahgdufahsugh",
			NickName:     "测试主播",
			AvatarUrl:    "test-avatar-url",
			Status:       1,
			RoomCode:     roomCode,
		}
		return dataInfo, nil
	}
	curTime := core.TimeServer().UnixMilli()
	callBackData := &BindData{
		CallBackUrl: "",
	}
	datajson := utils.HF_JtoA(callBackData)
	// 初始化请求体（不含签名）
	reqData := map[string]interface{}{
		"roomCode":   roomCode,
		"timestamp":  curTime,
		"moduleType": "bind",
		"actionType": actionType,
		"data":       datajson,
	}

	// 生成签名（关键修改点）
	actualSign := generateSignature(reqData)

	// 构造最终请求体（包含签名）
	finalReq := &BindRequest{
		RoomCode:   roomCode,
		Timestamp:  curTime,
		ModuleType: "bind",
		ActionType: actionType,
		Data:       datajson,
		Sign:       actualSign,
	}

	opts, err := httpclient.CreateJSONRequest("POST", KS_URL(URLLiveBind), finalReq)
	if err != nil {
		return nil, fmt.Errorf("创建LiveBind请求失败 %w", err)
	}

	bodyData, err := httpclient.DoHTTPRequest(opts)
	if err != nil {
		return nil, fmt.Errorf("执行LiveBind请求失败 %w", err)
	}

	// core.LogInfo(fmt.Sprintf("LiveBind请求结果: %s", string(bodyData)))
	// 解析响应
	var result BindResponse
	if err := json.Unmarshal(bodyData, &result); err != nil {
		return nil, fmt.Errorf("解析LiveBind请求失败 %w", err) //nil, fmt.Errorf("响应解析失败: %v", err)
	}

	// 业务逻辑错误处理
	if result.Result != 1 {
		return nil, fmt.Errorf("业务逻辑错误: code=%d msg=%s", result.Result, result.ErrorMsg)
	}
	var data DataInfo
	if err := json.Unmarshal([]byte(result.Data), &data); err != nil {
		return nil, fmt.Errorf("解析LiveBind Data失败 %w", err) //nil, fmt.Errorf("响应解析失败: %v", err)
	}
	data.RoomCode = roomCode
	// 成功日志（不再使用 fmt.Sprintf）
	core.LogInfo(fmt.Sprintf("直播开启成功[%s] 主播信息: %s(%s)", roomCode, data.NickName, data.AnchorOpenId))

	return &data, nil
}

func addSystemParams(paramMap map[string]string) {
	sysParams := map[string]string{
		"app_id": LIVE_APP_ID,
		// "timestamp": fmt.Sprintf("%d", core.TimeServer().UnixMilli()),
	}

	// 增加过滤逻辑
	for k, v := range sysParams {
		if !isExcludedParam(k) { // 新增系统参数过滤
			paramMap[k] = v
		}
	}
}

// 函数签名
// 修改 generateSignature 函数// 修改后的 generateSignature 函数（关键部分）
func generateSignature(bodyParams map[string]interface{}) string {
	paramMap := make(map[string]string)

	// 1. 系统级参数添加
	addSystemParams(paramMap)

	// // 2. URL参数处理
	// for param, values := range urlParams {
	// 	param = strings.TrimSpace(strings.ToLower(param))
	// 	if isExcludedParam(param) || len(values) == 0 || values[0] == "" {
	// 		continue
	// 	}
	// 	paramMap[param] = strings.TrimSpace(values[0])
	// }

	// 3. Body参数处理
	for param, value := range bodyParams {
		cleanParam := strings.TrimSpace(param)
		if isExcludedParam(cleanParam) || cleanParam == "" {
			continue
		}

		switch v := value.(type) {
		case string:
			if strVal := strings.TrimSpace(v); strVal != "" {
				paramMap[cleanParam] = strVal
			}
		case int:
			paramMap[cleanParam] = strconv.Itoa(v)
		case int32:
			paramMap[cleanParam] = strconv.FormatInt(int64(v), 10)
		case int64:
			paramMap[cleanParam] = strconv.FormatInt(v, 10)
		case bool:
			paramMap[cleanParam] = strconv.FormatBool(v)
		case float32:
			paramMap[cleanParam] = strconv.FormatFloat(float64(v), 'f', -1, 32)
		case float64:
			paramMap[cleanParam] = strconv.FormatFloat(v, 'f', -1, 64)
		default:
			if b, err := json.Marshal(v); err == nil && len(b) > 0 {
				paramMap[cleanParam] = string(b)
			}
		}
	}

	// // 新增：定义 buf 并写入待签名字符串
	// var buf bytes.Buffer
	// for k, v := range paramMap {
	// 	buf.WriteString(k)
	// 	buf.WriteString("=")
	// 	buf.WriteString(v)
	// }
	// buf.WriteString(appSecret) // 最后拼接appSecret

	return calcSign(paramMap) //strings.ToLower(calculateMD5(buf.String()))
}

func calcSign(signParamsMap map[string]string) string {
	// 1. 过滤空键和空值
	trimmedMap := make(map[string]string)
	for k, v := range signParamsMap {
		if isKeyValid(k) && isValueValid(v) {
			trimmedMap[k] = fmt.Sprintf("%v", v) // 统一转为字符串
		}
	}
	// 2. 获取排序后的键列表
	keys := make([]string, 0, len(trimmedMap))
	for k := range trimmedMap {
		keys = append(keys, k)
	}
	sort.Strings(keys) // 按字母顺序排序
	// 3. 构建待签名字符串
	var builder strings.Builder
	for i, k := range keys {
		if i > 0 {
			builder.WriteByte('&')
		}
		builder.WriteString(k)
		builder.WriteByte('=')
		builder.WriteString(trimmedMap[k])
	}
	signStr := builder.String() + LIVE_SECRET
	// core.LogInfo(fmt.Sprintf("LiveBind签名目标: %s", signStr))
	// 4. 计算 MD5
	hasher := md5.New()
	hasher.Write([]byte(signStr))
	return fmt.Sprintf("%x", hasher.Sum(nil))
}

// 辅助函数：验证键有效性
func isKeyValid(k string) bool {
	return strings.TrimSpace(k) != "" // 非空且不全是空格
}

// 辅助函数：验证值有效性
func isValueValid(v string) bool {
	return strings.TrimSpace(v) != ""
}

func isExcludedParam(param string) bool {
	// 兼容大小写及前后空格的参数名
	normalized := strings.ToLower(strings.TrimSpace(param))
	_, exists := excludedSignatureParams[normalized]
	return exists
}

type RoundResult struct {
	SingleNotGroupRoundResult string `json:"singleNotGroupRoundResult"`
}

type RoundData struct {
	RoundId     string       `json:"roundId"`
	RoundType   string       `json:"roundType"`
	RoundResult *RoundResult `json:"result"`
}

type RoundRequest struct {
	RoomCode   string `json:"roomCode"`
	TimeStamp  int64  `json:"timestamp"`
	Sign       string `json:"sign"`
	ModuleType string `json:"moduleType"`
	ActionType string `json:"actionType"`
	Data       string `json:"data"`
}

// func handleRoundRequest(action string, roomCode string, data *RoundData) (int, bool) {
// 	// 参数校验

// }

// 错误码映射表
var roundErrorCodes = map[int]string{
	21:     "data数据有误",
	220607: "下场观众总数校验失败",
	220608: "面板文案过长",
	220382: "主播控制面板文案风控未通过",
}

func getRoundErrorDesc(code int) string {
	if desc, exists := roundErrorCodes[code]; exists {
		return desc
	}
	return "未知错误"
}

func KS_Round(roomCode, actionType string, data *RoundData) bool {

	if roomCode == "" {
		core.LogError(fmt.Printf("空房间号检测到异常 roomCode=%s", roomCode))
		return false
	}

	if network.IsTestServer {
		return true
	}

	// 构造签名基础参数
	timestamp := core.TimeServer().UnixMilli()
	reqData := map[string]interface{}{
		"roomCode":   roomCode,
		"moduleType": "round",
		"actionType": actionType,
		"timestamp":  timestamp,
		"data":       utils.HF_JtoA(data),
	}

	// 生成API签名
	actualSign := generateSignature(reqData)

	// 构造最终请求体
	finalReq := &RoundRequest{
		RoomCode:   roomCode,
		TimeStamp:  timestamp,
		Sign:       actualSign,
		ModuleType: "round",
		ActionType: actionType,
		Data:       utils.HF_JtoA(data),
	}

	opts, err := httpclient.CreateJSONRequest("POST", KS_URL(URLRoundAPI), finalReq)
	if err != nil {
		return false
	}

	bodyData, err := httpclient.DoHTTPRequest(opts)
	if err != nil {
		return false
	}

	var result ResultHeader
	if err := json.Unmarshal(bodyData, &result); err != nil {
		core.LogError(fmt.Printf("响应解析异常 roomCode=%s body=%s err=%v",
			roomCode, string(bodyData), err))
		return false
	}

	// 处理业务错误
	if result.Result != 1 {
		errorDesc := getRoundErrorDesc(result.Result)
		core.LogError(fmt.Printf("业务操作失败 roomCode=%s code=%d desc=%s msg=%s",
			roomCode, result.Result, errorDesc, result.ErrorMsg))
		return false
	}

	core.LogInfo(fmt.Printf("操作成功 roomCode=%s action=%s timestamp=%d",
		roomCode, actionType, result.Timestamp))
	return true
}

// 请求与响应结构体
type GiftQueryRequest struct {
	RoomCode  string `json:"roomCode"`
	Timestamp int64  `json:"timestamp"`
	Sign      string `json:"sign"`
	PushType  string `json:"pushType"`
	Data      string `json:"data"` // 可传"{}"或带时间范围的JSON
}

type GiftQueryResponse struct {
	Result   int    `json:"result"`
	ErrorMsg string `json:"errorMsg"`
	DataList string `json:"dataList"` // 需要二次解析的JSON字符串
}

// 解析后的消息结构体(复用原有结构)
type GiftQueryItem struct {
	UniqueMessageID string             `json:"uniqueMessageId"`
	AuthorOpenID    string             `json:"authorOpenId"`
	RoomCode        string             `json:"roomCode"`
	PushType        string             `json:"pushType"`
	LiveTimestamp   int64              `json:"liveTimestamp"`
	Payload         []*PayloadDataInfo `json:"payload"`
}

// 消息回查接口实现
func QueryGiftMessages(roomCode string) ([]*GiftQueryItem, error) {
	// 参数校验
	if roomCode == "" {
		return nil, fmt.Errorf("无效的房间号")
	}

	if network.IsTestServer {
		return nil, nil
	}

	timestamp := core.TimeServer().UnixMilli()
	// 构造请求数据
	reqData := map[string]interface{}{
		"roomCode": roomCode,
		"pushType": "giftSend",
		// "app_id":    LIVE_APP_ID, // 参与签名
		"timestamp": timestamp,
	}

	// reqData["data"] = ""

	// 生成签名(参数按字母序排序)
	actualSign := generateSignature(reqData)

	// 构造请求体
	requestBody := &GiftQueryRequest{
		RoomCode:  roomCode,
		PushType:  "giftSend",
		Timestamp: timestamp,
		Data:      "",
		Sign:      actualSign,
	}

	opts, err := httpclient.CreateJSONRequest("POST", KS_URL(URLQueryGifts), requestBody)
	if err != nil {
		return nil, fmt.Errorf("创建Round请求失败: %v", err)
	}

	bodyData, err := httpclient.DoHTTPRequest(opts)
	if err != nil {
		return nil, fmt.Errorf("响应Round请求失败: %v", err)
	}

	// 解析响应
	var response GiftQueryResponse
	if err := json.Unmarshal(bodyData, &response); err != nil {
		return nil, fmt.Errorf("响应解析异常 roomCode=%s body=%s err=%v", roomCode, string(bodyData), err)
	}

	if response.Result != 1 {
		return nil, fmt.Errorf("接口错误[%d]: %s", response.Result, response.ErrorMsg)
	}

	// 解析dataList
	var items []*GiftQueryItem
	if err := json.Unmarshal([]byte(response.DataList), &items); err != nil {
		return nil, fmt.Errorf("消息解析失败: %v", err)
	}

	return items, nil
}

type KS_AckData struct {
	UniqueMessageId     string `json:"uniqueMessageId"`
	PushType            string `json:"pushType"`
	CpClientShowTime    int64  `json:"cpClientShowTime"`
	CpServerReceiveTime int64  `json:"cpServerReceiveTime"`
	CpClientReceiveTime int64  `json:"cpClientReceiveTime"`
}

type KS_Ack struct {
	RoomCode  string `json:"roomCode"`
	Timestamp int64  `json:"timestamp"`
	Sign      string `json:"sign"`
	AckType   string `json:"ackType"`
	Data      string `json:"data"`
}

// ReceiveAck
func KS_ReceiveAck(roomCode string, msgId string, cpTime int64) {
	// 参数校验
	if roomCode == "" {
		core.LogError("KS_ReceiveAck失败: roomCode为空")
		return
	}

	// 测试环境数据处理
	if network.IsTestServer {
		return
	}

	gifttime := int64(0)
	if v, loaded := GiftTimeRecord.LoadAndDelete(msgId); loaded {
		gifttime = v.(int64)
	} else {
		return
	}

	ack := &KS_AckData{
		UniqueMessageId:     msgId,
		PushType:            "giftSend",
		CpServerReceiveTime: gifttime,
		CpClientReceiveTime: cpTime,
	}
	dataJson := utils.HF_JtoA(ack)
	timestamp := core.TimeServer().UnixMilli()
	// 初始化请求体（不含签名）
	reqData := map[string]interface{}{
		"roomCode":  roomCode,
		"ackType":   "cpClientReceive",
		"timestamp": timestamp,
		"data":      dataJson,
	}

	// 生成签名（关键修改点）
	actualSign := generateSignature(reqData)

	// 构造最终请求体（包含签名）
	finalReq := &KS_Ack{
		RoomCode:  roomCode,
		Timestamp: timestamp,
		Sign:      actualSign,
		AckType:   "cpClientReceive",
		Data:      dataJson,
	}

	opts, err := httpclient.CreateJSONRequest("POST", URLReceiveAck, finalReq)
	if err != nil {
		core.LogError("创建ReceiveAck请求失败:", err)
		return
	}

	bodyData, err := httpclient.DoHTTPRequest(opts)
	if err != nil {
		core.LogError("执行ReceiveAck请求失败:", err)
		return
	}

	var ret AckResponse
	if err := httpclient.ParseJSONResponse(bodyData, &ret); err != nil {
		core.LogError("解析ReceiveAck响应失败:", err)
		return
	}
	if ret.Result != 1 {
		core.LogError("ReceiveAck错误:", string(bodyData))
	}
}

func KS_ShowAck(roomCode string, msgId string, cpTime int64) {
	// 参数校验
	if roomCode == "" {
		core.LogError("KS_ShowAck失败: roomCode为空")
		return
	}

	// 测试环境数据处理
	if network.IsTestServer {
		return
	}

	ack := &KS_AckData{
		UniqueMessageId:  msgId,
		PushType:         "giftSend",
		CpClientShowTime: cpTime,
	}
	dataJson := utils.HF_JtoA(ack)
	timestamp := core.TimeServer().UnixMilli()
	// 初始化请求体（不含签名）
	reqData := map[string]interface{}{
		"roomCode":  roomCode,
		"ackType":   "cpClientShow",
		"timestamp": timestamp,
		"data":      dataJson,
	}

	// 生成签名（关键修改点）
	actualSign := generateSignature(reqData)

	// 构造最终请求体（包含签名）
	finalReq := &KS_Ack{
		RoomCode:  roomCode,
		Timestamp: timestamp,
		Sign:      actualSign,
		AckType:   "cpClientShow",
		Data:      dataJson,
	}

	opts, err := httpclient.CreateJSONRequest("POST", URLShowAck, finalReq)
	if err != nil {
		core.LogError("创建KS_ShowAck请求失败:", err)
		return
	}

	bodyData, err := httpclient.DoHTTPRequest(opts)
	if err != nil {
		core.LogError("执行KS_ShowAck请求失败:", err)
		return
	}

	var ret AckResponse
	if err := httpclient.ParseJSONResponse(bodyData, &ret); err != nil {
		core.LogError("解析ReceiveAck响应失败:", err)
		return
	}
	if ret.Result != 1 {
		core.LogError("KS_ShowAck错误:", string(bodyData))
	}
}

// https://open.kuaishou.com/openapi/developer/live/data/interactive/start

var giftContent string = ""

type SetGiftRequest struct {
	RoomCode   string `json:"roomCode"`
	Timestamp  int64  `json:"timestamp"`
	Sign       string `json:"sign"`
	ModuleType string `json:"moduleType"`
	ActionType string `json:"actionType"`
	Data       string `json:"data"`
}
type SetGiftData struct {
	GiftList       string `json:"giftList"`
	GiftExtendInfo string `json:"giftExtendInfo"`
}

type GiftExtendInfoItem struct {
	ID             int    `json:"id"`
	CustomGiftName string `json:"customGiftName"`
	EffectDesc     string `json:"effectDesc"`
	BatchBar       struct {
		SubTitle   string `json:"subTitle"`
		BatchInfos []struct {
			Count            int    `json:"count"`
			Action           string `json:"action"`
			ObjectBeingActed string `json:"objectBeingActed"`
		} `json:"batchInfos"`
	} `json:"batchBar"`
}

var giftIdList []int = []int{13587, 13585, 13586, 11582, 11584, 12252, 11606, 11585, 11587}
var giftNameList []string = []string{"哪吒", "敖丙", "申公豹", "福利", "灵珠护盾", "枪兵", "盾兵", "骑兵", "全军出击"}

func KS_SetGiftTop(roomCode, actionType string) bool {

	core.LogInfo(fmt.Sprintf("SetGiftTop: roomCode:%s actionType:%s", roomCode, actionType))
	if giftContent == "" {

		GiftExtendInfoItems := make([]GiftExtendInfoItem, 0)
		for i, id := range giftIdList {
			item := GiftExtendInfoItem{
				ID:             id,
				CustomGiftName: giftNameList[i],
				EffectDesc:     giftNameList[i],
				BatchBar: struct {
					SubTitle   string `json:"subTitle"`
					BatchInfos []struct {
						Count            int    `json:"count"`
						Action           string `json:"action"`
						ObjectBeingActed string `json:"objectBeingActed"`
					} `json:"batchInfos"`
				}{
					SubTitle: giftNameList[i],
					BatchInfos: []struct {
						Count            int    `json:"count"`
						Action           string `json:"action"`
						ObjectBeingActed string `json:"objectBeingActed"`
					}{
						{Count: 1, Action: "召唤", ObjectBeingActed: giftNameList[i]},
						{Count: 10, Action: "召唤", ObjectBeingActed: giftNameList[i]},
						{Count: 30, Action: "召唤", ObjectBeingActed: giftNameList[i]},
						{Count: 66, Action: "召唤", ObjectBeingActed: giftNameList[i]},
						{Count: 188, Action: "召唤", ObjectBeingActed: giftNameList[i]},
						{Count: 520, Action: "召唤", ObjectBeingActed: giftNameList[i]},
						{Count: 888, Action: "召唤", ObjectBeingActed: giftNameList[i]},
					},
				},
			}
			GiftExtendInfoItems = append(GiftExtendInfoItems, item)
		}
		giftContent = utils.HF_JtoA(GiftExtendInfoItems)
	}
	if network.IsTestServer {
		return true
	}
	curTime := core.TimeServer().UnixMilli()
	giftData := &SetGiftData{
		GiftList:       "13587,13585,13586,11582,11584,12252,11606,11585,11587",
		GiftExtendInfo: giftContent,
	}
	// core.LogInfo(fmt.Sprintf("giftContent:%s", giftContent))
	datajson := utils.HF_JtoA(giftData)
	// core.LogInfo(fmt.Sprintf("datajson:%s", datajson))
	// 初始化请求体（不含签名）
	reqData := map[string]interface{}{
		"roomCode":   roomCode,
		"timestamp":  curTime,
		"moduleType": "gift",
		"actionType": "top",
		"data":       datajson,
	}

	// 生成签名（关键修改点）
	actualSign := generateSignature(reqData)

	// 构造最终请求体（包含签名）
	finalReq := &SetGiftRequest{
		RoomCode:   roomCode,
		Timestamp:  curTime,
		ModuleType: "gift",
		ActionType: "top",
		Data:       datajson,
		Sign:       actualSign,
	}

	// core.LogInfo(fmt.Sprintf("SetGiftRequest:%s", utils.HF_JtoA(finalReq)))

	opts, err := httpclient.CreateJSONRequest("POST", KS_URL(URLSetGift), finalReq)
	if err != nil {
		core.LogError("创建SetGiftTop请求失败:", err)
		return false
	}

	bodyData, err := httpclient.DoHTTPRequest(opts)
	if err != nil {
		core.LogError("执行SetGiftTop请求失败:", err)
		return false
	}

	core.LogInfo(fmt.Sprintf("SetGiftTop请求结果: %s", string(bodyData)))
	// 解析响应
	var result BindResponse
	if err := json.Unmarshal(bodyData, &result); err != nil {
		core.LogError("解析SetGiftTop请求失败:", err)
		return false
	}

	// 业务逻辑错误处理
	if result.Result != 1 {
		core.LogError("SetGiftTop请求业务逻辑错误:", result.ErrorMsg)
		return false
	}
	return true
}

type QuickCommentRequest struct {
	RoomCode  string `json:"roomCode"`
	Timestamp int64  `json:"timestamp"`
	Sign      string `json:"sign"`
	RoundId   string `json:"roundId"`
	Type      string `json:"type"`
	Data      string `json:"data"`
}

func KS_QuickComment(roomCode, roundId string) bool {

	core.LogInfo(fmt.Sprintf("KS_QuickComment: roomCode:%s roundId:%s", roomCode, roundId))
	// 参数校验
	if roomCode == "" {
		core.LogError(fmt.Sprintf("必要参数为空: roomCode:%s", roomCode))
		return false
	}
	// 测试环境数据处理
	if network.IsTestServer {
		return true
	}
	curTime := core.TimeServer().UnixMilli()
	datajson := "{\"closeQuickInfoBySendGift\":false,\"commandInfo\":{\"commentTextList\":[\"加入\",\"攻打全部\",\"停战\",\"666\",\"解除结盟\"]},\"quickInfos\":[]}"

	// 初始化请求体（不含签名）
	reqData := map[string]interface{}{
		"roomCode":  roomCode,
		"timestamp": curTime,
		"roundId":   roundId,
		"type":      "1",
		"data":      datajson,
	}

	// 生成签名（关键修改点）
	actualSign := generateSignature(reqData)

	// 构造最终请求体（包含签名）
	finalReq := &QuickCommentRequest{
		RoomCode:  roomCode,
		Timestamp: curTime,
		RoundId:   roundId,
		Type:      "1",
		Data:      datajson,
		Sign:      actualSign,
	}

	opts, err := httpclient.CreateJSONRequest("POST", KS_URL(URLQuickComment), finalReq)
	if err != nil {
		core.LogError("创建QuickComment请求失败:", err)
		return false
	}

	bodyData, err := httpclient.DoHTTPRequest(opts)
	if err != nil {
		core.LogError("执行QuickComment请求失败:", err)
		return false
	}

	// core.LogInfo(fmt.Sprintf("LiveBind请求结果: %s", string(bodyData)))
	// 解析响应
	var result BindResponse
	if err := json.Unmarshal(bodyData, &result); err != nil {
		core.LogError("解析QuickComment请求失败:", err)
		return false
	}

	// 业务逻辑错误处理
	if result.Result != 1 {
		core.LogError("QuickComment请求业务逻辑错误:", result.ErrorMsg)
		return false
	}
	return true
}
