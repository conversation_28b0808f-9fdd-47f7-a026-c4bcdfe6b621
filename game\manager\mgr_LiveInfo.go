package manager

import (
	"log"
	"runtime/debug"
	"time"
	"zone/game/mods"
	"zone/game/platform/bytedance"
	"zone/game/platform/kuaishou"
	"zone/lib/core"
	"zone/lib/network"
	"zone/lib/utils"
)

// func LiveStart(roomId, accessToken, msgType string, test bool)
type DYLiveReq struct {
	RoomId       string
	MsgType      string
	Type         int
	CallBack     func(any)
	FailCallBack func(any)
}
type DYFailDataReq struct {
	RoomId   string
	MsgType  string
	Page     int
	Size     int
	CallBack func(bool, []*bytedance.JsFailData)
}

type KSLiveReq struct {
	RoomId       string
	ActionType   string
	Type         int
	Data         *kuaishou.RoundData
	CallBack     func(any)
	FailCallBack func(any)
}

type KSGiftDataQueue struct {
	RoomId   string
	CallBack func([]*kuaishou.GiftQueryItem)
}

// RoomMgr ! 中心服务器
type LiveInfoMgr struct {
	DYLiveInfoQueqe    []*DYLiveReq                     //! 抖音直播请求队列
	DYLiveStartQueqe   []*DYLiveReq                     //! 抖音直播请求队列
	DYLiveStopQueqe    []*DYLiveReq                     //! 抖音直播请求队列
	DYLiveStatusQueqe  []*DYLiveReq                     //! 抖音直播请求队列
	DYFailDataReqQueqe *utils.SafeSlice[*DYFailDataReq] //! 抖音失败数据请求队列

	KSBindQueqe     []*KSLiveReq                       //! 快手绑定请求队列
	KSRoundQueqe    []*KSLiveReq                       //! 快手绑定请求队列
	KSQuickComment  []*KSLiveReq                       //! 快手绑定请求队列
	KSTopGift       []*KSLiveReq                       //! 快手绑定请求队列
	KSGiftDataQueue *utils.SafeSlice[*KSGiftDataQueue] //! 抖音失败数据请求队列

}

const (
	Type_LiveInfoReq = iota
	Type_LiveStart
	Type_LiveStop
	Type_LiveStatus
	Type_GameOver
	Type_CallBack
	Type_Bind
	Type_Round
	Type_TopGift
	Type_QComment
)

var s_LiveInfoMgr *LiveInfoMgr = nil

func GetLiveInfoMgr() *LiveInfoMgr {
	if s_LiveInfoMgr == nil {
		s_LiveInfoMgr = new(LiveInfoMgr)
		s_LiveInfoMgr.Init() // 确保初始化所有字段

		// 注册到mods包的管理器注册表
		mods.RegisterLiveInfoManager(s_LiveInfoMgr)
	}

	return s_LiveInfoMgr
}

func (liveInfo *LiveInfoMgr) Init() {
	// 防止重复初始化

	// 无论platform是什么，都要初始化基本字段，防止nil指针错误

	// 只有在platform为0时才启动逻辑循环
	if network.Platform == network.Platform_DouYin {
		liveInfo.DYLiveInfoQueqe = make([]*DYLiveReq, 0)
		liveInfo.DYLiveStartQueqe = make([]*DYLiveReq, 0)
		liveInfo.DYLiveStopQueqe = make([]*DYLiveReq, 0)
		liveInfo.DYLiveStatusQueqe = make([]*DYLiveReq, 0)
		liveInfo.DYFailDataReqQueqe = utils.NewSafeSlice[*DYFailDataReq]()
	} else {

		liveInfo.KSBindQueqe = make([]*KSLiveReq, 0)
		liveInfo.KSRoundQueqe = make([]*KSLiveReq, 0)
		liveInfo.KSQuickComment = make([]*KSLiveReq, 0)
		liveInfo.KSTopGift = make([]*KSLiveReq, 0)
		liveInfo.KSGiftDataQueue = utils.NewSafeSlice[*KSGiftDataQueue]()
	}
	go liveInfo.logicRun()
	core.LogInfo("LiveInfoMgr初始化完成，已启动逻辑循环")
}

func (liveInfo *LiveInfoMgr) AddKSLiveReq(roomDYID string, callBack func(any), failcallBack func(any), reqType int, actionType string, data interface{}) {
	if network.Platform != network.Platform_KuaiShou {
		return
	}
	if roomDYID == "" {
		core.LogError("AddLiveReq", reqType, "Error. LiveReq roomDYID is nil")
		return
	}
	var roundData *kuaishou.RoundData
	if data != nil {
		if rd, ok := data.(*kuaishou.RoundData); ok {
			roundData = rd
		}
	}
	req := &KSLiveReq{RoomId: roomDYID, CallBack: callBack, Type: reqType, FailCallBack: failcallBack, ActionType: actionType, Data: roundData}
	switch reqType {
	case Type_Bind:
		liveInfo.KSBindQueqe = append(liveInfo.KSBindQueqe, req)
	case Type_Round:
		liveInfo.KSRoundQueqe = append(liveInfo.KSRoundQueqe, req)
	case Type_TopGift:
		liveInfo.KSTopGift = append(liveInfo.KSTopGift, req)
	case Type_QComment:
		liveInfo.KSQuickComment = append(liveInfo.KSQuickComment, req)
	default:
		core.LogError("AddLiveReq Error. 这不是快手的请求类型", reqType)
	}
}

func (liveInfo *LiveInfoMgr) AddDYLiveReq(roomDYID string, callBack func(any), failcallBack func(any), msg_type string, reqType int) {
	if network.Platform != network.Platform_DouYin {
		return
	}
	if roomDYID == "" {
		core.LogError("AddLiveReq", reqType, "msg_type", msg_type, "Error. LiveReq roomDYID is nil")
		return
	}
	req := &DYLiveReq{RoomId: roomDYID, CallBack: callBack, Type: reqType, MsgType: msg_type, FailCallBack: failcallBack}
	switch reqType {
	case Type_LiveInfoReq:
		liveInfo.DYLiveInfoQueqe = append(liveInfo.DYLiveInfoQueqe, req)
	case Type_LiveStart:
		liveInfo.DYLiveStartQueqe = append(liveInfo.DYLiveStartQueqe, req)
	case Type_LiveStop:
		liveInfo.DYLiveStopQueqe = append(liveInfo.DYLiveStopQueqe, req)
	case Type_LiveStatus:
		liveInfo.DYLiveStatusQueqe = append(liveInfo.DYLiveStatusQueqe, req)
	default:
		core.LogError("AddLiveReq Error. 这不是抖音的请求类型", reqType)
	}
}

func (liveInfo *LiveInfoMgr) CheckDYFailData(roomId, msgType string, page, size int, callBack func(bool, []*bytedance.JsFailData)) {

	if network.Platform != network.Platform_DouYin {
		return
	}
	if !liveInfo.DYFailDataReqQueqe.Exists(func(req *DYFailDataReq) bool {
		return req.RoomId == roomId
	}) {
		liveInfo.DYFailDataReqQueqe.Append(&DYFailDataReq{RoomId: roomId, CallBack: callBack, Page: page, MsgType: msgType, Size: size})
	}
}

func (liveInfo *LiveInfoMgr) CheckKSFailData(roomId string, callBack func([]*kuaishou.GiftQueryItem)) {

	if network.Platform != network.Platform_KuaiShou {
		return
	}
	liveInfo.KSGiftDataQueue.Append(&KSGiftDataQueue{RoomId: roomId, CallBack: callBack})
}

func (liveInfo *LiveInfoMgr) Reporting(roomId string, ackType int, msgid string, msgType string) {

	if network.Platform != network.Platform_DouYin {
		return
	}
	bytedance.Reporting(roomId, ackType, msgid, msgType)
}

func (liveInfo *LiveInfoMgr) logicRun() {
	defer func() {
		x := recover()
		if x != nil {
			log.Println(x, string(debug.Stack()))
			core.LogError(x, string(debug.Stack()))
		}
	}()

	//! 0.1s 消息循环
	ticker := time.NewTicker(time.Second * 1)
	// hourTicker := time.NewTicker(time.Minute * 60)
	for {
		if core.GetZoneApp().IsClosed() { //! 关服
			break
		}

		select {
		case <-ticker.C:
			liveInfo.onTimer()
			// case <-hourTicker.C:
			// 	liveInfo.onHourTimer()
		}
	}

	ticker.Stop()
}

func (liveInfo *LiveInfoMgr) ExDYQueue(queue []*DYLiveReq) []*DYLiveReq {
	count := len(queue)
	count = min(count, 10)
	recordCount := 0
	for recordCount != count && len(queue) > 0 {
		req := queue[0]
		queue = queue[1:]
		if req.Type == Type_LiveInfoReq {
			roomInfo, err := bytedance.LiveInfoReq(req.RoomId, req.MsgType)
			if err != nil {
				if req.FailCallBack != nil {
					req.FailCallBack(err)
				}
			} else {
				if roomInfo != nil {
					if req.CallBack != nil {
						req.CallBack(roomInfo)
					}
				} else {
					if req.FailCallBack != nil {
						req.FailCallBack(nil)
					}
				}
			}
			recordCount++
		} else {
			if req.Type == Type_CallBack {
				req.CallBack(1)
				recordCount++
			} else {
				room := GetRoomMgr().GetRoomByPlatformId(req.RoomId)
				if room != nil && room.GetPlayState() != mods.PLAY_STATE_WAIT {
					switch req.Type {
					case Type_LiveStart:
						if room.GetPlayState() != mods.PLAY_STATE_END {
							suc := bytedance.LiveStart(req.RoomId, req.MsgType)
							if !suc {
								liveInfo.AddDYLiveReq(req.RoomId, nil, nil, req.MsgType, req.Type)
							} else {
								if req.CallBack != nil {
									req.CallBack(suc)
								}
							}
						}
					case Type_LiveStop:
						bytedance.LiveStop(req.RoomId, req.MsgType)
					case Type_LiveStatus:
						if room.GetPlayState() != mods.PLAY_STATE_END {
							suc := bytedance.LiveStatus(req.RoomId, req.MsgType)
							if !suc {
								liveInfo.AddDYLiveReq(req.RoomId, nil, nil, req.MsgType, Type_LiveStart)
							}
						}
					}
					recordCount++
				}
			}
		}
	}
	return queue
}

func (liveInfo *LiveInfoMgr) ExKSQueue(queue []*KSLiveReq, max int) []*KSLiveReq {
	count := len(queue)
	count = min(count, max)
	recordCount := 0
	for recordCount != count && len(queue) > 0 {
		req := queue[0]
		queue = queue[1:]
		if req.Type == Type_Bind {
			roomInfo, err := kuaishou.KS_Bind(req.RoomId, req.ActionType)
			if err != nil {
				if req.FailCallBack != nil {
					req.FailCallBack(err)
				}
			} else {
				if roomInfo != nil {
					if req.CallBack != nil {
						req.CallBack(roomInfo)
					}
				} else {
					if req.FailCallBack != nil {
						req.FailCallBack(nil)
					}
				}
			}
			recordCount++
		} else {
			if req.Type == Type_Round {
				suc := kuaishou.KS_Round(req.RoomId, req.ActionType, req.Data)
				if req.CallBack != nil {
					req.CallBack(suc)
				}
			}
			if req.Type == Type_TopGift {
				suc := kuaishou.KS_SetGiftTop(req.RoomId, req.ActionType)
				if req.CallBack != nil {
					req.CallBack(suc)
				}
			}
			if req.Type == Type_QComment {
				suc := kuaishou.KS_QuickComment(req.RoomId, req.ActionType)
				if req.CallBack != nil {
					req.CallBack(suc)
				}
			}
			recordCount++
		}
	}
	return queue
}

func (liveInfo *LiveInfoMgr) onTimer() {

	if network.Platform == network.Platform_DouYin {
		liveInfo.DYLiveInfoQueqe = liveInfo.ExDYQueue(liveInfo.DYLiveInfoQueqe)
		liveInfo.DYLiveStartQueqe = liveInfo.ExDYQueue(liveInfo.DYLiveStartQueqe)
		liveInfo.DYLiveStopQueqe = liveInfo.ExDYQueue(liveInfo.DYLiveStopQueqe)
		liveInfo.DYLiveStatusQueqe = liveInfo.ExDYQueue(liveInfo.DYLiveStatusQueqe)

		recordCount := 0
		count := min(10, liveInfo.DYFailDataReqQueqe.Len())
		for recordCount != count && liveInfo.DYFailDataReqQueqe.Len() > 0 {
			req, err := liveInfo.DYFailDataReqQueqe.RemoveAt(0)
			if err != nil {
				continue
			}
			room := GetRoomMgr().GetRoomByPlatformId(req.RoomId)
			if room == nil || room.GetPlayState() == mods.PLAY_STATE_WAIT || room.GetPlayState() == mods.PLAY_STATE_END {
				continue
			}
			suc, data := bytedance.GetFailData(req.RoomId, req.MsgType, req.Page, req.Size)
			if req.CallBack != nil {
				req.CallBack(suc, data)
			}
			recordCount++
		}
	} else {
		liveInfo.KSBindQueqe = liveInfo.ExKSQueue(liveInfo.KSBindQueqe, 1000)
		liveInfo.KSRoundQueqe = liveInfo.ExKSQueue(liveInfo.KSRoundQueqe, 10)
		liveInfo.KSTopGift = liveInfo.ExKSQueue(liveInfo.KSTopGift, 10)
		liveInfo.KSQuickComment = liveInfo.ExKSQueue(liveInfo.KSQuickComment, 10)

		recordCount := 0
		count := min(10, liveInfo.KSGiftDataQueue.Len())
		for recordCount != count && liveInfo.KSGiftDataQueue.Len() > 0 {
			req, err := liveInfo.KSGiftDataQueue.RemoveAt(0)
			if err != nil {
				continue
			}
			room := GetRoomMgr().GetRoomByPlatformId(req.RoomId)
			if room == nil || room.GetPlayState() == mods.PLAY_STATE_WAIT || room.GetPlayState() == mods.PLAY_STATE_END {
				continue
			}
			data, err := kuaishou.QueryGiftMessages(req.RoomId)
			if err != nil {
				if req.CallBack != nil {
					req.CallBack(data)
				}
			}

			recordCount++
		}
	}

}
