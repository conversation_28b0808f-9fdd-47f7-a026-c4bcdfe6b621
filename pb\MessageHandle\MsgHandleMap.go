// Code generated by pb_exporter.go. DO NOT EDIT.
package MessageHandle

import (
	"zone/lib/network"
	"zone/game/Response"
)

// MessageHandler 消息处理函数类型
type MessageHandler func(session *network.Session, msg any)

// MsgHandleMap 消息ID到处理函数的映射表
var MsgHandleMap = map[int32]MessageHandler{
	ErrorCode: Response.ErrorCodeResponse,
	HeartBeat: Response.HeartBeatResponse,
	GmOrder: Response.GmOrderResponse,
	Announcement: Response.AnnouncementResponse,
	AddPlayer: Response.AddPlayerResponse,
	PlayerOperate: Response.PlayerOperateResponse,
	SyncPlayerData: Response.SyncPlayerDataResponse,
	KuaiShouAck: Response.KuaiShouAckResponse,
	CreateRoom: Response.CreateRoomResponse,
	CreateGame: Response.CreateGameResponse,
	MatchMutiGame: Response.MatchMutiGameResponse,
	EnterMutiGame: Response.EnterMutiGameResponse,
	SyncFrame: Response.SyncFrameResponse,
	PlayerKillCount: Response.PlayerKillCountResponse,
	GameStart: Response.GameStartResponse,
	GameOver: Response.GameOverResponse,
	GetScoreRank: Response.GetScoreRankResponse,
	FamilyRank: Response.FamilyRankResponse,
	ReloginGame: Response.ReloginGameResponse,
	InitGameOver: Response.InitGameOverResponse,
	SyncWinStreakPool: Response.SyncWinStreakPoolResponse,
}

// GetMessageHandler 根据消息ID获取对应的处理函数
func GetMessageHandler(msgID int32) (MessageHandler, bool) {
	handler, exists := MsgHandleMap[msgID]
	return handler, exists
}

// HandleMessage 统一的消息处理入口
func HandleMessage(session *network.Session, v[]byte) bool {
	pbMsg := BytesToMsg(v)
	if pbMsg != nil {
		if handler, exists := GetMessageHandler(pbMsg.GetMainID()); exists {
			handler(session, pbMsg.GetMsg())
			return true
		}
	}
	return false
}
