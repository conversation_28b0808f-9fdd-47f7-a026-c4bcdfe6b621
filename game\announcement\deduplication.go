package announcement

import (
	"fmt"
	"time"
	"zone/lib/core"
	"zone/lib/db"
)

// DeduplicationManager 公告去重管理器
type DeduplicationManager struct {
	keyPrefix        string        // Redis键前缀
	defaultTTL       time.Duration // 默认过期时间
	cleanupBatchSize int           // 批量清理数量
}

// NewDeduplicationManager 创建去重管理器
func NewDeduplicationManager() *DeduplicationManager {
	return &DeduplicationManager{
		keyPrefix:        "announcement_sent",
		defaultTTL:       7 * 24 * time.Hour, // 7天过期
		cleanupBatchSize: 100,                // 每次清理100个键
	}
}

// GenerateKey 生成去重键
// 格式: announcement_sent:{session_id}:{announcement_id}
func (dm *DeduplicationManager) GenerateKey(sessionID, announcementID int) string {
	return fmt.Sprintf("%s:%d:%d", dm.keyPrefix, sessionID, announcementID)
}

// GenerateKeyLegacy 生成旧格式的去重键（用于兼容性）
func (dm *DeduplicationManager) GenerateKeyLegacy(sessionID, roomID, announcementID int) string {
	return fmt.Sprintf("%s:%d:%d:%d", dm.keyPrefix, sessionID, roomID, announcementID)
}

// IsAlreadySent 检查公告是否已经发送过
func (dm *DeduplicationManager) IsAlreadySent(sessionID, announcementID int) bool {
	key := dm.GenerateKey(sessionID, announcementID)

	// 记录查询开始
	core.LogDebug("开始检查去重键", "sessionID:", sessionID, "announcementID:", announcementID, "键:", key)

	exists, err := db.GetRedisMgr().Exists(key)
	if err != nil {
		core.LogError("检查公告去重键失败:", err, "键:", key, "sessionID:", sessionID, "announcementID:", announcementID)
		// Redis连接失败时的降级策略：允许发送，避免阻塞正常业务
		core.LogInfo("Redis连接失败，采用降级策略允许公告发送", "sessionID:", sessionID, "announcementID:", announcementID)
		return false
	}

	// if exists {
	// 	core.LogInfo("公告已发送过，跳过推送", "sessionID:", sessionID, "announcementID:", announcementID, "键:", key)
	// } else {
	// 	core.LogDebug("公告未发送过，允许推送", "sessionID:", sessionID, "announcementID:", announcementID, "键:", key)
	// }

	return exists
}

// MarkAsSentWithEndTime 根据公告结束时间设置TTL并标记为已发送
func (dm *DeduplicationManager) MarkAsSentWithEndTime(sessionID, announcementID int, endTime int64) error {
	key := dm.GenerateKey(sessionID, announcementID)

	// 计算TTL：公告结束时间 + 额外的缓冲时间（1小时）
	// 使用服务器时间确保时区一致性
	now := core.TimeServer().Unix()

	// 添加1小时（3600秒）的缓冲时间，确保公告结束后仍能去重一段时间
	bufferTime := int64(3600) // 1小时缓冲
	ttl := endTime - now + bufferTime

	// 设置合理的TTL范围
	minTTL := int64(300)           // 最小5分钟
	maxTTL := int64(7 * 24 * 3600) // 最大7天

	if ttl < minTTL {
		ttl = minTTL
		core.LogDebug("TTL过短，设置为最小值", "sessionID:", sessionID, "announcementID:", announcementID, "原TTL:", endTime-now, "设置TTL:", ttl)
	} else if ttl > maxTTL {
		ttl = maxTTL
		core.LogDebug("TTL过长，设置为最大值", "sessionID:", sessionID, "announcementID:", announcementID, "原TTL:", endTime-now, "设置TTL:", ttl)
	}

	expiration := ttl

	// 记录详细的调试信息
	core.LogDebug("准备设置Redis去重键",
		"sessionID:", sessionID,
		"announcementID:", announcementID,
		"键:", key,
		"当前时间:", now,
		"结束时间:", endTime,
		"缓冲时间:", bufferTime,
		"计算TTL:", endTime-now+bufferTime,
		"实际TTL:", ttl)

	_, err := db.GetRedisMgr().SetEx(key, "1", expiration)
	if err != nil {
		core.LogError("设置公告去重键失败:", err, "键:", key, "TTL:", ttl)
		core.LogInfo("Redis连接失败，无法记录去重状态，可能导致重复推送")
		return err
	}

	// 验证写入是否成功（可选的一致性检查）
	if dm.enableConsistencyCheck() {
		exists, verifyErr := db.GetRedisMgr().Exists(key)
		if verifyErr != nil {
			core.LogError("验证Redis写入失败:", verifyErr, "键:", key)
		} else if !exists {
			core.LogError("Redis写入验证失败：键不存在", "键:", key, "sessionID:", sessionID, "announcementID:", announcementID)
			return fmt.Errorf("Redis写入验证失败，键不存在: %s", key)
		} else {
			core.LogDebug("Redis写入验证成功", "键:", key)
		}
	}

	core.LogInfo("标记公告为已发送成功", "sessionID:", sessionID, "announcementID:", announcementID,
		"键:", key, "TTL:", ttl, "过期时间:", now+ttl)
	return nil
}

// enableConsistencyCheck 是否启用一致性检查（可配置）
func (dm *DeduplicationManager) enableConsistencyCheck() bool {
	// 在生产环境中可以通过配置控制是否启用
	// 这里默认启用以帮助调试
	return true
}

// IsRedisAvailable 检查Redis连接是否可用
func (dm *DeduplicationManager) IsRedisAvailable() bool {
	// 安全检查：首先检查Redis管理器是否存在
	redisMgr := db.GetRedisMgr()
	if redisMgr == nil {
		core.LogError("Redis管理器未初始化")
		return false
	}

	// 使用defer和recover来捕获panic
	defer func() {
		if r := recover(); r != nil {
			core.LogError("Redis可用性检查发生panic:", r)
		}
	}()

	// 尝试执行一个简单的Redis命令来检查连接
	testKey := "health_check_" + fmt.Sprintf("%d", core.TimeServer().Unix())

	// 测试写入
	_, writeErr := redisMgr.SetEx(testKey, "test", 10) // 10秒过期
	if writeErr != nil {
		core.LogError("Redis写入测试失败:", writeErr)
		return false
	}

	// 测试读取
	exists, readErr := redisMgr.Exists(testKey)
	if readErr != nil {
		core.LogError("Redis读取测试失败:", readErr)
		return false
	}

	// 清理测试键
	_, delErr := redisMgr.Del(testKey)
	if delErr != nil {
		core.LogError("Redis删除测试失败:", delErr)
		// 删除失败不影响可用性判断
	}

	if !exists {
		core.LogError("Redis读写不一致：写入成功但读取失败")
		return false
	}

	core.LogDebug("Redis连接状态正常")
	return true
}

// IsRedisAvailableSafe 安全的Redis可用性检查（不会panic）
func (dm *DeduplicationManager) IsRedisAvailableSafe() bool {
	defer func() {
		if r := recover(); r != nil {
			core.LogError("Redis安全检查发生panic:", r)
		}
	}()

	// 检查Redis管理器是否存在
	redisMgr := db.GetRedisMgr()
	if redisMgr == nil {
		return false
	}

	// 简单的存在性检查，不进行实际的Redis操作
	return true
}

// GetDeduplicationStats 获取去重统计信息
func (dm *DeduplicationManager) GetDeduplicationStats() map[string]interface{} {
	stats := make(map[string]interface{})

	// 获取所有去重键的数量
	pattern := dm.keyPrefix + ":*"
	keys, err := db.GetRedisMgr().Keys(pattern)
	if err != nil {
		core.LogError("获取去重统计失败:", err)
		stats["error"] = err.Error()
		stats["redis_available"] = false
		return stats
	}

	stats["total_keys"] = len(keys)
	stats["redis_available"] = true
	stats["key_prefix"] = dm.keyPrefix
	stats["default_ttl"] = dm.defaultTTL.String()

	return stats
}

// CleanupExpiredKeys 清理过期的去重键（可选的维护操作）
func (dm *DeduplicationManager) CleanupExpiredKeys() error {
	// Redis会自动清理过期键，这个方法主要用于手动维护
	pattern := dm.keyPrefix + ":*"
	keys, err := db.GetRedisMgr().Keys(pattern)
	if err != nil {
		core.LogError("获取去重键列表失败:", err)
		return err
	}

	core.LogInfo("当前去重键数量:", len(keys))

	// 这里可以添加额外的清理逻辑，比如检查键的TTL等
	// 但通常Redis的自动过期机制已经足够

	return nil
}

// BatchCheckAlreadySent 批量检查多个公告是否已发送
func (dm *DeduplicationManager) BatchCheckAlreadySent(sessionID int, announcementIDs []int) map[int]bool {
	results := make(map[int]bool)

	if len(announcementIDs) == 0 {
		return results
	}

	// 构建所有需要检查的键
	keys := make([]string, len(announcementIDs))
	for i, announcementID := range announcementIDs {
		keys[i] = dm.GenerateKey(sessionID, announcementID)
	}

	// 批量检查Redis键是否存在
	for i, key := range keys {
		exists, err := db.GetRedisMgr().Exists(key)
		if err != nil {
			core.LogError("批量检查公告去重键失败:", err, "键:", key)
			// 出错时允许发送，避免阻塞
			results[announcementIDs[i]] = false
		} else {
			results[announcementIDs[i]] = exists
		}
	}

	core.LogDebug("批量检查去重结果", "sessionID:", sessionID, "检查数量:", len(announcementIDs), "已发送数量:", countTrue(results))
	return results
}

// BatchMarkAsSent 批量标记多个公告为已发送
func (dm *DeduplicationManager) BatchMarkAsSent(sessionID int, announcements []struct {
	ID      int
	EndTime int64
}) error {
	if len(announcements) == 0 {
		return nil
	}

	successCount := 0
	errorCount := 0

	for _, announcement := range announcements {
		err := dm.MarkAsSentWithEndTime(sessionID, announcement.ID, announcement.EndTime)
		if err != nil {
			errorCount++
			core.LogError("批量标记公告失败", "sessionID:", sessionID, "announcementID:", announcement.ID, "错误:", err)
		} else {
			successCount++
		}
	}

	core.LogDebug("批量标记完成", "sessionID:", sessionID, "成功:", successCount, "失败:", errorCount)

	if errorCount > 0 {
		return fmt.Errorf("批量标记部分失败，成功:%d，失败:%d", successCount, errorCount)
	}

	return nil
}

// countTrue 计算map中true值的数量
func countTrue(m map[int]bool) int {
	count := 0
	for _, v := range m {
		if v {
			count++
		}
	}
	return count
}

// CleanupExpiredRecords 清理过期记录
func (dm *DeduplicationManager) CleanupExpiredRecords() {
	// 获取所有匹配的键
	pattern := fmt.Sprintf("%s:*", dm.keyPrefix)
	keys, err := db.GetRedisMgr().Keys(pattern)
	if err != nil {
		core.LogError("获取公告去重键失败:", err, "模式:", pattern)
		return
	}

	if len(keys) == 0 {
		return
	}

	// 批量检查和清理过期键
	cleanedCount := 0
	for i := 0; i < len(keys); i += dm.cleanupBatchSize {
		end := i + dm.cleanupBatchSize
		if end > len(keys) {
			end = len(keys)
		}

		batch := keys[i:end]
		cleanedCount += dm.cleanupBatchKeys(batch)
	}
}

// cleanupBatchKeys 批量清理键
func (dm *DeduplicationManager) cleanupBatchKeys(keys []string) int {
	cleanedCount := 0

	for _, key := range keys {
		// 检查键是否存在
		exists, err := db.GetRedisMgr().Exists(key)
		if err != nil {
			core.LogError("检查键存在性失败:", err, "键:", key)
			continue
		}

		// 如果键不存在，说明已过期，跳过
		if !exists {
			continue
		}

		// 简化处理：直接删除所有存在的键（在实际应用中可以添加更复杂的TTL检查）
		_, err = db.GetRedisMgr().Del(key)
		if err != nil {
			core.LogError("删除过期键失败:", err, "键:", key)
		} else {
			cleanedCount++
			// core.LogDebug("删除过期去重键", "键:", key)
		}
	}

	return cleanedCount
}

// CleanupByAnnouncementID 根据公告ID清理相关的去重记录
func (dm *DeduplicationManager) CleanupByAnnouncementID(announcementID int) {
	pattern := fmt.Sprintf("%s:*:*:%d", dm.keyPrefix, announcementID)

	keys, err := db.GetRedisMgr().Keys(pattern)
	if err != nil {
		core.LogError("获取公告去重键失败:", err, "模式:", pattern)
		return
	}

	if len(keys) == 0 {
		return
	}

	core.LogDebug("清理公告相关去重记录", "公告ID:", announcementID, "键数量:", len(keys))

	// 删除所有匹配的键
	for _, key := range keys {
		_, err := db.GetRedisMgr().Del(key)
		if err != nil {
			core.LogError("删除公告去重键失败:", err, "键:", key)
		}
	}

	core.LogInfo("清理公告去重记录完成", "公告ID:", announcementID, "清理数量:", len(keys))
}

// CleanupBySessionID 根据会话ID清理相关的去重记录
func (dm *DeduplicationManager) CleanupBySessionID(sessionID int) {
	pattern := fmt.Sprintf("%s:%d:*:*", dm.keyPrefix, sessionID)

	keys, err := db.GetRedisMgr().Keys(pattern)
	if err != nil {
		core.LogError("获取会话去重键失败:", err, "模式:", pattern)
		return
	}

	if len(keys) == 0 {
		return
	}

	core.LogDebug("清理会话相关去重记录", "会话ID:", sessionID, "键数量:", len(keys))

	// 删除所有匹配的键
	for _, key := range keys {
		_, err := db.GetRedisMgr().Del(key)
		if err != nil {
			core.LogError("删除会话去重键失败:", err, "键:", key)
		}
	}

	core.LogInfo("清理会话去重记录完成", "会话ID:", sessionID, "清理数量:", len(keys))
}

// CleanupByRoomID 根据房间ID清理相关的去重记录
func (dm *DeduplicationManager) CleanupByRoomID(roomID int) {
	pattern := fmt.Sprintf("%s:*:%d:*", dm.keyPrefix, roomID)

	keys, err := db.GetRedisMgr().Keys(pattern)
	if err != nil {
		core.LogError("获取房间去重键失败:", err, "模式:", pattern)
		return
	}

	if len(keys) == 0 {
		return
	}

	core.LogDebug("清理房间相关去重记录", "房间ID:", roomID, "键数量:", len(keys))

	// 删除所有匹配的键
	for _, key := range keys {
		_, err := db.GetRedisMgr().Del(key)
		if err != nil {
			core.LogError("删除房间去重键失败:", err, "键:", key)
		}
	}

	core.LogInfo("清理房间去重记录完成", "房间ID:", roomID, "清理数量:", len(keys))
}

// GetStats 获取去重统计信息
func (dm *DeduplicationManager) GetStats() map[string]interface{} {
	pattern := fmt.Sprintf("%s:*", dm.keyPrefix)
	keys, err := db.GetRedisMgr().Keys(pattern)
	if err != nil {
		core.LogError("获取去重统计失败:", err)
		return map[string]interface{}{
			"error": err.Error(),
		}
	}

	stats := map[string]interface{}{
		"total_keys":         len(keys),
		"key_prefix":         dm.keyPrefix,
		"default_ttl":        dm.defaultTTL.String(),
		"cleanup_batch_size": dm.cleanupBatchSize,
	}

	// 统计即将过期的键数量（简化处理，暂时设为0）
	expiringSoon := 0
	// TODO: 实现TTL检查逻辑
	stats["expiring_soon"] = expiringSoon

	return stats
}
