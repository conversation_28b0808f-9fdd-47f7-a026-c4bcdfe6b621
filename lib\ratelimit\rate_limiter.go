package ratelimit

import (
	"sync"
	"time"
	"zone/lib/core"
)

// TokenBucket 令牌桶结构
type TokenBucket struct {
	capacity   int64     // 桶容量（最大令牌数）
	tokens     int64     // 当前令牌数
	refillRate int64     // 每秒补充的令牌数
	lastRefill time.Time // 上次补充令牌的时间
	mu         sync.Mutex
}

// NewTokenBucket 创建新的令牌桶
func NewTokenBucket(capacity, refillRate int64) *TokenBucket {
	return &TokenBucket{
		capacity:   capacity,
		tokens:     capacity, // 初始时桶是满的
		refillRate: refillRate,
		lastRefill: time.Now(),
	}
}

// TryConsume 尝试消费指定数量的令牌
// tokens: 要消费的令牌数量
// 返回值: 是否成功消费
func (tb *TokenBucket) TryConsume(tokens int64) bool {
	tb.mu.Lock()
	defer tb.mu.Unlock()

	// 补充令牌
	tb.refill()

	// 检查是否有足够的令牌
	if tb.tokens >= tokens {
		tb.tokens -= tokens
		return true
	}

	return false
}

// refill 补充令牌（内部方法，调用前需要加锁）
func (tb *TokenBucket) refill() {
	now := time.Now()
	elapsed := now.Sub(tb.lastRefill)

	// 计算应该补充的令牌数
	tokensToAdd := int64(elapsed.Seconds()) * tb.refillRate

	if tokensToAdd > 0 {
		tb.tokens += tokensToAdd
		if tb.tokens > tb.capacity {
			tb.tokens = tb.capacity
		}
		tb.lastRefill = now
	}
}

// GetAvailableTokens 获取当前可用令牌数
func (tb *TokenBucket) GetAvailableTokens() int64 {
	tb.mu.Lock()
	defer tb.mu.Unlock()

	tb.refill()
	return tb.tokens
}

// RateLimiterConfig 频率限制配置
type RateLimiterConfig struct {
	RequestsPerSecond int64         // 每秒允许的请求数
	BurstCapacity     int64         // 突发容量
	CleanupInterval   time.Duration // 清理过期记录的间隔
	IPWhitelist       []string      // IP白名单
	IPBlacklist       []string      // IP黑名单
}

// DefaultRateLimiterConfig 默认配置
func DefaultRateLimiterConfig() *RateLimiterConfig {
	return &RateLimiterConfig{
		RequestsPerSecond: 10,              // 每秒10个请求
		BurstCapacity:     20,              // 突发容量20
		CleanupInterval:   5 * time.Minute, // 5分钟清理一次
		IPWhitelist:       []string{},
		IPBlacklist:       []string{},
	}
}

// IPRateLimiter IP级别的频率限制器
type IPRateLimiter struct {
	config      *RateLimiterConfig
	buckets     map[string]*TokenBucket
	lastAccess  map[string]time.Time
	mu          sync.RWMutex
	whitelist   map[string]bool
	blacklist   map[string]bool
	stopCleanup chan struct{}
}

// NewIPRateLimiter 创建新的IP频率限制器
func NewIPRateLimiter(config *RateLimiterConfig) *IPRateLimiter {
	if config == nil {
		config = DefaultRateLimiterConfig()
	}

	limiter := &IPRateLimiter{
		config:      config,
		buckets:     make(map[string]*TokenBucket),
		lastAccess:  make(map[string]time.Time),
		whitelist:   make(map[string]bool),
		blacklist:   make(map[string]bool),
		stopCleanup: make(chan struct{}),
	}

	// 初始化白名单和黑名单
	for _, ip := range config.IPWhitelist {
		limiter.whitelist[ip] = true
	}
	for _, ip := range config.IPBlacklist {
		limiter.blacklist[ip] = true
	}

	// 启动清理协程
	go limiter.startCleanup()

	core.LogInfo("IP频率限制器已启动",
		"每秒请求数:", config.RequestsPerSecond,
		"突发容量:", config.BurstCapacity,
		"清理间隔:", config.CleanupInterval)

	return limiter
}

// IsAllowed 检查IP是否被允许发送请求
func (irl *IPRateLimiter) IsAllowed(ip string) bool {
	// 检查黑名单
	if irl.blacklist[ip] {
		core.LogDebug("IP在黑名单中，拒绝请求:", ip)
		return false
	}

	// 检查白名单
	if irl.whitelist[ip] {
		core.LogDebug("IP在白名单中，允许请求:", ip)
		return true
	}

	// 获取或创建令牌桶
	bucket := irl.getBucket(ip)

	// 尝试消费一个令牌
	allowed := bucket.TryConsume(1)

	if !allowed {
		core.LogDebug("IP频率限制触发:", ip, "可用令牌:", bucket.GetAvailableTokens())
	}

	return allowed
}

// getBucket 获取或创建IP对应的令牌桶
func (irl *IPRateLimiter) getBucket(ip string) *TokenBucket {
	irl.mu.Lock()
	defer irl.mu.Unlock()

	// 更新最后访问时间
	irl.lastAccess[ip] = time.Now()

	// 检查是否已存在令牌桶
	if bucket, exists := irl.buckets[ip]; exists {
		return bucket
	}

	// 创建新的令牌桶
	bucket := NewTokenBucket(irl.config.BurstCapacity, irl.config.RequestsPerSecond)
	irl.buckets[ip] = bucket

	core.LogDebug("为IP创建新的令牌桶:", ip)
	return bucket
}

// startCleanup 启动清理过期记录的协程
func (irl *IPRateLimiter) startCleanup() {
	ticker := time.NewTicker(irl.config.CleanupInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			irl.cleanup()
		case <-irl.stopCleanup:
			return
		}
	}
}

// cleanup 清理过期的IP记录
func (irl *IPRateLimiter) cleanup() {
	irl.mu.Lock()
	defer irl.mu.Unlock()

	now := time.Now()
	expireTime := irl.config.CleanupInterval * 2 // 过期时间为清理间隔的2倍

	var expiredIPs []string
	for ip, lastAccess := range irl.lastAccess {
		if now.Sub(lastAccess) > expireTime {
			expiredIPs = append(expiredIPs, ip)
		}
	}

	// 删除过期的记录
	for _, ip := range expiredIPs {
		delete(irl.buckets, ip)
		delete(irl.lastAccess, ip)
	}

	if len(expiredIPs) > 0 {
		core.LogDebug("清理过期IP记录:", len(expiredIPs), "个")
	}
}

// Stop 停止频率限制器
func (irl *IPRateLimiter) Stop() {
	close(irl.stopCleanup)
	core.LogInfo("IP频率限制器已停止")
}

// GetStats 获取统计信息
func (irl *IPRateLimiter) GetStats() map[string]interface{} {
	irl.mu.RLock()
	defer irl.mu.RUnlock()

	stats := map[string]interface{}{
		"total_ips":        len(irl.buckets),
		"whitelist_count":  len(irl.whitelist),
		"blacklist_count":  len(irl.blacklist),
		"requests_per_sec": irl.config.RequestsPerSecond,
		"burst_capacity":   irl.config.BurstCapacity,
	}

	return stats
}

// AddToWhitelist 添加IP到白名单
func (irl *IPRateLimiter) AddToWhitelist(ip string) {
	irl.mu.Lock()
	defer irl.mu.Unlock()

	irl.whitelist[ip] = true
	core.LogInfo("IP已添加到白名单:", ip)
}

// AddToBlacklist 添加IP到黑名单
func (irl *IPRateLimiter) AddToBlacklist(ip string) {
	irl.mu.Lock()
	defer irl.mu.Unlock()

	irl.blacklist[ip] = true
	core.LogInfo("IP已添加到黑名单:", ip)
}

// RemoveFromWhitelist 从白名单移除IP
func (irl *IPRateLimiter) RemoveFromWhitelist(ip string) {
	irl.mu.Lock()
	defer irl.mu.Unlock()

	delete(irl.whitelist, ip)
	core.LogInfo("IP已从白名单移除:", ip)
}

// RemoveFromBlacklist 从黑名单移除IP
func (irl *IPRateLimiter) RemoveFromBlacklist(ip string) {
	irl.mu.Lock()
	defer irl.mu.Unlock()

	delete(irl.blacklist, ip)
	core.LogInfo("IP已从黑名单移除:", ip)
}

// GlobalRateLimiterManager 全局频率限制管理器
type GlobalRateLimiterManager struct {
	httpLimiter      *IPRateLimiter
	websocketLimiter *IPRateLimiter
	mu               sync.RWMutex
}

var globalManager *GlobalRateLimiterManager
var once sync.Once

// GetGlobalRateLimiterManager 获取全局频率限制管理器实例
func GetGlobalRateLimiterManager() *GlobalRateLimiterManager {
	once.Do(func() {
		globalManager = &GlobalRateLimiterManager{}
	})
	return globalManager
}

// InitHTTPRateLimiter 初始化HTTP频率限制器
func (grm *GlobalRateLimiterManager) InitHTTPRateLimiter(config *RateLimiterConfig) {
	grm.mu.Lock()
	defer grm.mu.Unlock()

	if grm.httpLimiter != nil {
		grm.httpLimiter.Stop()
	}

	grm.httpLimiter = NewIPRateLimiter(config)
	core.LogInfo("HTTP频率限制器已初始化")
}

// InitWebSocketRateLimiter 初始化WebSocket频率限制器
func (grm *GlobalRateLimiterManager) InitWebSocketRateLimiter(config *RateLimiterConfig) {
	grm.mu.Lock()
	defer grm.mu.Unlock()

	if grm.websocketLimiter != nil {
		grm.websocketLimiter.Stop()
	}

	grm.websocketLimiter = NewIPRateLimiter(config)
	core.LogInfo("WebSocket频率限制器已初始化")
}

// CheckHTTPRateLimit 检查HTTP请求频率限制
func (grm *GlobalRateLimiterManager) CheckHTTPRateLimit(ip string) bool {
	grm.mu.RLock()
	defer grm.mu.RUnlock()

	if grm.httpLimiter == nil {
		return true // 如果未初始化，则允许所有请求
	}

	return grm.httpLimiter.IsAllowed(ip)
}

// CheckWebSocketRateLimit 检查WebSocket连接频率限制
func (grm *GlobalRateLimiterManager) CheckWebSocketRateLimit(ip string) bool {
	grm.mu.RLock()
	defer grm.mu.RUnlock()

	if grm.websocketLimiter == nil {
		return true // 如果未初始化，则允许所有连接
	}

	return grm.websocketLimiter.IsAllowed(ip)
}

// GetHTTPStats 获取HTTP频率限制统计信息
func (grm *GlobalRateLimiterManager) GetHTTPStats() map[string]interface{} {
	grm.mu.RLock()
	defer grm.mu.RUnlock()

	if grm.httpLimiter == nil {
		return map[string]interface{}{"status": "not_initialized"}
	}

	return grm.httpLimiter.GetStats()
}

// GetWebSocketStats 获取WebSocket频率限制统计信息
func (grm *GlobalRateLimiterManager) GetWebSocketStats() map[string]interface{} {
	grm.mu.RLock()
	defer grm.mu.RUnlock()

	if grm.websocketLimiter == nil {
		return map[string]interface{}{"status": "not_initialized"}
	}

	return grm.websocketLimiter.GetStats()
}

// Stop 停止所有频率限制器
func (grm *GlobalRateLimiterManager) Stop() {
	grm.mu.Lock()
	defer grm.mu.Unlock()

	if grm.httpLimiter != nil {
		grm.httpLimiter.Stop()
		grm.httpLimiter = nil
	}

	if grm.websocketLimiter != nil {
		grm.websocketLimiter.Stop()
		grm.websocketLimiter = nil
	}

	core.LogInfo("全局频率限制管理器已停止")
}
