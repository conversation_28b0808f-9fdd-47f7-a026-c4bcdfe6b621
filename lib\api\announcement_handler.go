package api

import (
	"fmt"
	"net/http"
	"strconv"

	"zone/game/models"
	"zone/lib/core"
	"zone/lib/db"
	"zone/lib/storage"

	"github.com/gin-gonic/gin"
)

// AnnouncementHandler 公告API处理器
type AnnouncementHandler struct{}

// NewAnnouncementHandler 创建公告处理器
func NewAnnouncementHandler() *AnnouncementHandler {
	return &AnnouncementHandler{}
}

// CreateAnnouncementRequest 创建公告请求结构
type CreateAnnouncementRequest struct {
	Content   string `json:"content" binding:"required,min=1,max=512"`
	StartTime int64  `json:"start_time" binding:"required"`
	EndTime   int64  `json:"end_time" binding:"required"`
	Priority  int    `json:"priority"`
	// 注意：已移除target_rooms字段，公告现在面向所有房间
}

// UpdateAnnouncementRequest 更新公告请求结构
type UpdateAnnouncementRequest struct {
	Title     string `json:"title" binding:"required,min=1,max=255"`
	Content   string `json:"content" binding:"required,min=1,max=512"`
	StartTime int64  `json:"start_time" binding:"required"`
	EndTime   int64  `json:"end_time" binding:"required"`
	Status    int    `json:"status" binding:"min=0,max=1"`
	Priority  int    `json:"priority"`
	// 注意：已移除target_rooms字段，公告现在面向所有房间
}

// AnnouncementListRequest 公告列表请求结构
type AnnouncementListRequest struct {
	Page     int `form:"page,default=1" binding:"min=1"`
	PageSize int `form:"page_size,default=10" binding:"min=1,max=100"`
	Status   int `form:"status,default=-1" binding:"min=-1,max=1"`
}

// APIResponse 标准API响应结构
type APIResponse struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// AnnouncementListResponse 公告列表响应结构
type AnnouncementListResponse struct {
	Announcements []AnnouncementInfo `json:"announcements"`
	Total         int                `json:"total"`
	Page          int                `json:"page"`
	PageSize      int                `json:"page_size"`
}

// AnnouncementInfo 公告信息结构
type AnnouncementInfo struct {
	ID          int    `json:"id"`
	Content     string `json:"content"`
	StartTime   int64  `json:"start_time"`
	EndTime     int64  `json:"end_time"`
	Status      int    `json:"status"`
	Priority    int    `json:"priority"`
	CreatedTime int64  `json:"created_time"`
	CreatedBy   string `json:"created_by"`
	// 注意：已移除target_rooms字段，公告现在面向所有房间
}

// CreateAnnouncement 创建公告
func (h *AnnouncementHandler) CreateAnnouncement(c *gin.Context) {
	// 添加调试日志 - 方法入口
	var req CreateAnnouncementRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, APIResponse{
			Code:    400,
			Message: "请求参数错误: " + err.Error(),
		})
		return
	}

	// 验证时间逻辑
	if req.EndTime < req.StartTime {
		c.JSON(http.StatusBadRequest, APIResponse{
			Code:    400,
			Message: "结束时间不能早于开始时间",
		})
		return
	}

	// 创建公告对象
	announcement := &models.AnnouncementDB{
		Content:     req.Content,
		StartTime:   req.StartTime,
		EndTime:     req.EndTime,
		Status:      1, // 默认启用
		Priority:    req.Priority,
		CreatedTime: core.TimeServer().Unix(),
		UpdatedTime: core.TimeServer().Unix(),
		CreatedBy:   "api", // 可以从认证信息中获取
	}

	// 插入数据库
	lastId := db.InsertTable(storage.TABLE_Announcement, announcement, 0, false)
	if lastId == 0 {
		c.JSON(http.StatusInternalServerError, APIResponse{
			Code:    500,
			Message: "创建公告失败",
		})
		return
	}

	// 设置生成的ID
	announcement.Id = int(lastId)

	// 初始化数据库接口
	announcement.Init(storage.TABLE_Announcement, announcement, false)

	// 转换为响应格式
	announcementInfo := AnnouncementInfo{
		ID:          announcement.Id,
		Content:     announcement.Content,
		StartTime:   announcement.StartTime,
		EndTime:     announcement.EndTime,
		Status:      announcement.Status,
		Priority:    announcement.Priority,
		CreatedTime: announcement.CreatedTime,
		CreatedBy:   announcement.CreatedBy,
	}

	c.JSON(http.StatusOK, APIResponse{
		Code:    200,
		Message: "公告创建成功",
		Data:    announcementInfo,
	})
}

// GetAnnouncementList 获取公告列表
func (h *AnnouncementHandler) GetAnnouncementList(c *gin.Context) {
	var req AnnouncementListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		c.JSON(http.StatusBadRequest, APIResponse{
			Code:    400,
			Message: "请求参数错误: " + err.Error(),
		})
		return
	}

	// 计算偏移量
	offset := (req.Page - 1) * req.PageSize

	// 查询总数
	var countQuery string
	var total int

	if req.Status >= 0 {
		countQuery = fmt.Sprintf("SELECT COUNT(*) FROM `%s` WHERE status = %d", storage.TABLE_Announcement, req.Status)
	} else {
		countQuery = fmt.Sprintf("SELECT COUNT(*) FROM `%s`", storage.TABLE_Announcement)
	}

	rows, err := db.GetDBMgr().DBUser.QueryAny(countQuery)
	if err != nil {
		core.LogError("查询公告总数失败:", err)
		c.JSON(http.StatusInternalServerError, APIResponse{
			Code:    500,
			Message: "查询失败",
		})
		return
	}
	defer rows.Close()

	if rows.Next() {
		err = rows.Scan(&total)
		if err != nil {
			core.LogError("扫描总数失败:", err)
			c.JSON(http.StatusInternalServerError, APIResponse{
				Code:    500,
				Message: "查询失败",
			})
			return
		}
	}

	// 查询公告列表
	var listQuery string
	if req.Status >= 0 {
		listQuery = fmt.Sprintf("SELECT * FROM `%s` WHERE status = %d ORDER BY priority DESC, created_time DESC LIMIT %d OFFSET %d",
			storage.TABLE_Announcement, req.Status, req.PageSize, offset)
	} else {
		listQuery = fmt.Sprintf("SELECT * FROM `%s` ORDER BY priority DESC, created_time DESC LIMIT %d OFFSET %d",
			storage.TABLE_Announcement, req.PageSize, offset)
	}

	var announcementTemplate models.AnnouncementDB
	results := db.GetDBMgr().DBUser.GetAllData(listQuery, &announcementTemplate)

	// 检查查询结果
	if results == nil {
		core.LogError("查询公告列表失败:", listQuery)
		c.JSON(http.StatusInternalServerError, APIResponse{
			Code:    500,
			Message: "查询失败",
		})
		return
	}

	// 转换为响应格式
	announcements := make([]AnnouncementInfo, 0, len(results))
	for _, result := range results {
		announcementData := result.(*models.AnnouncementDB)

		announcements = append(announcements, AnnouncementInfo{
			ID:          announcementData.Id,
			Content:     announcementData.Content,
			StartTime:   announcementData.StartTime,
			EndTime:     announcementData.EndTime,
			Status:      announcementData.Status,
			Priority:    announcementData.Priority,
			CreatedTime: announcementData.CreatedTime,
			CreatedBy:   announcementData.CreatedBy,
		})
	}

	response := AnnouncementListResponse{
		Announcements: announcements,
		Total:         total,
		Page:          req.Page,
		PageSize:      req.PageSize,
	}

	c.JSON(http.StatusOK, APIResponse{
		Code:    0,
		Message: "查询成功",
		Data:    response,
	})
}

// UpdateAnnouncement 更新公告
func (h *AnnouncementHandler) UpdateAnnouncement(c *gin.Context) {
	// 获取公告ID
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, APIResponse{
			Code:    400,
			Message: "无效的公告ID",
		})
		return
	}

	var req UpdateAnnouncementRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, APIResponse{
			Code:    400,
			Message: "请求参数错误: " + err.Error(),
		})
		return
	}

	// 验证时间逻辑
	if req.EndTime < req.StartTime {
		c.JSON(http.StatusBadRequest, APIResponse{
			Code:    400,
			Message: "结束时间不能早于开始时间",
		})
		return
	}

	// 查询现有公告
	var announcement models.AnnouncementDB
	query := fmt.Sprintf("SELECT * FROM `%s` WHERE id = %d", storage.TABLE_Announcement, id)
	if !db.GetDBMgr().DBUser.GetOneData(query, &announcement, "", 0) {
		core.LogError("查询公告失败或公告不存在", "ID:", id, "查询:", query)
		c.JSON(http.StatusNotFound, APIResponse{
			Code:    404,
			Message: "公告不存在",
		})
		return
	}

	// 更新公告数据
	announcement.Content = req.Content
	announcement.StartTime = req.StartTime
	announcement.EndTime = req.EndTime
	announcement.Status = req.Status
	announcement.Priority = req.Priority
	announcement.UpdatedTime = core.TimeServer().Unix()

	// 初始化数据库接口并保存
	announcement.Init(storage.TABLE_Announcement, &announcement, false)
	announcement.Update(false, false)

	// 注意：Update方法没有返回值，我们通过其他方式验证更新是否成功
	// 可以通过重新查询数据库来验证更新是否成功，但在这里我们假设更新成功
	core.LogInfo("公告更新操作已执行", "ID:", id, "标题:", req.Title)

	// 转换为响应格式
	announcementInfo := AnnouncementInfo{
		ID:          announcement.Id,
		Content:     announcement.Content,
		StartTime:   announcement.StartTime,
		EndTime:     announcement.EndTime,
		Status:      announcement.Status,
		Priority:    announcement.Priority,
		CreatedTime: announcement.CreatedTime,
		CreatedBy:   announcement.CreatedBy,
	}

	c.JSON(http.StatusOK, APIResponse{
		Code:    0,
		Message: "公告更新成功",
		Data:    announcementInfo,
	})
}

// DeleteAnnouncement 删除公告
func (h *AnnouncementHandler) DeleteAnnouncement(c *gin.Context) {
	// 获取公告ID
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, APIResponse{
			Code:    400,
			Message: "无效的公告ID",
		})
		return
	}

	// 查询现有公告
	var announcement models.AnnouncementDB
	query := fmt.Sprintf("SELECT * FROM `%s` WHERE id = %d", storage.TABLE_Announcement, id)
	if !db.GetDBMgr().DBUser.GetOneData(query, &announcement, "", 0) {
		core.LogError("删除公告失败：查询公告失败或公告不存在", "ID:", id, "查询:", query)
		c.JSON(http.StatusNotFound, APIResponse{
			Code:    404,
			Message: "公告不存在",
		})
		return
	}

	// 删除公告
	deleteQuery := fmt.Sprintf("DELETE FROM `%s` WHERE id = %d", storage.TABLE_Announcement, id)
	_, _, success := db.GetDBMgr().DBUser.Exec(deleteQuery)
	if !success {
		core.LogError("删除公告失败", "ID:", id)
		c.JSON(http.StatusInternalServerError, APIResponse{
			Code:    500,
			Message: "删除公告失败",
		})
		return
	}

	c.JSON(http.StatusOK, APIResponse{
		Code:    0,
		Message: "公告删除成功",
	})
}

// GetAnnouncement 获取单个公告
func (h *AnnouncementHandler) GetAnnouncement(c *gin.Context) {
	// 获取公告ID
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, APIResponse{
			Code:    400,
			Message: "无效的公告ID",
		})
		return
	}

	// 查询公告
	var announcement models.AnnouncementDB
	query := fmt.Sprintf("SELECT * FROM `%s` WHERE id = %d", storage.TABLE_Announcement, id)
	if !db.GetDBMgr().DBUser.GetOneData(query, &announcement, "", 0) {
		core.LogError("获取公告失败：查询公告失败或公告不存在", "ID:", id, "查询:", query)
		c.JSON(http.StatusNotFound, APIResponse{
			Code:    404,
			Message: "公告不存在",
		})
		return
	}

	// 转换为响应格式
	announcementInfo := AnnouncementInfo{
		ID:          announcement.Id,
		Content:     announcement.Content,
		StartTime:   announcement.StartTime,
		EndTime:     announcement.EndTime,
		Status:      announcement.Status,
		Priority:    announcement.Priority,
		CreatedTime: announcement.CreatedTime,
		CreatedBy:   announcement.CreatedBy,
	}

	c.JSON(http.StatusOK, APIResponse{
		Code:    0,
		Message: "查询成功",
		Data:    announcementInfo,
	})
}
