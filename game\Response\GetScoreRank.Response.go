// Code generated by pb_exporter.go. DO NOT EDIT.
// Generated at: 2025-06-12 16:35:42

package Response

import (
	"zone/game/Request"
	"zone/game/mods"

	// "zone/lib/core"
	"zone/lib/network"
	"zone/pb/Message"
)

func GetScoreRankResponse(session *network.Session, msg any) {
	// 将 msg 转换为对应的 C2S 消息结构指针
	getScoreRank := msg.(*Message.GetScoreRankC2S)
	// core.LogDebug("GetScoreRankC2S:", getScoreRank)

	var re *Message.GetScoreRankS2C
	if getScoreRank.Type == 2 {
		re = mods.GetLiveTopMgr().GetWeekRank()
	}
	if getScoreRank.Type == 3 {
		re = mods.GetLiveTopMgr().GetMonthRank()
	}
	if getScoreRank.Type == 4 {
		re = mods.GetLiveTopMgr().GetWinRank()
	}
	if getScoreRank.Type == 5 {
		re = mods.GetLiveTopMgr().GetCallRank()
	}
	Request.GetScoreRankRequest(session, re.Rank, re.GetType())
	// TODO: 实现具体的业务逻辑
	// 从 getscorerank 中提取字段值
	// 处理业务逻辑
	// 发送响应消息

	// 示例：创建响应消息
	// GetScoreRankRequire(session, /* 参数 */)
}
