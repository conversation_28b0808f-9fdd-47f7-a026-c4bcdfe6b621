package app

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"log"
	"net"
	"net/http"
	"strings"
	"sync"
	"time"
	"zone/game"
	"zone/game/platform/bytedance"
	"zone/game/platform/kuaishou"
	"zone/lib"
	"zone/lib/api"
	"zone/lib/core"
	"zone/lib/db"
	"zone/lib/ratelimit"
	"zone/lib/storage"
	"zone/lib/utils"

	"github.com/BurntSushi/toml"
	"github.com/gin-gonic/gin"
)

const (
	// DefaultServerVersion is the default server version if not specified in config
	DefaultServerVersion = 1000
	// MainProcessServerID is the server ID for the main process server
	MainProcessServerID = 1001
	// SecondsPerDay represents the number of seconds in a day
	SecondsPerDay = 86400
	// OpeningHour is the hour (5 AM) when the server day starts
	OpeningHour = 5
)

// ZoneApp represents the main application instance for the zone server
type ZoneApp struct {
	config         *core.Config    // JSON configuration
	tomlConfig     core.ConfigToml // TOML configuration
	waitGroup      *sync.WaitGroup // synchronization for graceful shutdown
	isShutdown     bool            // shutdown flag
	worldLevel     int             // current world level
	isInitialized  bool            // initialization status
	timeZoneOffset int             // timezone offset in hours
	ginEngine      *gin.Engine     // Gin HTTP router engine
}

// GetServerName returns the server name from configuration
func (app *ZoneApp) GetServerName() string {
	return app.GetConfig().ServerName
}

// GetServerLang returns the server language setting
func (app *ZoneApp) GetServerLang() int {
	return app.GetConfig().Lang
}

// calculateDaysSince calculates the number of days between two timestamps
func (app *ZoneApp) calculateDaysSince(fromTime, toTime int64) int {
	timeDiff := toTime - fromTime
	days := int(timeDiff / SecondsPerDay)
	if timeDiff%SecondsPerDay >= 0 {
		days++
	}
	return days
}

// getServerOpenTime returns the parsed server opening time
func (app *ZoneApp) getServerOpenTime() time.Time {
	openTime, err := time.ParseInLocation(core.DATEFORMAT, app.GetConfig().OpenTime, time.Local)
	if err != nil {
		log.Printf("Warning: failed to parse server open time '%s': %v", app.GetConfig().OpenTime, err)
		return time.Now() // fallback to current time
	}
	return openTime
}

// GetLoginDay calculates the number of days from login time to now
func (app *ZoneApp) GetLoginDay(login string) int {
	// if login is a numeric day offset
	if dayOffset := utils.HF_Atoi(login); dayOffset > 0 {
		return 1 + app.GetOpenTime() - dayOffset
	}

	// parse login time string
	loginTime, err := time.ParseInLocation(core.DATEFORMAT, login, time.Local)
	if err != nil {
		log.Printf("Warning: failed to parse login time '%s': %v", login, err)
		return 0 // return 0 days if parsing fails
	}
	return app.calculateDaysSince(loginTime.Unix(), core.TimeServer().Unix())
}

// GetOpenTimeKeyDay returns a slice of day numbers from timeStart to current time
func (app *ZoneApp) GetOpenTimeKeyDay(timeStart int64) []int {
	openTime := app.getServerOpenTime()
	currentDays := app.calculateDaysSince(openTime.Unix(), core.TimeServer().Unix())
	startDays := app.calculateDaysSince(openTime.Unix(), timeStart)

	// build day range slice
	dayRange := make([]int, 0, currentDays-startDays+1)
	for day := startDays; day <= currentDays; day++ {
		dayRange = append(dayRange, day)
	}
	return dayRange
}

// GetOpenTimeAllDay returns the total number of days from timeStart to current time
func (app *ZoneApp) GetOpenTimeAllDay(timeStart int64) int {
	return len(app.GetOpenTimeKeyDay(timeStart))
}

// BroadCastMsgToCamp broadcasts a message to all players in a specific camp
func (app *ZoneApp) BroadCastMsgToCamp(camp int, head string, body []byte) {
	// TODO: implement actual broadcast functionality
	// player.GetPlayerMgr().BroadCastMsgToCamp(camp, head, body)
}

// singleton instance
var zoneAppInstance *ZoneApp

// GetZoneApp returns the singleton instance of ZoneApp
func GetZoneApp() *ZoneApp {
	if zoneAppInstance == nil {
		zoneAppInstance = &ZoneApp{
			isShutdown: false,
			waitGroup:  &sync.WaitGroup{},
		}
		zoneAppInstance.Init()
		// register global interface
		core.ZoneApp = zoneAppInstance
	}
	return zoneAppInstance
}

//! 停止服务器

// InitGinRouter initializes the Gin HTTP router with middleware and routes
func (app *ZoneApp) InitGinRouter() {
	// configure Gin for production mode (disables debug output)
	gin.SetMode(gin.ReleaseMode)

	// disable Gin's default logging output
	gin.DefaultWriter = ioutil.Discard

	// create new Gin engine
	app.ginEngine = gin.New()

	// add recovery middleware (without logger to keep output clean)
	app.ginEngine.Use(gin.Recovery())

	// 初始化频率限制器
	app.initRateLimiters()

	// 添加频率限制中间件
	app.addRateLimitMiddleware()

	// register WebSocket route using custom handler that bypasses Gin's ResponseWriter wrapper
	app.ginEngine.GET("/", app.createWebSocketHandler())

	// register HTTP API routes
	app.registerHTTPRoutes()
}

// createWebSocketHandler creates a WebSocket handler that properly handles the upgrade process
// This completely bypasses Gin's ResponseWriter wrapper to prevent hijacked connection warnings
func (app *ZoneApp) createWebSocketHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Check if this is a WebSocket upgrade request
		if !app.IsWebSocketRequest(c.Request) {
			c.AbortWithStatus(400)
			return
		}

		// 优化的WebSocket升级流程：
		// 1. 完全绕过Gin的响应处理机制
		// 2. 直接使用底层ResponseWriter
		// 3. 防止Gin在连接劫持后继续写入响应

		// 安全的ResponseWriter获取和处理
		responseWriter, err := app.GetSafeResponseWriter(c)
		if err != nil {
			core.LogError("获取安全ResponseWriter失败:", err)
			c.AbortWithStatus(http.StatusInternalServerError)
			return
		}

		// 安全检查：确保连接可以被安全劫持
		if !app.IsConnectionHijackSafe(responseWriter) {
			core.LogError("连接劫持安全检查失败")
			c.AbortWithStatus(http.StatusInternalServerError)
			return
		}

		// 关键优化：在WebSocket升级之前完全接管响应控制权
		// 这确保Gin不会在连接被劫持后尝试写入任何响应头

		// 直接调用WebSocket处理器，使用解包的ResponseWriter
		// 此时连接将被WebSocket升级器安全地劫持
		GetGateApp().GetConnectHandler().ServeHTTP(responseWriter, c.Request)

		// 重要：完全阻止Gin的后续处理
		// 使用Abort()确保不会有任何额外的响应操作
		c.Abort()
	}
}

// IsWebSocketRequest checks if the request is a valid WebSocket upgrade request
func (app *ZoneApp) IsWebSocketRequest(req *http.Request) bool {
	// Check for required WebSocket headers
	connectionHeader := req.Header.Get("Connection")
	upgradeHeader := req.Header.Get("Upgrade")

	// Connection header should contain "upgrade" (case-insensitive)
	hasUpgradeConnection := false
	for _, value := range strings.Split(connectionHeader, ",") {
		if strings.EqualFold(strings.TrimSpace(value), "upgrade") {
			hasUpgradeConnection = true
			break
		}
	}

	// Upgrade header should be "websocket" (case-insensitive)
	hasWebSocketUpgrade := strings.EqualFold(upgradeHeader, "websocket")

	// Method should be GET
	isGetMethod := req.Method == "GET"

	return hasUpgradeConnection && hasWebSocketUpgrade && isGetMethod
}

// registerHTTPRoutes registers all HTTP API routes
func (app *ZoneApp) registerHTTPRoutes() {
	// register ByteDance push API routes
	if app.config.Platform == 0 {
		bytedance.GetByteDancePayload().RegisterGinRoutes(app.ginEngine)
	} else {
		kuaishou.GetkuaishouPayload().RegisterGinRoutes(app.ginEngine)
	}

	// register announcement API routes
	api.RegisterAllAPIRoutes(app.ginEngine, app.config)
}

// StartService starts the zone server with initialization and HTTP service
func (app *ZoneApp) StartService() {
	app.Init()
	// initialize if not already done

	// start game service (blocking operation)
	game.GetGameApp().StartService(app.config)

	// create TCP listener
	listener, err := net.Listen("tcp", app.config.Host)
	if err != nil {
		log.Fatalf("failed to listen on %s: %v", app.config.Host, err)
	}

	log.Printf("Starting service on %s", app.config.Host)
	// start HTTP service using Gin engine
	log.Fatal(http.Serve(listener, app.ginEngine))
}

// StartHTTPSService starts HTTPS service on the specified port
func (app *ZoneApp) StartHTTPSService(port int) {
	server := &http.Server{
		Addr: fmt.Sprintf(":%d", port),
	}

	// start HTTPS listener in goroutine
	go func() {
		if err := server.ListenAndServeTLS("conf/server.crt", "conf/server.key"); err != nil && err != http.ErrServerClosed {
			core.LogFatal(err, "HTTPS server error: %v")
		}
	}()
}

// StopService stops the zone server
func (app *ZoneApp) StopService() {
	game.GetGameApp().StopService()
}

// Init initializes the zone application
func (app *ZoneApp) Init() {
	if app.isInitialized {
		return
	}
	app.isInitialized = true
	// initialize configuration
	app.InitConfig()
	// connect to database
	core.LogDebug("connecting to database...")
	app.ConnectDB()
	app.calculateTimeZoneOffset()
	// initialize WebSocket configuration service
	app.initServerUrlConfigService()
}

// initServerUrlConfigService 初始化ServerUrl配置服务（简化版）
func (app *ZoneApp) initServerUrlConfigService() {

	// 获取配置服务实例
	configService := storage.GetServerUrlService()

	// 初始化配置服务
	err := configService.Initialize()
	if err != nil {
		core.LogError("ServerUrl配置服务初始化失败: %v", err)
		// 不要让服务器启动失败，只记录错误
		return
	}
}

// calculateTimeZoneOffset calculates the timezone offset
func (app *ZoneApp) calculateTimeZoneOffset() {
	nowTime := core.TimeServer().Unix()
	// convert timestamp to date
	configTime := time.Unix(nowTime, 0)
	// format as date string
	timeStr := configTime.In(time.FixedZone("CST", 8*3600)).Format("2006-01-02 15:04:05")
	// parse back to time
	localTime, _ := time.ParseInLocation("2006-01-02 15:04:05", timeStr, time.Local)
	// get new timestamp
	newTime := localTime.Unix()
	app.timeZoneOffset = 8 + int((nowTime-newTime)/3600)
}

// GetServerId returns the server ID from configuration
func (app *ZoneApp) GetServerId() int {
	return app.config.ServerId
}

// ConnectDB establishes database connections
func (app *ZoneApp) ConnectDB() {
	// initialize Redis module
	redisInitialized := db.GetRedisMgr().Init(
		app.config.DBConf.Redis,
		app.config.DBConf.RedisDB,
		app.config.DBConf.RedisAuth,
	)
	if !redisInitialized {
		log.Fatal("failed to initialize Redis connection")
	}

	// initialize database module
	db.GetDBMgr().Init(app.config.DBConf.DBUser, app.config.DBConf.DBLog)

	// configure SQL manager
	lib.GetSqlMgr().SetDNS(app.config.DBConf.DBUser, app.config.DBConf.DBLog)
}

// Close gracefully shuts down the zone application
func (app *ZoneApp) Close() {
	app.isShutdown = true

	// 停止性能监控
	app.stopPerformanceMonitoring()

	// 停止频率限制器
	ratelimit.GetGlobalRateLimiterManager().Stop()

	app.StopService()
	// close database connections
	core.LogInfo("closing database connections...")
	db.GetDBMgr().Close()

	// wait for all goroutines to complete
	core.LogInfo("waiting for graceful shutdown...")
	app.waitGroup.Wait()

	// server shutdown complete
	core.LogFatal("server shutdown complete")
}

// IsClosed returns whether the application is shutting down
func (app *ZoneApp) IsClosed() bool {
	return app.isShutdown
}

// GetConfig returns the application configuration
func (app *ZoneApp) GetConfig() *core.Config {
	return app.config
}

// GetGinEngine returns the Gin engine instance
func (app *ZoneApp) GetGinEngine() *gin.Engine {
	return app.ginEngine
}

// IsProcess returns whether this server is the main process server
func (app *ZoneApp) IsProcess() bool {
	return app.config.ServerId == MainProcessServerID
}

// GetPlayerOnline returns the number of online players for a server
func (app *ZoneApp) GetPlayerOnline(serverId int) int {
	return 0 // TODO: implement actual player count
}

// GetPlayerTotal returns the total number of players
func (app *ZoneApp) GetPlayerTotal() int {
	return 1000 // TODO: implement actual player count
}

// InitConfig loads configuration files
func (app *ZoneApp) InitConfig() {
	// load JSON configuration
	configFile, err := ioutil.ReadFile("./config.json")
	if err != nil {
		log.Fatal("failed to read config.json:", err)
	}

	if err := json.Unmarshal(configFile, &app.config); err != nil {
		log.Fatal("failed to parse config.json:", err)
	}

	// set default server version if not specified
	if app.config.ServerVer == 0 {
		app.config.ServerVer = DefaultServerVersion
	}

	// configure logging
	core.GetLogMgr().SetLevel(app.config.LogConf.LogLevel, app.config.LogConf.LogConsole)

	// load TOML configuration (optional)
	if _, err := toml.DecodeFile("zone.toml", &app.tomlConfig); err != nil {
		fmt.Printf("Warning: failed to load zone.toml: %v\n", err)
	}
}

// Wait adds to the wait group for graceful shutdown
func (app *ZoneApp) Wait() {
	app.waitGroup.Add(1)
	core.LogDebug("waitgroup: added 1")
}

// Done decrements the wait group counter
func (app *ZoneApp) Done() {
	app.waitGroup.Done()
	core.LogDebug("waitgroup: done 1")
}

// GetWorldLevel returns the server world level
func (app *ZoneApp) GetWorldLevel(refresh bool) int {
	return db.GetDBMgr().GetWorldLevel(refresh)
}

// GetOpenTime returns the number of days since server opened (starting from 5 AM on opening day)
func (app *ZoneApp) GetOpenTime() int {
	openTime := app.getServerOpenTime()
	return app.calculateDaysSince(openTime.Unix(), core.TimeServer().Unix())
}

// GetOpenServer returns the server opening timestamp
func (app *ZoneApp) GetOpenServer() int64 {
	return app.getServerOpenTime().Unix()
}

// GetOpenDay returns the opening day timestamp (5 AM on opening day)
func (app *ZoneApp) GetOpenDay() int64 {
	openTime := app.getServerOpenTime()
	openingDay := time.Date(openTime.Year(), openTime.Month(), openTime.Day(), OpeningHour, 0, 0, 0, time.Local)

	// if server opened after 5 AM, use next day's 5 AM
	if openTime.Unix() > openingDay.Unix() {
		return openingDay.Unix() + int64(core.DAY_SECS)
	}
	return openingDay.Unix()
}

// GetTimeArea returns the timezone offset
func (app *ZoneApp) GetTimeArea() int {
	return app.timeZoneOffset
}

// IsHasName checks if a player name already exists
// TODO: implement caching for better performance
func (app *ZoneApp) IsHasName(name string) bool {
	// TODO: implement actual name checking logic
	return false
}

// GetSafeResponseWriter 安全地获取用于WebSocket升级的ResponseWriter
// 这个方法实现了多层安全检查，防止hijacked connection警告
func (app *ZoneApp) GetSafeResponseWriter(c *gin.Context) (http.ResponseWriter, error) {
	// 使用defer和recover来捕获可能的panic
	defer func() {
		if panicInfo := recover(); panicInfo != nil {
			core.LogError("获取安全ResponseWriter时发生panic:", panicInfo)
		}
	}()

	// 第一层：尝试解包Gin的ResponseWriter包装器
	if unwrapper, ok := c.Writer.(interface{ Unwrap() http.ResponseWriter }); ok {
		// core.LogDebug("成功解包Gin ResponseWriter")
		return unwrapper.Unwrap(), nil
	}

	// 第二层：检查是否已经是标准的http.ResponseWriter
	if _, ok := c.Writer.(http.ResponseWriter); ok {
		// 重要：不要预防性地写入响应头，这可能导致问题
		// 让WebSocket升级器来处理响应头的写入
		// core.LogDebug("使用Gin包装的ResponseWriter")
		return c.Writer, nil
	}

	// 第三层：降级处理 - 直接使用
	// core.LogDebug("使用降级处理的ResponseWriter")
	return c.Writer, nil
}

// IsConnectionHijackSafe 检查连接是否可以安全地被劫持
func (app *ZoneApp) IsConnectionHijackSafe(w http.ResponseWriter) bool {
	// 检查是否实现了Hijacker接口
	if _, ok := w.(http.Hijacker); !ok {
		core.LogError("ResponseWriter不支持连接劫持")
		return false
	}

	// 检查是否已经写入了响应体
	if ginWriter, ok := w.(gin.ResponseWriter); ok {
		if ginWriter.Written() && ginWriter.Size() > 0 {
			core.LogError("响应体已写入，劫持可能不安全")
			return false
		}
	}

	return true
}

// initRateLimiters 初始化频率限制器
func (app *ZoneApp) initRateLimiters() {
	// 检查是否启用频率限制
	if app.config.RateLimitConf == nil || !app.config.RateLimitConf.Enabled {
		core.LogInfo("频率限制功能已禁用")
		return
	}

	// 获取全局频率限制管理器
	manager := ratelimit.GetGlobalRateLimiterManager()

	// 创建HTTP频率限制配置
	httpConfig := &ratelimit.RateLimiterConfig{
		RequestsPerSecond: app.config.RateLimitConf.HTTPRequestsPerSec,
		BurstCapacity:     app.config.RateLimitConf.HTTPBurstCapacity,
		CleanupInterval:   time.Duration(app.config.RateLimitConf.CleanupIntervalSec) * time.Second,
		IPWhitelist:       app.config.RateLimitConf.IPWhitelist,
		IPBlacklist:       app.config.RateLimitConf.IPBlacklist,
	}

	// 创建WebSocket频率限制配置
	wsConfig := &ratelimit.RateLimiterConfig{
		RequestsPerSecond: app.config.RateLimitConf.WSRequestsPerSec,
		BurstCapacity:     app.config.RateLimitConf.WSBurstCapacity,
		CleanupInterval:   time.Duration(app.config.RateLimitConf.CleanupIntervalSec) * time.Second,
		IPWhitelist:       app.config.RateLimitConf.IPWhitelist,
		IPBlacklist:       app.config.RateLimitConf.IPBlacklist,
	}

	// 初始化频率限制器
	manager.InitHTTPRateLimiter(httpConfig)
	manager.InitWebSocketRateLimiter(wsConfig)

	// 启动性能监控和内存优化
	app.startPerformanceMonitoring()

	core.LogInfo("频率限制器初始化完成",
		"HTTP限制:", httpConfig.RequestsPerSecond, "req/s",
		"WebSocket限制:", wsConfig.RequestsPerSecond, "conn/s")
}

// addRateLimitMiddleware 添加频率限制中间件
func (app *ZoneApp) addRateLimitMiddleware() {
	// 检查是否启用频率限制
	if app.config.RateLimitConf == nil || !app.config.RateLimitConf.Enabled {
		return
	}

	// 添加IP黑名单中间件（最高优先级）
	if len(app.config.RateLimitConf.IPBlacklist) > 0 {
		app.ginEngine.Use(ratelimit.IPBlacklistMiddleware(app.config.RateLimitConf.IPBlacklist))
	}

	// 添加IP白名单中间件
	if len(app.config.RateLimitConf.IPWhitelist) > 0 {
		app.ginEngine.Use(ratelimit.IPWhitelistMiddleware(app.config.RateLimitConf.IPWhitelist))
	}

	// 添加HTTP频率限制中间件
	app.ginEngine.Use(ratelimit.HTTPRateLimitMiddleware())

	// 添加WebSocket频率限制中间件
	app.ginEngine.Use(ratelimit.WebSocketRateLimitMiddleware())

	// 添加频率限制统计中间件（用于监控）
	app.ginEngine.Use(ratelimit.RateLimitStatsMiddleware())

	core.LogInfo("频率限制中间件已添加")
}

// startPerformanceMonitoring 启动性能监控
func (app *ZoneApp) startPerformanceMonitoring() {
	// 启动性能监控器
	monitor := ratelimit.GetGlobalPerformanceMonitor()
	monitor.Start()

	// 启动内存优化器
	optimizer := ratelimit.GetGlobalMemoryOptimizer()
	optimizer.Start()

	core.LogInfo("性能监控和内存优化已启动")
}

// stopPerformanceMonitoring 停止性能监控
func (app *ZoneApp) stopPerformanceMonitoring() {
	// 停止性能监控器
	monitor := ratelimit.GetGlobalPerformanceMonitor()
	monitor.Stop()

	// 停止内存优化器
	optimizer := ratelimit.GetGlobalMemoryOptimizer()
	optimizer.Stop()

	core.LogInfo("性能监控和内存优化已停止")
}
