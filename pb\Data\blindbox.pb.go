// Generated from Excel file: BlindBox.xlsx

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.5.1
// source: blindbox.proto

package Data

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Blindbox 配置数据
type Blindbox struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 编号
	Id int32 `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty"`
	// 礼物id
	Item []int32 `protobuf:"varint,2,rep,packed,name=item,proto3" json:"item,omitempty"`
	// 几率
	Probability   []float32 `protobuf:"fixed32,3,rep,packed,name=probability,proto3" json:"probability,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Blindbox) Reset() {
	*x = Blindbox{}
	mi := &file_blindbox_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Blindbox) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Blindbox) ProtoMessage() {}

func (x *Blindbox) ProtoReflect() protoreflect.Message {
	mi := &file_blindbox_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Blindbox.ProtoReflect.Descriptor instead.
func (*Blindbox) Descriptor() ([]byte, []int) {
	return file_blindbox_proto_rawDescGZIP(), []int{0}
}

func (x *Blindbox) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Blindbox) GetItem() []int32 {
	if x != nil {
		return x.Item
	}
	return nil
}

func (x *Blindbox) GetProbability() []float32 {
	if x != nil {
		return x.Probability
	}
	return nil
}

// BlindboxList 配置数据列表
type BlindboxList struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Items         []*Blindbox            `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BlindboxList) Reset() {
	*x = BlindboxList{}
	mi := &file_blindbox_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BlindboxList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlindboxList) ProtoMessage() {}

func (x *BlindboxList) ProtoReflect() protoreflect.Message {
	mi := &file_blindbox_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlindboxList.ProtoReflect.Descriptor instead.
func (*BlindboxList) Descriptor() ([]byte, []int) {
	return file_blindbox_proto_rawDescGZIP(), []int{1}
}

func (x *BlindboxList) GetItems() []*Blindbox {
	if x != nil {
		return x.Items
	}
	return nil
}

var File_blindbox_proto protoreflect.FileDescriptor

const file_blindbox_proto_rawDesc = "" +
	"\n" +
	"\x0eblindbox.proto\x12\x04Data\"P\n" +
	"\bBlindbox\x12\x0e\n" +
	"\x02Id\x18\x01 \x01(\x05R\x02Id\x12\x12\n" +
	"\x04item\x18\x02 \x03(\x05R\x04item\x12 \n" +
	"\vprobability\x18\x03 \x03(\x02R\vprobability\"4\n" +
	"\fBlindboxList\x12$\n" +
	"\x05items\x18\x01 \x03(\v2\x0e.Data.BlindboxR\x05itemsB\x0eZ\fzone/pb/Datab\x06proto3"

var (
	file_blindbox_proto_rawDescOnce sync.Once
	file_blindbox_proto_rawDescData []byte
)

func file_blindbox_proto_rawDescGZIP() []byte {
	file_blindbox_proto_rawDescOnce.Do(func() {
		file_blindbox_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_blindbox_proto_rawDesc), len(file_blindbox_proto_rawDesc)))
	})
	return file_blindbox_proto_rawDescData
}

var file_blindbox_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_blindbox_proto_goTypes = []any{
	(*Blindbox)(nil),     // 0: Data.Blindbox
	(*BlindboxList)(nil), // 1: Data.BlindboxList
}
var file_blindbox_proto_depIdxs = []int32{
	0, // 0: Data.BlindboxList.items:type_name -> Data.Blindbox
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_blindbox_proto_init() }
func file_blindbox_proto_init() {
	if File_blindbox_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_blindbox_proto_rawDesc), len(file_blindbox_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_blindbox_proto_goTypes,
		DependencyIndexes: file_blindbox_proto_depIdxs,
		MessageInfos:      file_blindbox_proto_msgTypes,
	}.Build()
	File_blindbox_proto = out.File
	file_blindbox_proto_goTypes = nil
	file_blindbox_proto_depIdxs = nil
}
