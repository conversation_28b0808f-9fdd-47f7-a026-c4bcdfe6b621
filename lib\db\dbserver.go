//! 数据库底层

package db

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"log"
	"reflect"
	"strings"
	"time"
	"zone/lib/core"
	"zone/lib/utils"

	_ "github.com/go-sql-driver/mysql"
	"github.com/gomodule/redigo/redis"
)

// ! 常数定义
const (
	Max_Open_Conn = 30
	Max_Idle_Conn = 10
	Max_Life_Time = 28800
)

// TimeScanner 用于处理数据库时间字段扫描，支持从[]uint8转换为time.Time
type TimeScanner struct {
	Target *time.Time
}

// Scan 实现 sql.Scanner 接口，支持从 []uint8 转换为 time.Time
func (ts *TimeScanner) Scan(value interface{}) error {
	if value == nil {
		*ts.Target = time.Time{}
		return nil
	}

	switch v := value.(type) {
	case time.Time:
		*ts.Target = v
		return nil
	case []byte:
		// MySQL datetime 格式: "2006-01-02 15:04:05"
		if len(v) == 0 {
			*ts.Target = time.Time{}
			return nil
		}
		t, err := time.Parse("2006-01-02 15:04:05", string(v))
		if err != nil {
			// 尝试其他常见格式
			t, err = time.Parse("2006-01-02", string(v))
			if err != nil {
				return fmt.Errorf("无法解析时间格式: %s, 错误: %v", string(v), err)
			}
		}
		*ts.Target = t
		return nil
	case string:
		if v == "" {
			*ts.Target = time.Time{}
			return nil
		}
		t, err := time.Parse("2006-01-02 15:04:05", v)
		if err != nil {
			t, err = time.Parse("2006-01-02", v)
			if err != nil {
				return fmt.Errorf("无法解析时间格式: %s, 错误: %v", v, err)
			}
		}
		*ts.Target = t
		return nil
	default:
		return fmt.Errorf("无法将 %T 类型转换为 time.Time", value)
	}
}

// ! 数据库结构
type DBServer struct {
	m_db     *sql.DB //! db
	m_dbName string  //! 库名
}

// ! 得到库名
func (self *DBServer) GetDBName() string {
	return self.m_dbName
}

func (self *DBServer) GetDB() *sql.DB {
	return self.m_db
}

// ! 连接数据库
// ! dsn root:Wnr*JS4*qUyy95ll@tcp(192.168.20.126:3306)/football_dynamic?charset=utf8&timeout=10s
func (self *DBServer) Init(dsn string) bool {
	self.parseDBName(dsn)

	db, err := sql.Open("mysql", dsn)
	db.SetMaxOpenConns(Max_Open_Conn)
	db.SetMaxIdleConns(Max_Idle_Conn)
	db.SetConnMaxLifetime(Max_Life_Time * time.Second)

	if err != nil {
		//db.Close()
		log.Fatalln("db open fail! err:%s dsn:%s", err.Error(), self.m_dbName)
	}

	err = db.Ping()
	if err != nil {
		db.Close()
		log.Fatalln("db open ping fail!  err:%s dns:%s", err.Error(), self.m_dbName)
	}

	self.m_db = db

	//log.Println("db connect!", self.m_dbName)
	core.LogDebug("db connect!", self.m_dbName)

	return true
}

func (self *DBServer) Close() {
	if self.m_db != nil {
		self.m_db.Close()
	}
}

func (self *DBServer) checkError(info string, sql string, err error) {
	log.Println(info, sql, ",err:", err)
	core.LogError(info, sql, ",err:", err)
}

// ! 执行语句
func (self *DBServer) Exec(query string, args ...interface{}) (int64, int64, bool) {
	if self.m_db == nil {
		self.checkError("db exec fail! query:", fmt.Sprintf(query, args...), fmt.Errorf("数据库连接未初始化"))
		return 0, 0, false
	}
	sql := fmt.Sprintf(query, args...)
	result, err := self.m_db.Exec(sql)
	if err != nil {
		self.checkError("db exec fail! query:", sql, err)
		return 0, 0, false
	}

	LastInsertId := int64(0)
	LastInsertId, err = result.LastInsertId()
	if err != nil {
		self.checkError("db exec-LastInsertId fail! query:", sql, err)
	}

	RowsAffected := int64(0)
	RowsAffected, err = result.RowsAffected()
	if err != nil {
		self.checkError("db exec-RowsAffected fail! query:", sql, err)
	}
	return LastInsertId, RowsAffected, true
}

// ! 得到一条数据
func (self *DBServer) GetOneData(query string, struc interface{}, table string, key int64) bool {
	if self.m_db == nil {
		log.Println("db GetOneData fail! query:", query, ",err: 数据库连接未初始化")
		core.LogError("db GetOneData fail! query:", query, ",err: 数据库连接未初始化")
		return false
	}
	if table != "" && key != 0 {
		c := GetRedisMgr().GetRedisConn()
		defer c.Close()

		v, err := redis.Bytes(c.Do("GET", fmt.Sprintf("%s_%d", table, key)))
		if err == nil {
			json.Unmarshal(v, struc)
			//log.Println("从redis读取", string(v))
		} else {
			//log.Println("从数据库读取", query)
			rows, err := self.m_db.Query(query)
			defer rows.Close()
			if rows == nil || err != nil {
				log.Println("db GetOneData fail! query:", query, ",err:", err)
				core.LogError("db GetOneData fail! query:", query, ",err:", err)
				return false
			}

			//! 得到反射
			s := reflect.ValueOf(struc).Elem()
			num := s.NumField()
			data := make([]interface{}, 0)
			for i := 0; i < num; i++ {
				field := s.Field(i)
				fieldType := field.Type()

				// 检查是否为支持的类型
				ki := field.Kind()
				isTimeType := fieldType.String() == "time.Time"
				isBasicType := ki == reflect.Int || ki == reflect.Int64 || ki == reflect.Int8 ||
					ki == reflect.String || ki == reflect.Float32 || ki == reflect.Float64

				if !isBasicType && !isTimeType {
					continue
				}

				data = append(data, field.Addr().Interface())
			}

			has := false
			for rows.Next() {
				has = true
				err = rows.Scan(data...)
				if err != nil {
					log.Println("db GetOneData-Scan fail! query:", query, ",err:", err)
					core.LogError("db GetOneData-Scan fail! query:", query, ",err:", err)
					return false
				}
				break
			}
			//! 记录到redis
			if has {
				c.Do("SETEX", fmt.Sprintf("%s_%d", table, key), 864000, utils.HF_JtoB(struc))
				return true
			}

			return has
		}
		return true
	}

	rows, err := self.m_db.Query(query)
	if rows != nil {
		defer rows.Close()
	}
	if rows == nil || err != nil {
		log.Println("db GetOneData fail! query:", query, ",err:", err)
		core.LogError("db GetOneData fail! query:", query, ",err:", err)
		return false
	}

	//! 得到反射
	s := reflect.ValueOf(struc).Elem()
	num := s.NumField()
	data := make([]interface{}, 0)
	for i := 0; i < num; i++ {
		field := s.Field(i)
		fieldType := field.Type()

		// 检查是否为支持的类型
		ki := field.Kind()
		isTimeType := fieldType.String() == "time.Time"
		isBasicType := ki == reflect.Int || ki == reflect.Int64 || ki == reflect.Int8 ||
			ki == reflect.String || ki == reflect.Float32 || ki == reflect.Float64

		if !isBasicType && !isTimeType {
			continue
		}

		data = append(data, field.Addr().Interface())
	}

	has := false
	for rows.Next() {
		err = rows.Scan(data...)
		if err != nil {
			log.Println("db GetOneData-Scan fail! query:", query, ",err:", err)
			core.LogError("db GetOneData-Scan fail! query:", query, ",err:", err)
			return false
		}
		has = true
		break
	}

	return has
}

// ! 得到多条数据
func (self *DBServer) GetAllData(query string, struc interface{}) []interface{} {
	if self.m_db == nil {
		log.Println("db GetAllData fail! query:", query, ",err: 数据库连接未初始化")
		core.LogError("db GetAllData fail! query:", query, ",err: 数据库连接未初始化")
		return nil
	}
	rows, err := self.m_db.Query(query)
	if rows != nil {
		defer rows.Close()
	}
	if rows == nil || err != nil {
		log.Println("db GetAllData fail! query:", query, ",err:", err)
		core.LogError("db GetAllData fail! query:", query, ",err:", err)
		return nil
	}

	//! 得到反射
	s := reflect.ValueOf(struc).Elem()
	num := s.NumField()
	data := make([]interface{}, 0)
	for i := 0; i < num; i++ {
		field := s.Field(i)
		fieldType := field.Type()

		// 检查是否为支持的类型
		ki := field.Kind()
		isTimeType := fieldType.String() == "time.Time"
		isBasicType := ki == reflect.Int || ki == reflect.Int64 || ki == reflect.Int8 ||
			ki == reflect.String || ki == reflect.Float32 || ki == reflect.Float64

		if !isBasicType && !isTimeType {
			continue
		}

		data = append(data, field.Addr().Interface())
	}

	result := make([]interface{}, 0)
	for rows.Next() {
		err = rows.Scan(data...)
		if err != nil {
			log.Println("db GetAllData-Scan fail! query:", query, ",err:", err)
			core.LogError("db GetAllData-Scan fail! query:", query, ",err:", err)
			return nil
		}
		newObj := reflect.New(reflect.TypeOf(struc).Elem()).Elem()
		newObj.Set(s)
		result = append(result, newObj.Addr().Interface())
	}

	return result
}

// ! 解析库名
func (self *DBServer) parseDBName(dsn string) {
	name := dsn
	begin := strings.Index(name, "/")
	end := strings.Index(name, "?")
	self.m_dbName = name[begin+1 : end]
}

// ! 检查是否含有字段
func (self *DBServer) Query(sql string) bool {
	if self.m_db == nil {
		log.Println("Query fail! query:", sql, ",err: 数据库连接未初始化")
		core.LogError("Query fail! query:", sql, ",err: 数据库连接未初始化")
		return false
	}
	rows, err := self.m_db.Query(sql)
	if rows != nil {
		defer rows.Close()
	}
	if rows == nil || err != nil {
		log.Println("Query fail! query:", sql, ",err:", err)
		core.LogError("Query fail! query:", sql, ",err:", err)
		return false
	}

	num := 0
	for rows.Next() {
		num += 1
	}
	return num > 0
}

func (self *DBServer) QueryAny(sql string) (*sql.Rows, error) {
	if self.m_db == nil {
		return nil, fmt.Errorf("数据库连接未初始化")
	}
	return self.m_db.Query(sql)
}

// ! 得到多条数据, 支持tag="-"
func (self *DBServer) GetAllDataEx(query string, struc interface{}) []interface{} {
	if self.m_db == nil {
		log.Println("db GetAllData fail! query:", query, ",err: 数据库连接未初始化")
		core.LogError("db GetAllData fail! query:", query, ",err: 数据库连接未初始化")
		return nil
	}
	rows, err := self.m_db.Query(query)
	if rows != nil {
		defer rows.Close()
	}
	if rows == nil || err != nil {
		log.Println("db GetAllData fail! query:", query, ",err:", err)
		core.LogError("db GetAllData fail! query:", query, ",err:", err)
		return nil
	}

	//! 得到反射
	v := reflect.ValueOf(struc).Elem()
	data := make([]interface{}, 0)
	for i := 0; i < v.NumField(); i++ {
		// Get the field tag value
		tag := v.Type().Field(i).Tag.Get("json")

		// Skip if tag is not defined or ignored
		if tag == "" || strings.Contains(tag, "-") {
			continue
		}

		field := v.Field(i)
		fieldType := field.Type()

		// 检查是否为支持的类型
		ki := field.Kind()
		isTimeType := fieldType.String() == "time.Time"
		isBasicType := ki == reflect.Int || ki == reflect.Int64 || ki == reflect.Int8 ||
			ki == reflect.String || ki == reflect.Float32 || ki == reflect.Float64

		if !isBasicType && !isTimeType {
			continue
		}

		data = append(data, field.Addr().Interface())
	}

	result := make([]interface{}, 0)
	for rows.Next() {
		err = rows.Scan(data...)
		if err != nil {
			log.Println("db GetAllDataEx-Scan fail! query:", query, ",err:", err)
			core.LogError("db GetAllDataEx-Scan fail! query:", query, ",err:", err)
			return nil
		}
		newObj := reflect.New(reflect.TypeOf(struc).Elem()).Elem()
		newObj.Set(v)
		result = append(result, newObj.Addr().Interface())
	}

	return result
}

func (self *DBServer) QueryColomn(sql string) (bool, string) {
	if self.m_db == nil {
		log.Println("Query fail! query:", sql, ",err: 数据库连接未初始化")
		core.LogError("Query fail! query:", sql, ",err: 数据库连接未初始化")
		return false, ""
	}
	rows, err := self.m_db.Query(sql)
	if rows != nil {
		defer rows.Close()
	}
	if rows == nil || err != nil {
		log.Println("Query fail! query:", sql, ",err:", err)
		core.LogError("Query fail! query:", sql, ",err:", err)
		return false, ""
	}

	var fileName string
	for rows.Next() {
		err = rows.Scan(&fileName)
		if err != nil {
			log.Println("Query fail! query:", sql, ",err:", err)
			core.LogError("Query fail! query:", sql, ",err:", err)
			return false, ""
		}
		break
	}

	if fileName == "" {
		return false, ""
	}

	return true, fileName
}
