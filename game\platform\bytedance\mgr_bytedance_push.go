package bytedance

import (
	"encoding/json"
	"zone/lib/core"
	"zone/lib/httpclient"

	"github.com/gin-gonic/gin"
)

const (
	// HTTPStatusOK HTTP成功状态码
	HTTPStatusOK = 200
	// EmptyResponse 空响应内容
	EmptyResponse = ""

	// ByteDance API相关常量
	HeaderNonceStr  = "x-nonce-str" // 随机字符串头部
	HeaderTimestamp = "x-timestamp" // 时间戳头部
	HeaderSignature = "x-signature" // 签名头部
	HeaderRoomID    = "x-roomid"    // 房间ID头部
	HeaderMsgType   = "x-msg-type"  // 消息类型头部
)

// 回调函数类型定义
type (
	// ResetDataCallback 数据重置回调函数类型
	ResetDataCallback func()

	// AddCommentCallback 添加评论回调函数类型
	AddCommentCallback func(roomId string, comment []byte)

	// AddGiftCallback 添加礼物回调函数类型
	AddGiftCallback func(roomId string, gifts []*Payload)
	// AddLikeCallback 添加点赞回调函数类型
	AddLikeCallback func(roomId string, like []byte)
	AddFansCallback func(roomId string, fans []*Payload)
)

// 全局回调函数变量
var (
	resetDataWeeklyCallback  ResetDataCallback
	resetDataMonthlyCallback ResetDataCallback
	addCommentCallback       AddCommentCallback
	addGiftCallback          AddGiftCallback
	addLikeCallback          AddLikeCallback
	addFansCallback          AddFansCallback
)

// RegisterCallbacks 注册回调函数
// 由mods包在初始化时调用，注册具体的实现函数
func RegisterCallbacks(
	weeklyReset ResetDataCallback,
	monthlyReset ResetDataCallback,
	addComment AddCommentCallback,
	addGift AddGiftCallback,
	addLike AddLikeCallback,
	addFans AddFansCallback,
) {
	resetDataWeeklyCallback = weeklyReset
	resetDataMonthlyCallback = monthlyReset
	addCommentCallback = addComment
	addGiftCallback = addGift
	addLikeCallback = addLike
	addFansCallback = addFans
	core.LogInfo("抖音：回调函数注册完成")
}

// ByteDancePayload ByteDance推送服务处理器
// 负责处理来自ByteDance平台的各种推送消息（评论、礼物、点赞等）
type ByteDancePayload struct {
	// 预留字段用于扩展
}

// ByteDance推送服务单例实例
var byteDancePayloadInstance *ByteDancePayload

// GetByteDancePayload 获取ByteDance推送服务单例实例
func GetByteDancePayload() *ByteDancePayload {
	if byteDancePayloadInstance == nil {
		byteDancePayloadInstance = &ByteDancePayload{}
	}
	return byteDancePayloadInstance
}

// RegisterGinRoutes 注册Gin HTTP路由
// router: Gin路由引擎实例
func (payload *ByteDancePayload) RegisterGinRoutes(router *gin.Engine) {
	// 注册ByteDance推送API路由
	router.POST("/dy/comment/push/", payload.GinPayloadComment)
	router.POST("/dy/like/push/", payload.GinPayloadLike)
	router.POST("/dy/gift/push/", payload.GinPayloadGift)
	router.POST("/dy/fans/push/", payload.GinPayloadFans)

	// 抖音傻逼的HeadCheck
	router.HEAD("/dy/comment/push/", payload.HeadCheck)
	router.HEAD("/dy/like/push/", payload.HeadCheck)
	router.HEAD("/dy/gift/push/", payload.HeadCheck)
	router.HEAD("/dy/fans/push/", payload.HeadCheck)

	// 注册测试API路由
	router.POST("/gifttest", payload.GinPayloadGiftTest)
	router.POST("/commenttest", payload.GinPayloadCommentTest)
	router.POST("/liketest", payload.GinPayloadLikeTest)
	router.POST("/fanstest", payload.GinPayloadFansTest)

	// 注册管理API路由
	router.POST("/weekclear", payload.GinWeekClear)
	router.POST("/monthclear", payload.GinMonthClear)

	core.LogInfo("ByteDance推送服务路由注册完成")
}

func (payload *ByteDancePayload) HeadCheck(context *gin.Context) {
	context.Status(200)
}

// readRequestBody 读取HTTP请求体数据
// context: Gin上下文对象
// 返回值: 请求体字节数据和错误信息
func (payload *ByteDancePayload) readRequestBody(context *gin.Context) ([]byte, error) {
	return httpclient.ReadRequestBody(context.Request.Body)
}

// sendSuccessResponse 发送成功响应
// context: Gin上下文对象
func (payload *ByteDancePayload) sendSuccessResponse(context *gin.Context) {
	context.Status(HTTPStatusOK)
}

// GinWeekClear 周数据清理处理器
// 处理周数据重置请求
func (payload *ByteDancePayload) GinWeekClear(context *gin.Context) {
	core.LogInfo("收到周数据清理请求")

	// 读取请求体（虽然不使用，但保持API一致性）
	_, err := payload.readRequestBody(context)
	if err != nil {
		return
	}

	// 执行周数据重置
	if resetDataWeeklyCallback != nil {
		resetDataWeeklyCallback()
		core.LogInfo("周数据清理完成")
	} else {
		core.LogError("周数据重置回调函数未注册")
	}

	payload.sendSuccessResponse(context)
}

// GinMonthClear 月数据清理处理器
// 处理月数据重置请求
func (payload *ByteDancePayload) GinMonthClear(context *gin.Context) {
	core.LogInfo("收到月数据清理请求")

	// 读取请求体（虽然不使用，但保持API一致性）
	_, err := payload.readRequestBody(context)
	if err != nil {
		return
	}

	// 执行月数据重置
	if resetDataMonthlyCallback != nil {
		resetDataMonthlyCallback()
		core.LogInfo("月数据清理完成")
	} else {
		core.LogError("月数据重置回调函数未注册")
	}

	payload.sendSuccessResponse(context)
}

// GinPayloadGiftTest ! Gin版本的礼物测试处理器
func (payload *ByteDancePayload) GinPayloadGiftTest(c *gin.Context) {
	result, err := httpclient.ReadRequestBody(c.Request.Body)
	if err != nil {
		core.LogError("Payload Read Body : ", result)
		c.Status(200)
		return
	}

	var gifts []*Payload
	err1 := json.Unmarshal(result, &gifts)
	if err1 != nil {
		core.LogError("Payload Gift Err:", string(result), err1.Error())
		c.Status(200)
		return
	}

	if addGiftCallback != nil {
		addGiftCallback(gifts[0].AvatarUrl, gifts)
	} else {
		core.LogError("添加礼物回调函数未注册")
	}
	c.Status(200)
}

// GinPayloadCommentTest ! Gin版本的评论测试处理器
func (payload *ByteDancePayload) GinPayloadCommentTest(c *gin.Context) {
	result, err := httpclient.ReadRequestBody(c.Request.Body)
	if err != nil {
		core.LogError("Payload Read Body : ", result)
		c.Status(200)
		return
	}

	var comments []*Payload
	err1 := json.Unmarshal(result, &comments)
	if err1 != nil {
		core.LogError("Payload Comment Err:", string(result), err1.Error())
		c.Status(200)
		return
	}

	if addCommentCallback != nil {
		addCommentCallback(comments[0].AvatarUrl, result)
	} else {
		core.LogError("添加评论回调函数未注册")
	}
	c.Status(200)
}

// GinPayloadComment ! Gin版本的评论推送接口
func (payload *ByteDancePayload) GinPayloadComment(c *gin.Context) {
	result, err := httpclient.ReadRequestBody(c.Request.Body)
	if err != nil {
		core.LogError("Payload Read Body : ", result)
		c.Status(200)
		return
	}

	once_str := c.GetHeader("x-nonce-str")
	timestamp := c.GetHeader("x-timestamp")
	signature_str := c.GetHeader("x-signature")
	room_id := c.GetHeader("x-roomid")
	msg_type := c.GetHeader("x-msg-type")

	if signature_str != "" {
		check_map := make(map[string]string)
		check_map["x-nonce-str"] = once_str
		check_map["x-timestamp"] = timestamp
		check_map["x-roomid"] = room_id
		check_map["x-msg-type"] = msg_type

		sign_str := DouYinSignature(check_map, string(result), LIVE_MSG_COMMENT_SECRET)
		if sign_str != signature_str {
			core.LogError("PayloadComment Sign Error : ", sign_str, signature_str, room_id, msg_type)
			c.Status(200)
			return
		}
	}
	if addCommentCallback != nil {
		addCommentCallback(room_id, result)
	} else {
		core.LogError("添加评论回调函数未注册")
	}
	c.Status(200)
}

func (payload *ByteDancePayload) GinPayloadFans(c *gin.Context) {
	once_str := c.GetHeader("x-nonce-str")
	timestamp := c.GetHeader("x-timestamp")
	signature_str := c.GetHeader("x-signature")
	room_id := c.GetHeader("x-roomid")
	msg_type := c.GetHeader("x-msg-type")

	result, err := httpclient.ReadRequestBody(c.Request.Body)
	if err != nil {
		core.LogError("Payload Read Body : ", err.Error())
		c.Status(200)
		return
	}

	if signature_str != "" {
		check_map := make(map[string]string)
		check_map["x-nonce-str"] = once_str
		check_map["x-timestamp"] = timestamp
		check_map["x-roomid"] = room_id
		check_map["x-msg-type"] = msg_type

		sign_str := DouYinSignature(check_map, string(result), LIVE_MSG_FANS_SECRET)
		if sign_str != signature_str {
			core.LogError("PayloadGift Sign Error : ", sign_str, signature_str, room_id, msg_type)
			c.Status(200)
			return
		}
	}
	var gifts []*Payload
	err1 := json.Unmarshal(result, &gifts)
	if err1 != nil {
		core.LogError("Payload Gift Err:", string(result), err1.Error())
		c.Status(200)
		return
	}
	if addFansCallback != nil {
		addFansCallback(room_id, gifts)
	} else {
		core.LogError("添加粉丝回调函数未注册")
	}
	c.Status(200)
}

// GinPayloadGift ! Gin版本的礼物推送接口
func (payload *ByteDancePayload) GinPayloadGift(c *gin.Context) {
	once_str := c.GetHeader("x-nonce-str")
	timestamp := c.GetHeader("x-timestamp")
	signature_str := c.GetHeader("x-signature")
	room_id := c.GetHeader("x-roomid")
	msg_type := c.GetHeader("x-msg-type")

	result, err := httpclient.ReadRequestBody(c.Request.Body)
	if err != nil {
		core.LogError("Payload Read Body : ", err.Error())
		c.Status(200)
		return
	}

	if signature_str != "" {
		check_map := make(map[string]string)
		check_map["x-nonce-str"] = once_str
		check_map["x-timestamp"] = timestamp
		check_map["x-roomid"] = room_id
		check_map["x-msg-type"] = msg_type

		sign_str := DouYinSignature(check_map, string(result), LIVE_MSG_GIFT_SECRET)
		if sign_str != signature_str {
			core.LogError("PayloadGift Sign Error : ", sign_str, signature_str, room_id, msg_type)
			c.Status(200)
			return
		}
	}
	var gifts []*Payload
	err1 := json.Unmarshal(result, &gifts)
	if err1 != nil {
		core.LogError("Payload Gift Err:", string(result), err1.Error())
		c.Status(200)
		return
	}
	if addGiftCallback != nil {
		addGiftCallback(room_id, gifts)
	} else {
		core.LogError("添加礼物回调函数未注册")
	}
	c.Status(200)
}

func (payload *ByteDancePayload) GinPayloadFansTest(c *gin.Context) {
	result, err := httpclient.ReadRequestBody(c.Request.Body)
	if err != nil {
		core.LogError("Payload Read Body : ", result)
		c.Status(200)
		return
	}

	var gifts []*Payload
	err1 := json.Unmarshal(result, &gifts)
	if err1 != nil {
		core.LogError("Payload Gift Err:", string(result), err1.Error())
		c.Status(200)
		return
	}

	if addFansCallback != nil {
		addFansCallback(gifts[0].AvatarUrl, gifts)
	} else {
		core.LogError("添加礼物回调函数未注册")
	}
	c.Status(200)
}

// GinPayloadLikeTest ! Gin版本的点赞测试处理器
func (payload *ByteDancePayload) GinPayloadLikeTest(c *gin.Context) {
	result, err := httpclient.ReadRequestBody(c.Request.Body)
	if err != nil {
		core.LogError("Payload Read Body : ", result)
		c.Status(200)
		return
	}

	var likes []*Payload
	err1 := json.Unmarshal(result, &likes)
	if err1 != nil {
		core.LogError("Payload Like Err:", string(result), err1.Error())
		c.Status(200)
		return
	}

	if addLikeCallback != nil {
		addLikeCallback(likes[0].AvatarUrl, result)
	} else {
		core.LogError("添加点赞回调函数未注册")
	}
	c.Status(200)
}

// GinPayloadLike ! Gin版本的点赞推送接口
func (payload *ByteDancePayload) GinPayloadLike(c *gin.Context) {
	once_str := c.GetHeader("x-nonce-str")
	timestamp := c.GetHeader("x-timestamp")
	signature_str := c.GetHeader("x-signature")
	room_id := c.GetHeader("x-roomid")
	msg_type := c.GetHeader("x-msg-type")

	result, err := httpclient.ReadRequestBody(c.Request.Body)
	if err != nil {
		core.LogError("Payload Read Body : ", err.Error())
		c.Status(200)
		return
	}

	if signature_str != "" {
		check_map := make(map[string]string)
		check_map["x-nonce-str"] = once_str
		check_map["x-timestamp"] = timestamp
		check_map["x-roomid"] = room_id
		check_map["x-msg-type"] = msg_type

		sign_str := DouYinSignature(check_map, string(result), LIVE_MSG_LIKE_SECRET)
		if sign_str != signature_str {
			core.LogError("PayloadLike Sign Error : ", sign_str, signature_str, room_id, msg_type)
			c.Status(200)
			return
		}
	}
	if addLikeCallback != nil {
		addLikeCallback(room_id, result)
	} else {
		core.LogError("添加点赞回调函数未注册")
	}
	c.Status(200)
}
