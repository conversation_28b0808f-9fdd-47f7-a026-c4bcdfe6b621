// JoysGames copyrights this specification. No part of this specification may be
// reproduced in any form or means, without the prior written consent of JoysGames.
//
// This specification is preliminary and is subject to change at any time without notice.
// JoysGames assumes no responsibility for any errors contained herein.
//
// The above copyright notice and this permission notice shall be included
// in all copies or substantial portions of the Software.
// @package JGServer
// @copyright joysgames.cn All rights reserved.
// @version v1.0

package core

import (
	"fmt"
	"reflect"
	"sync"
)

// 等待执行
func Await(cb interface{}, args ...interface{}) (retArr []reflect.Value, reterr error) {
	kind := reflect.TypeOf(cb).Kind()
	if kind != reflect.Func {
		LogError("cb must be func")
		return
	}

	handler := reflect.ValueOf(cb)
	funcType := handler.Type()
	options := make([]reflect.Value, len(args))
	for i, v := range args {
		if v == nil {
			options[i] = reflect.New(funcType.In(i)).Elem()
		} else {
			options[i] = reflect.ValueOf(v)
		}
	}

	// 异步执行任务
	wg := &sync.WaitGroup{}
	wg.Add(1)
	go func(handler_ reflect.Value, options_ []reflect.Value) {
		defer func() {
			if err := recover(); err != nil {
				reterr = fmt.Errorf("failed to await %v", err)
				LogError(reterr)
				wg.Done()
			}
		}()

		retArr = handler_.Call(options_)
		if len(retArr) > 0 {
			last := retArr[len(retArr)-1].Interface()
			opterr, ok := last.(error)
			if ok {
				retArr = nil
				reterr = opterr
			}
		}
		wg.Done()
	}(handler, options)
	wg.Wait()
	return retArr, reterr
}
