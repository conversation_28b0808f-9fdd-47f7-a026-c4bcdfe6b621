// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.5.1
// source: systemMsg.proto

package Message

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// class=SystemMsg
// cmd=101 通用错误码结构，可以附带参数
type ErrorCodeC2S struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Cmd           int32                  `protobuf:"varint,1,opt,name=cmd,proto3" json:"cmd,omitempty"`        // 错误的协议号
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"` // 错误信息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ErrorCodeC2S) Reset() {
	*x = ErrorCodeC2S{}
	mi := &file_systemMsg_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ErrorCodeC2S) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ErrorCodeC2S) ProtoMessage() {}

func (x *ErrorCodeC2S) ProtoReflect() protoreflect.Message {
	mi := &file_systemMsg_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ErrorCodeC2S.ProtoReflect.Descriptor instead.
func (*ErrorCodeC2S) Descriptor() ([]byte, []int) {
	return file_systemMsg_proto_rawDescGZIP(), []int{0}
}

func (x *ErrorCodeC2S) GetCmd() int32 {
	if x != nil {
		return x.Cmd
	}
	return 0
}

func (x *ErrorCodeC2S) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type ErrorCodeS2C struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Cmd           int32                  `protobuf:"varint,1,opt,name=cmd,proto3" json:"cmd,omitempty"`        // 错误的协议号
	Code          int32                  `protobuf:"varint,2,opt,name=code,proto3" json:"code,omitempty"`      // 错误码
	Message       string                 `protobuf:"bytes,3,opt,name=message,proto3" json:"message,omitempty"` // 参数列表
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ErrorCodeS2C) Reset() {
	*x = ErrorCodeS2C{}
	mi := &file_systemMsg_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ErrorCodeS2C) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ErrorCodeS2C) ProtoMessage() {}

func (x *ErrorCodeS2C) ProtoReflect() protoreflect.Message {
	mi := &file_systemMsg_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ErrorCodeS2C.ProtoReflect.Descriptor instead.
func (*ErrorCodeS2C) Descriptor() ([]byte, []int) {
	return file_systemMsg_proto_rawDescGZIP(), []int{1}
}

func (x *ErrorCodeS2C) GetCmd() int32 {
	if x != nil {
		return x.Cmd
	}
	return 0
}

func (x *ErrorCodeS2C) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ErrorCodeS2C) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// cmd=102 心跳
type HeartBeatC2S struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HeartBeatC2S) Reset() {
	*x = HeartBeatC2S{}
	mi := &file_systemMsg_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HeartBeatC2S) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HeartBeatC2S) ProtoMessage() {}

func (x *HeartBeatC2S) ProtoReflect() protoreflect.Message {
	mi := &file_systemMsg_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HeartBeatC2S.ProtoReflect.Descriptor instead.
func (*HeartBeatC2S) Descriptor() ([]byte, []int) {
	return file_systemMsg_proto_rawDescGZIP(), []int{2}
}

type HeartBeatS2C struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HeartBeatS2C) Reset() {
	*x = HeartBeatS2C{}
	mi := &file_systemMsg_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HeartBeatS2C) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HeartBeatS2C) ProtoMessage() {}

func (x *HeartBeatS2C) ProtoReflect() protoreflect.Message {
	mi := &file_systemMsg_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HeartBeatS2C.ProtoReflect.Descriptor instead.
func (*HeartBeatS2C) Descriptor() ([]byte, []int) {
	return file_systemMsg_proto_rawDescGZIP(), []int{3}
}

// cmd=103 GM指令
type GmOrderC2S struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Type          string                 `protobuf:"bytes,1,opt,name=type,proto3" json:"type,omitempty"`     // GM指令类型 AddPlayer
	Params        []string               `protobuf:"bytes,2,rep,name=params,proto3" json:"params,omitempty"` // 参数列表
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GmOrderC2S) Reset() {
	*x = GmOrderC2S{}
	mi := &file_systemMsg_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GmOrderC2S) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GmOrderC2S) ProtoMessage() {}

func (x *GmOrderC2S) ProtoReflect() protoreflect.Message {
	mi := &file_systemMsg_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GmOrderC2S.ProtoReflect.Descriptor instead.
func (*GmOrderC2S) Descriptor() ([]byte, []int) {
	return file_systemMsg_proto_rawDescGZIP(), []int{4}
}

func (x *GmOrderC2S) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *GmOrderC2S) GetParams() []string {
	if x != nil {
		return x.Params
	}
	return nil
}

type GmOrderS2C struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`    // GM执行结果
	Type          string                 `protobuf:"bytes,2,opt,name=type,proto3" json:"type,omitempty"`     // GM指令类型
	Params        []string               `protobuf:"bytes,3,rep,name=params,proto3" json:"params,omitempty"` // 参数列表
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GmOrderS2C) Reset() {
	*x = GmOrderS2C{}
	mi := &file_systemMsg_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GmOrderS2C) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GmOrderS2C) ProtoMessage() {}

func (x *GmOrderS2C) ProtoReflect() protoreflect.Message {
	mi := &file_systemMsg_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GmOrderS2C.ProtoReflect.Descriptor instead.
func (*GmOrderS2C) Descriptor() ([]byte, []int) {
	return file_systemMsg_proto_rawDescGZIP(), []int{5}
}

func (x *GmOrderS2C) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GmOrderS2C) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *GmOrderS2C) GetParams() []string {
	if x != nil {
		return x.Params
	}
	return nil
}

// cmd=104 公告
type AnnouncementC2S struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Suc           int32                  `protobuf:"varint,1,opt,name=suc,proto3" json:"suc,omitempty"` // 公告结果
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AnnouncementC2S) Reset() {
	*x = AnnouncementC2S{}
	mi := &file_systemMsg_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AnnouncementC2S) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnnouncementC2S) ProtoMessage() {}

func (x *AnnouncementC2S) ProtoReflect() protoreflect.Message {
	mi := &file_systemMsg_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnnouncementC2S.ProtoReflect.Descriptor instead.
func (*AnnouncementC2S) Descriptor() ([]byte, []int) {
	return file_systemMsg_proto_rawDescGZIP(), []int{6}
}

func (x *AnnouncementC2S) GetSuc() int32 {
	if x != nil {
		return x.Suc
	}
	return 0
}

type AnnouncementS2C struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Announcement  string                 `protobuf:"bytes,1,opt,name=announcement,proto3" json:"announcement,omitempty"` // 公告内容
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AnnouncementS2C) Reset() {
	*x = AnnouncementS2C{}
	mi := &file_systemMsg_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AnnouncementS2C) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnnouncementS2C) ProtoMessage() {}

func (x *AnnouncementS2C) ProtoReflect() protoreflect.Message {
	mi := &file_systemMsg_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnnouncementS2C.ProtoReflect.Descriptor instead.
func (*AnnouncementS2C) Descriptor() ([]byte, []int) {
	return file_systemMsg_proto_rawDescGZIP(), []int{7}
}

func (x *AnnouncementS2C) GetAnnouncement() string {
	if x != nil {
		return x.Announcement
	}
	return ""
}

var File_systemMsg_proto protoreflect.FileDescriptor

const file_systemMsg_proto_rawDesc = "" +
	"\n" +
	"\x0fsystemMsg.proto\x12\n" +
	"PB.Message\":\n" +
	"\fErrorCodeC2S\x12\x10\n" +
	"\x03cmd\x18\x01 \x01(\x05R\x03cmd\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"N\n" +
	"\fErrorCodeS2C\x12\x10\n" +
	"\x03cmd\x18\x01 \x01(\x05R\x03cmd\x12\x12\n" +
	"\x04code\x18\x02 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x03 \x01(\tR\amessage\"\x0e\n" +
	"\fHeartBeatC2S\"\x0e\n" +
	"\fHeartBeatS2C\"8\n" +
	"\n" +
	"GmOrderC2S\x12\x12\n" +
	"\x04type\x18\x01 \x01(\tR\x04type\x12\x16\n" +
	"\x06params\x18\x02 \x03(\tR\x06params\"L\n" +
	"\n" +
	"GmOrderS2C\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x12\n" +
	"\x04type\x18\x02 \x01(\tR\x04type\x12\x16\n" +
	"\x06params\x18\x03 \x03(\tR\x06params\"#\n" +
	"\x0fAnnouncementC2S\x12\x10\n" +
	"\x03suc\x18\x01 \x01(\x05R\x03suc\"5\n" +
	"\x0fAnnouncementS2C\x12\"\n" +
	"\fannouncement\x18\x01 \x01(\tR\fannouncementb\x06proto3"

var (
	file_systemMsg_proto_rawDescOnce sync.Once
	file_systemMsg_proto_rawDescData []byte
)

func file_systemMsg_proto_rawDescGZIP() []byte {
	file_systemMsg_proto_rawDescOnce.Do(func() {
		file_systemMsg_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_systemMsg_proto_rawDesc), len(file_systemMsg_proto_rawDesc)))
	})
	return file_systemMsg_proto_rawDescData
}

var file_systemMsg_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_systemMsg_proto_goTypes = []any{
	(*ErrorCodeC2S)(nil),    // 0: PB.Message.ErrorCodeC2S
	(*ErrorCodeS2C)(nil),    // 1: PB.Message.ErrorCodeS2C
	(*HeartBeatC2S)(nil),    // 2: PB.Message.HeartBeatC2S
	(*HeartBeatS2C)(nil),    // 3: PB.Message.HeartBeatS2C
	(*GmOrderC2S)(nil),      // 4: PB.Message.GmOrderC2S
	(*GmOrderS2C)(nil),      // 5: PB.Message.GmOrderS2C
	(*AnnouncementC2S)(nil), // 6: PB.Message.AnnouncementC2S
	(*AnnouncementS2C)(nil), // 7: PB.Message.AnnouncementS2C
}
var file_systemMsg_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_systemMsg_proto_init() }
func file_systemMsg_proto_init() {
	if File_systemMsg_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_systemMsg_proto_rawDesc), len(file_systemMsg_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_systemMsg_proto_goTypes,
		DependencyIndexes: file_systemMsg_proto_depIdxs,
		MessageInfos:      file_systemMsg_proto_msgTypes,
	}.Build()
	File_systemMsg_proto = out.File
	file_systemMsg_proto_goTypes = nil
	file_systemMsg_proto_depIdxs = nil
}
