// Code generated by pb_exporter.go. DO NOT EDIT.
package Request

import (
	"zone/lib/network"
	"zone/pb/Message"
)

func SyncWinStreakPoolRequest(session *network.Session, winstreakpool int32) {
	syncwinstreakpoolS2C := &Message.SyncWinStreakPoolS2C{
		WinStreakPool: int32(winstreakpool),
	}
	pbMsg := network.NewPBMsg(SyncWinStreakPool, syncwinstreakpoolS2C)
	session.SendPBMsg(pbMsg.MsgToBytes())
}
