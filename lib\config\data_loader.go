package config

import (
	"bufio"
	"encoding/binary"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"reflect"
	"strings"
	"sync"

	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// ConfigTypeInfo 配置类型信息
type ConfigTypeInfo struct {
	MessageName string               // 消息名称
	MessageType reflect.Type         // Go类型
	NewInstance func() proto.Message // 创建新实例的函数
}

// DataLoader 配置数据加载器
type DataLoader struct {
	dataDir   string
	cache     sync.Map                   // 缓存已加载的数据
	typeMap   map[string]*ConfigTypeInfo // 类型映射表
	typeMutex sync.RWMutex               // 类型映射表的读写锁
}

var globalDataLoader *DataLoader
var loaderOnce sync.Once

// GetDataLoader 获取全局数据加载器实例
func GetDataLoader() *DataLoader {
	loaderOnce.Do(func() {
		globalDataLoader = &DataLoader{
			dataDir: "bytes", // 默认数据目录
			typeMap: make(map[string]*ConfigTypeInfo),
		}
		// 初始化类型映射
		globalDataLoader.initTypeMap()
	})
	return globalDataLoader
}

// SetDataDir 设置数据目录
func (dl *DataLoader) SetDataDir(dir string) {
	dl.dataDir = dir
}

// RegisterConfigType 注册配置类型
func (dl *DataLoader) RegisterConfigType(dataType string, info *ConfigTypeInfo) {
	dl.typeMutex.Lock()
	defer dl.typeMutex.Unlock()
	dl.typeMap[strings.ToLower(dataType)] = info
}

// LoadData 加载指定类型的配置数据
func (dl *DataLoader) LoadData(dataType string, result interface{}) error {
	dataType = strings.ToLower(dataType)

	// 检查缓存
	if cached, ok := dl.cache.Load(dataType); ok {
		return dl.copyData(cached, result)
	}

	// 获取类型信息
	dl.typeMutex.RLock()
	typeInfo, exists := dl.typeMap[dataType]
	dl.typeMutex.RUnlock()

	if !exists {
		return fmt.Errorf("未注册的配置类型: %s", dataType)
	}

	// 构建文件路径
	fileName := dataType + ".bytes"
	filePath := filepath.Join(dl.dataDir, fileName)

	// 检查文件是否存在
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		return fmt.Errorf("配置文件不存在: %s", filePath)
	}

	// 读取并解析数据
	data, err := dl.readDataFile(filePath, typeInfo)
	if err != nil {
		return fmt.Errorf("读取配置文件失败: %v", err)
	}

	// 缓存数据
	dl.cache.Store(dataType, data)

	// 复制到结果
	return dl.copyData(data, result)
}

// readDataFile 读取数据文件
func (dl *DataLoader) readDataFile(filePath string, typeInfo *ConfigTypeInfo) (interface{}, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	reader := bufio.NewReader(file)

	// 读取消息名长度
	var nameLen int32
	if err := binary.Read(reader, binary.LittleEndian, &nameLen); err != nil {
		return nil, fmt.Errorf("读取消息名长度失败: %v", err)
	}

	// 读取消息名
	nameBytes := make([]byte, nameLen)
	if _, err := io.ReadFull(reader, nameBytes); err != nil {
		return nil, fmt.Errorf("读取消息名失败: %v", err)
	}
	messageName := string(nameBytes)

	// 验证消息名
	if strings.ToLower(messageName) != strings.ToLower(typeInfo.MessageName) {
		return nil, fmt.Errorf("消息名不匹配: 期望 %s, 实际 %s", typeInfo.MessageName, messageName)
	}

	// 读取记录数
	var recordCount int32
	if err := binary.Read(reader, binary.LittleEndian, &recordCount); err != nil {
		return nil, fmt.Errorf("读取记录数失败: %v", err)
	}

	// 解析记录数据
	return dl.parseRecords(reader, typeInfo, int(recordCount))
}

// parseRecords 解析记录数据
func (dl *DataLoader) parseRecords(reader *bufio.Reader, typeInfo *ConfigTypeInfo, recordCount int) (interface{}, error) {
	// 创建切片类型
	sliceType := reflect.SliceOf(typeInfo.MessageType)
	slice := reflect.MakeSlice(sliceType, 0, recordCount)

	for i := 0; i < recordCount; i++ {
		// 创建新的消息实例
		instance := typeInfo.NewInstance()

		// 解析单条记录
		if err := dl.parseRecord(reader, instance); err != nil {
			return nil, fmt.Errorf("解析第%d条记录失败: %v", i+1, err)
		}

		// 添加到切片
		slice = reflect.Append(slice, reflect.ValueOf(instance))
	}

	return slice.Interface(), nil
}

// parseRecord 解析单条记录
func (dl *DataLoader) parseRecord(reader *bufio.Reader, message proto.Message) error {
	// 读取字段数
	var fieldCount int32
	if err := binary.Read(reader, binary.LittleEndian, &fieldCount); err != nil {
		return fmt.Errorf("读取字段数失败: %v", err)
	}

	// 获取消息的反射描述符
	msgReflect := message.ProtoReflect()
	msgDesc := msgReflect.Descriptor()
	fields := msgDesc.Fields()

	// 读取每个字段
	for i := 0; i < int(fieldCount); i++ {
		if i >= fields.Len() {
			return fmt.Errorf("字段索引超出范围: %d >= %d", i, fields.Len())
		}

		field := fields.Get(i)
		if err := dl.readField(reader, msgReflect, field); err != nil {
			return fmt.Errorf("读取字段 %s 失败: %v", field.Name(), err)
		}
	}

	return nil
}

// readField 读取单个字段
func (dl *DataLoader) readField(reader *bufio.Reader, msgReflect protoreflect.Message, field protoreflect.FieldDescriptor) error {
	if field.IsList() {
		return dl.readRepeatedField(reader, msgReflect, field)
	} else {
		return dl.readSingleField(reader, msgReflect, field)
	}
}

// readSingleField 读取单个值字段
func (dl *DataLoader) readSingleField(reader *bufio.Reader, msgReflect protoreflect.Message, field protoreflect.FieldDescriptor) error {
	switch field.Kind() {
	case protoreflect.Int32Kind:
		var value int32
		if err := binary.Read(reader, binary.LittleEndian, &value); err != nil {
			return err
		}
		msgReflect.Set(field, protoreflect.ValueOfInt32(value))
	case protoreflect.Int64Kind:
		var value int64
		if err := binary.Read(reader, binary.LittleEndian, &value); err != nil {
			return err
		}
		msgReflect.Set(field, protoreflect.ValueOfInt64(value))
	case protoreflect.FloatKind:
		var length int32
		if err := binary.Read(reader, binary.LittleEndian, &length); err != nil {
			return err
		}
		if length > 0 {
			bytes := make([]byte, length)
			if _, err := io.ReadFull(reader, bytes); err != nil {
				return err
			}
			// 简化处理：从字符串解析float
			var value float32
			if _, err := fmt.Sscanf(string(bytes), "%f", &value); err != nil {
				return err
			}
			msgReflect.Set(field, protoreflect.ValueOfFloat32(value))
		}
	case protoreflect.DoubleKind:
		var length int32
		if err := binary.Read(reader, binary.LittleEndian, &length); err != nil {
			return err
		}
		if length > 0 {
			bytes := make([]byte, length)
			if _, err := io.ReadFull(reader, bytes); err != nil {
				return err
			}
			// 简化处理：从字符串解析double
			var value float64
			if _, err := fmt.Sscanf(string(bytes), "%f", &value); err != nil {
				return err
			}
			msgReflect.Set(field, protoreflect.ValueOfFloat64(value))
		}
	case protoreflect.StringKind:
		var length int32
		if err := binary.Read(reader, binary.LittleEndian, &length); err != nil {
			return err
		}
		if length > 0 {
			bytes := make([]byte, length)
			if _, err := io.ReadFull(reader, bytes); err != nil {
				return err
			}
			msgReflect.Set(field, protoreflect.ValueOfString(string(bytes)))
		} else {
			msgReflect.Set(field, protoreflect.ValueOfString(""))
		}
	case protoreflect.BoolKind:
		var value byte
		if err := binary.Read(reader, binary.LittleEndian, &value); err != nil {
			return err
		}
		msgReflect.Set(field, protoreflect.ValueOfBool(value != 0))
	default:
		return fmt.Errorf("不支持的字段类型: %v", field.Kind())
	}
	return nil
}

// readRepeatedField 读取重复字段
func (dl *DataLoader) readRepeatedField(reader *bufio.Reader, msgReflect protoreflect.Message, field protoreflect.FieldDescriptor) error {
	// 读取数组长度
	var length int32
	if err := binary.Read(reader, binary.LittleEndian, &length); err != nil {
		return err
	}

	list := msgReflect.Mutable(field).List()
	for i := 0; i < int(length); i++ {
		// 直接读取单个值并添加到列表
		value, err := dl.readSingleValue(reader, field.Kind())
		if err != nil {
			return err
		}
		list.Append(value)
	}

	return nil
}

// readSingleValue 读取单个值
func (dl *DataLoader) readSingleValue(reader *bufio.Reader, kind protoreflect.Kind) (protoreflect.Value, error) {
	switch kind {
	case protoreflect.Int32Kind:
		var value int32
		if err := binary.Read(reader, binary.LittleEndian, &value); err != nil {
			return protoreflect.Value{}, err
		}
		return protoreflect.ValueOfInt32(value), nil
	case protoreflect.Int64Kind:
		var value int64
		if err := binary.Read(reader, binary.LittleEndian, &value); err != nil {
			return protoreflect.Value{}, err
		}
		return protoreflect.ValueOfInt64(value), nil
	case protoreflect.FloatKind:
		var length int32
		if err := binary.Read(reader, binary.LittleEndian, &length); err != nil {
			return protoreflect.Value{}, err
		}
		if length > 0 {
			bytes := make([]byte, length)
			if _, err := io.ReadFull(reader, bytes); err != nil {
				return protoreflect.Value{}, err
			}
			// 简化处理：从字符串解析float
			var value float32
			if _, err := fmt.Sscanf(string(bytes), "%f", &value); err != nil {
				return protoreflect.Value{}, err
			}
			return protoreflect.ValueOfFloat32(value), nil
		}
		return protoreflect.ValueOfFloat32(0), nil
	case protoreflect.DoubleKind:
		var length int32
		if err := binary.Read(reader, binary.LittleEndian, &length); err != nil {
			return protoreflect.Value{}, err
		}
		if length > 0 {
			bytes := make([]byte, length)
			if _, err := io.ReadFull(reader, bytes); err != nil {
				return protoreflect.Value{}, err
			}
			// 简化处理：从字符串解析double
			var value float64
			if _, err := fmt.Sscanf(string(bytes), "%f", &value); err != nil {
				return protoreflect.Value{}, err
			}
			return protoreflect.ValueOfFloat64(value), nil
		}
		return protoreflect.ValueOfFloat64(0), nil
	case protoreflect.StringKind:
		var length int32
		if err := binary.Read(reader, binary.LittleEndian, &length); err != nil {
			return protoreflect.Value{}, err
		}
		if length > 0 {
			bytes := make([]byte, length)
			if _, err := io.ReadFull(reader, bytes); err != nil {
				return protoreflect.Value{}, err
			}
			return protoreflect.ValueOfString(string(bytes)), nil
		}
		return protoreflect.ValueOfString(""), nil
	case protoreflect.BoolKind:
		var value byte
		if err := binary.Read(reader, binary.LittleEndian, &value); err != nil {
			return protoreflect.Value{}, err
		}
		return protoreflect.ValueOfBool(value != 0), nil
	default:
		return protoreflect.Value{}, fmt.Errorf("不支持的字段类型: %v", kind)
	}
}

// copyData 复制数据到目标结构
func (dl *DataLoader) copyData(source, target interface{}) error {
	sourceVal := reflect.ValueOf(source)
	targetVal := reflect.ValueOf(target)

	if targetVal.Kind() != reflect.Ptr {
		return fmt.Errorf("目标必须是指针类型")
	}

	targetVal = targetVal.Elem()
	if !targetVal.CanSet() {
		return fmt.Errorf("目标不可设置")
	}

	// 如果源和目标类型相同，直接设置
	if sourceVal.Type() == targetVal.Type() {
		targetVal.Set(sourceVal)
		return nil
	}

	// 如果目标是切片类型，尝试设置切片
	if targetVal.Kind() == reflect.Slice && sourceVal.Kind() == reflect.Slice {
		targetVal.Set(sourceVal)
		return nil
	}

	return fmt.Errorf("类型不匹配: %v -> %v", sourceVal.Type(), targetVal.Type())
}

// ClearCache 清除缓存
func (dl *DataLoader) ClearCache() {
	dl.cache.Range(func(key, value interface{}) bool {
		dl.cache.Delete(key)
		return true
	})
}

// ReloadData 重新加载指定类型的数据
func (dl *DataLoader) ReloadData(dataType string) error {
	dl.cache.Delete(strings.ToLower(dataType))
	var dummy interface{}
	return dl.LoadData(dataType, &dummy)
}

// GetCachedDataTypes 获取已缓存的数据类型列表
func (dl *DataLoader) GetCachedDataTypes() []string {
	var types []string
	dl.cache.Range(func(key, value interface{}) bool {
		if keyStr, ok := key.(string); ok {
			types = append(types, keyStr)
		}
		return true
	})
	return types
}

// GetRegisteredTypes 获取已注册的配置类型列表
func (dl *DataLoader) GetRegisteredTypes() []string {
	dl.typeMutex.RLock()
	defer dl.typeMutex.RUnlock()

	var types []string
	for dataType := range dl.typeMap {
		types = append(types, dataType)
	}
	return types
}
