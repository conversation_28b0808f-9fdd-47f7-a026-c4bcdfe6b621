package network

import (
	"fmt"
	"io"
	"log"
	"net"
	"runtime/debug"
	"sync"
	"time"
	"zone/lib/core"

	"github.com/gorilla/websocket"
)

const (
	// DefaultSocketTimeout WebSocket操作默认超时时间（秒）
	DefaultSocketTimeout = 30
	// DefaultSocketRetryCount 默认重试次数
	DefaultSocketRetryCount = 3
	// DefaultReadTimeoutRetryCount 读取超时重试次数
	DefaultReadTimeoutRetryCount = 5
	// SendChannelBufferSize 发送通道缓冲区大小
	SendChannelBufferSize = 1000
	// ReceiveChannelBufferSize 接收通道缓冲区大小
	ReceiveChannelBufferSize = 1000
)

// IsNormalWebSocketClose 判断是否为正常的WebSocket关闭
// 返回true表示正常关闭，false表示异常关闭
func IsNormalWebSocketClose(err error) bool {
	return websocket.IsCloseError(err, websocket.CloseNormalClosure, websocket.CloseGoingAway)
}

// GetWebSocketCloseDescription 获取WebSocket关闭码的描述信息
func GetWebSocketCloseDescription(err error) string {
	if closeErr, ok := err.(*websocket.CloseError); ok {
		switch closeErr.Code {
		case websocket.CloseNormalClosure:
			return "正常关闭(1000)"
		case websocket.CloseGoingAway:
			return "客户端离开(1001)"
		case websocket.CloseProtocolError:
			return "协议错误(1002)"
		case websocket.CloseUnsupportedData:
			return "不支持的数据类型(1003)"
		case websocket.CloseAbnormalClosure:
			return "异常关闭(1006)"
		case websocket.CloseInvalidFramePayloadData:
			return "无效帧数据(1007)"
		case websocket.ClosePolicyViolation:
			return "策略违规(1008)"
		case websocket.CloseMessageTooBig:
			return "消息过大(1009)"
		case websocket.CloseMandatoryExtension:
			return "强制扩展(1010)"
		case websocket.CloseInternalServerErr:
			return "内部服务器错误(1011)"
		default:
			return fmt.Sprintf("未知关闭码(%d)", closeErr.Code)
		}
	}
	return "未知关闭类型"
}

// Session WebSocket会话连接，管理客户端与服务器之间的通信
type Session struct {
	ID        int64           // 会话唯一标识ID
	RoomID    int32           // 所属房间ID
	Ws        *websocket.Conn // WebSocket连接对象（保持兼容性）
	SendChan  chan []byte     // 发送消息通道（保持兼容性）
	RecvChan  chan []byte     // 接收消息通道（保持兼容性）
	ShutDown  bool            // 关闭状态标志（保持兼容性）
	ShutTime  int64           // 关闭时间戳（保持兼容性）
	LogicTime int64           // 逻辑处理时间（保持兼容性）
	MsgTime   []int64         // 消息时间切片（保持兼容性）
	MsgMaxId  int             // 当前最大消息ID（保持兼容性）

	// 新增字段用于连接状态管理
	connMutex        sync.RWMutex                               // 连接状态读写锁
	isConnected      bool                                       // 连接状态标志
	readErrCount     int                                        // 读取错误计数器
	readTimeoutCount int                                        // 读取超时计数器
	lastReadTime     time.Time                                  // 最后读取时间
	IP               string                                     // 客户端IP地址（保持兼容性）
	TryNum           int                                        // 重试次数（保持兼容性）
	Os               string                                     // 客户端操作系统（保持兼容性）
	LoginBuf         []byte                                     // 登录缓存数据（保持兼容性）
	onMessage        func(session *Session, messageData []byte) // 消息处理回调
	onClose          func(session *Session)                     // 关闭处理回调

	// 流量优化相关
	messageBatch *MessageBatch // 消息批处理器
	enableBatch  bool          // 是否启用批处理
}

// SetOnMessage 设置消息处理回调函数
// hookFunc: 消息处理函数，接收会话和消息数据作为参数
func (session *Session) SetOnMessage(hookFunc func(session *Session, messageData []byte)) {
	session.onMessage = hookFunc
}

// SetOnClose 设置连接关闭回调函数
// hookFunc: 关闭处理函数，接收会话作为参数
func (session *Session) SetOnClose(hookFunc func(session *Session)) {
	session.onClose = hookFunc
}

// GetId 获取会话ID
func (session *Session) GetId() int64 {
	return session.ID
}

// OnClose 主动关闭会话连接
func (session *Session) OnClose() {
	if session.onClose != nil {
		session.onClose(session)
	}
}

// IsConnected 检查连接是否有效
func (session *Session) IsConnected() bool {
	session.connMutex.RLock()
	defer session.connMutex.RUnlock()
	return session.isConnected && !session.ShutDown
}

// SetConnected 设置连接状态
func (session *Session) SetConnected(connected bool) {
	session.connMutex.Lock()
	defer session.connMutex.Unlock()
	session.isConnected = connected
	if connected {
		session.readErrCount = 0
		session.readTimeoutCount = 0
		session.lastReadTime = time.Now()
	}
}

// IncrementReadError 增加读取错误计数
func (session *Session) IncrementReadError() bool {
	session.connMutex.Lock()
	defer session.connMutex.Unlock()
	session.readErrCount++

	// 降低错误阈值，防止触发gorilla/websocket的panic
	// gorilla/websocket在1000次失败读取后会panic，我们在更早的时候就停止
	if session.readErrCount >= 5 {
		session.isConnected = false
		core.LogError("连接读取错误次数过多，标记为断开连接，会话ID:", session.ID, "错误次数:", session.readErrCount)
		return true // 表示应该停止读取
	}
	return false
}

// UpdateLastReadTime 更新最后读取时间
func (session *Session) UpdateLastReadTime() {
	session.connMutex.Lock()
	defer session.connMutex.Unlock()
	session.lastReadTime = time.Now()
	session.readErrCount = 0     // 成功读取时重置错误计数
	session.readTimeoutCount = 0 // 成功读取时重置超时计数
}

// IncrementReadTimeout 增加读取超时计数
func (session *Session) IncrementReadTimeout() bool {
	session.connMutex.Lock()
	defer session.connMutex.Unlock()
	session.readTimeoutCount++

	// 检查超时次数是否超过阈值
	if session.readTimeoutCount >= DefaultReadTimeoutRetryCount {
		session.isConnected = false
		core.LogError("读取超时次数过多，标记为断开连接，会话ID:", session.ID, "超时次数:", session.readTimeoutCount)
		return true // 表示应该停止读取
	}

	core.LogDebug("读取超时计数增加，会话ID:", session.ID, "当前超时次数:", session.readTimeoutCount)
	return false
}

// IsWebSocketConnectionValid 检查WebSocket连接是否有效
// 这个方法会尝试发送ping来检测连接状态
func (session *Session) IsWebSocketConnectionValid() bool {
	if session.Ws == nil {
		return false
	}

	// 检查内部连接状态
	if !session.IsConnected() {
		return false
	}

	// 尝试设置一个很短的写超时来快速检测连接状态
	session.Ws.SetWriteDeadline(time.Now().Add(time.Millisecond * 100))
	err := session.Ws.WriteMessage(websocket.PingMessage, []byte{})

	// 重置写超时
	session.Ws.SetWriteDeadline(time.Time{})

	if err != nil {
		core.LogDebug("WebSocket连接无效，ping失败，会话ID:", session.ID, "错误:", err)
		// 标记连接为断开状态
		session.SetConnected(false)
		return false
	}

	return true
}

// GetConnectionStats 获取连接统计信息
func (session *Session) GetConnectionStats() (bool, int, int, time.Time) {
	session.connMutex.RLock()
	defer session.connMutex.RUnlock()
	return session.isConnected, session.readErrCount, session.readTimeoutCount, session.lastReadTime
}

// IsConnectionHealthy 检查连接是否健康
func (session *Session) IsConnectionHealthy() bool {
	session.connMutex.RLock()
	defer session.connMutex.RUnlock()

	// 检查基本连接状态
	if !session.isConnected || session.ShutDown {
		return false
	}

	// 检查WebSocket连接是否为nil
	if session.Ws == nil {
		return false
	}

	// 检查是否长时间没有读取数据（可能表示连接异常）
	timeSinceLastRead := time.Since(session.lastReadTime)
	if timeSinceLastRead > time.Minute*5 { // 5分钟没有数据认为可能有问题
		core.LogDebug("连接可能不健康，长时间无数据，会话ID:", session.ID, "距离上次读取:", timeSinceLastRead)
		return false
	}

	// 检查错误计数 - 降低阈值以更早发现问题
	if session.readErrCount > 3 {
		core.LogDebug("连接可能不健康，错误次数过多，会话ID:", session.ID, "错误次数:", session.readErrCount)
		return false
	}

	// 检查超时计数
	if session.readTimeoutCount > 3 {
		core.LogDebug("连接可能不健康，超时次数过多，会话ID:", session.ID, "超时次数:", session.readTimeoutCount)
		return false
	}

	return true
}

// ForceCloseConnection 强制关闭连接
// 用于在检测到连接异常时立即关闭连接，防止继续在失败的连接上读取
func (session *Session) ForceCloseConnection(reason string) {
	session.connMutex.Lock()
	defer session.connMutex.Unlock()

	core.LogInfo("强制关闭连接，会话ID:", session.ID, "原因:", reason)

	// 标记连接为断开状态
	session.isConnected = false
	session.ShutDown = true
	session.ShutTime = time.Now().Unix()

	// 关闭WebSocket连接
	if session.Ws != nil {
		session.Ws.Close()
	}
}

// PrepareForReconnection 为重连做准备
// 清理当前连接状态，但保留会话信息以便重连
func (session *Session) PrepareForReconnection() {
	session.connMutex.Lock()
	defer session.connMutex.Unlock()

	core.LogInfo("准备重连，会话ID:", session.ID, "房间ID:", session.RoomID)

	// 重置连接相关状态
	session.isConnected = false
	session.readErrCount = 0
	session.readTimeoutCount = 0
	session.lastReadTime = time.Now()

	// 关闭当前WebSocket连接
	if session.Ws != nil {
		session.Ws.Close()
		session.Ws = nil
	}
}

// UpdateConnectionForReconnect 更新连接用于重连
// 用于重连时更新WebSocket连接对象
func (session *Session) UpdateConnectionForReconnect(newWs *websocket.Conn) {
	session.connMutex.Lock()
	defer session.connMutex.Unlock()

	core.LogInfo("更新重连连接，会话ID:", session.ID, "房间ID:", session.RoomID)

	// 更新WebSocket连接
	session.Ws = newWs
	session.isConnected = true
	session.readErrCount = 0
	session.readTimeoutCount = 0
	session.lastReadTime = time.Now()
	session.ShutDown = false
}

// LogConnectionStatus 记录连接状态信息
func (session *Session) LogConnectionStatus(context string) {
	connected, errCount, timeoutCount, lastRead := session.GetConnectionStats()
	core.LogDebug("连接状态检查",
		"上下文:", context,
		"会话ID:", session.ID,
		"房间ID:", session.RoomID,
		"连接状态:", connected,
		"关闭标志:", session.ShutDown,
		"错误次数:", errCount,
		"超时次数:", timeoutCount,
		"最后读取时间:", lastRead.Format("15:04:05"),
		"距离上次读取:", time.Since(lastRead))
}

// EnableMessageBatch 启用消息批处理
func (session *Session) EnableMessageBatch() {
	if session.messageBatch == nil {
		session.messageBatch = NewMessageBatch(session)
	}
	session.enableBatch = true
	session.messageBatch.Start()
	core.LogDebug("消息批处理已启用，会话ID:", session.ID)
}

// DisableMessageBatch 禁用消息批处理
func (session *Session) DisableMessageBatch() {
	session.enableBatch = false
	if session.messageBatch != nil {
		session.messageBatch.Stop()
	}
	core.LogDebug("消息批处理已禁用，会话ID:", session.ID)
}

// SetBatchConfig 设置批处理配置
func (session *Session) SetBatchConfig(batchSize int, flushTime time.Duration) {
	if session.messageBatch != nil {
		session.messageBatch.SetBatchSize(batchSize)
		session.messageBatch.SetFlushTime(flushTime)
	}
}

// SendPBMsg 发送Protocol Buffer消息（支持批处理优化）
// messageData: 要发送的消息字节数据
func (session *Session) SendPBMsg(messageData []byte) {
	// 检查消息数据是否为空
	if len(messageData) == 0 {
		return
	}

	// 检查会话是否已关闭或发送通道是否为空
	if session.ShutDown || session.SendChan == nil {
		return
	}

	// 如果启用了批处理，使用批处理发送
	if session.enableBatch && session.messageBatch != nil {
		session.messageBatch.AddMessage(messageData)
		return
	}

	// 检查发送通道是否接近满载，预留100个位置的缓冲
	if len(session.SendChan) >= sendChanSize-100 {
		core.LogError("发送通道溢出，强制关闭会话，会话ID:", session.ID)
		// 发送空消息作为关闭信号
		session.SendChan <- []byte("")
		session.ShutDown = true
		session.ShutTime = time.Now().Unix()
		return
	}

	// 将消息放入发送通道
	session.SendChan <- messageData
}

// SendMsgBatch 批量发送消息（用于广播）
// messageData: 要发送的消息字节数据
func (session *Session) SendMsgBatch(messageData []byte) {
	// 检查会话状态
	if session.ShutDown {
		return
	}

	// 检查发送通道是否可用
	if session.SendChan == nil {
		core.LogError("发送通道为空，无法发送消息，会话ID:", session.ID)
		return
	}

	// 发送消息到通道
	session.SendChan <- messageData
}

// CloseChan 关闭会话的所有通道
func (session *Session) CloseChan() {
	// 关闭消息批处理器
	if session.messageBatch != nil {
		session.messageBatch.Stop()
		session.messageBatch = nil
		core.LogDebug("消息批处理器已关闭，会话ID:", session.ID)
	}

	// 安全关闭发送通道
	if session.SendChan != nil {
		close(session.SendChan)
		session.SendChan = nil
		core.LogDebug("发送通道已关闭，会话ID:", session.ID)
	}

	// 安全关闭接收通道
	if session.RecvChan != nil {
		func() {
			defer func() {
				if panicInfo := recover(); panicInfo != nil {
					core.LogDebug("接收通道已关闭，忽略panic，会话ID:", session.ID)
				}
			}()
			close(session.RecvChan)
		}()
		session.RecvChan = nil
		core.LogDebug("接收通道已关闭，会话ID:", session.ID)
	}
}

// Run 启动会话的消息处理循环
// 启动三个goroutine分别处理发送、逻辑和接收
func (session *Session) Run() {
	core.LogDebug("启动会话消息处理循环，会话ID:", session.ID)

	// 启动发送消息处理goroutine
	go session.sendMsgRun()

	// 启动逻辑处理goroutine
	go session.logicRun()

	// 在当前goroutine中运行接收消息处理（阻塞）
	session.receiveMsgRun()
}

// logicRun 逻辑处理循环
// 处理从接收通道来的消息，并调用消息处理回调
func (session *Session) logicRun() {
	defer func() {
		// 异常恢复处理
		if panicInfo := recover(); panicInfo != nil {
			stackTrace := string(debug.Stack())
			log.Printf("会话逻辑处理发生异常，会话ID: %d, 异常: %v\n堆栈:\n%s",
				session.ID, panicInfo, stackTrace)
			core.LogError("会话逻辑处理异常，会话ID:", session.ID, "异常:", panicInfo, "堆栈:", stackTrace)
		}

		// 确保在任何情况下都会调用关闭回调和清理资源
		if session.onClose != nil {
			session.onClose(session)
		}
		GetSessionMgr().RemoveSession(session)
		core.LogDebug("会话逻辑处理循环已结束，会话ID:", session.ID)
	}()

	core.LogDebug("会话逻辑处理循环已启动，会话ID:", session.ID)

	// 消息处理主循环
	for {
		// 检查系统是否正在关闭
		if core.GetZoneApp().IsClosed() {
			core.LogDebug("检测到系统关闭信号，退出逻辑处理循环，会话ID:", session.ID)
			break
		}

		// 检查会话是否已关闭
		if session.ShutDown {
			core.LogDebug("会话已关闭，退出逻辑处理循环，会话ID:", session.ID)
			break
		}

		// 使用select监听接收通道
		select {
		case messagePacket, channelOpen := <-session.RecvChan:
			if !channelOpen {
				// 通道已关闭，说明接收循环已结束
				core.LogInfo("接收通道已关闭，结束逻辑处理，会话ID:", session.ID, "房间ID:", session.RoomID)
				return
			}

			// 处理接收到的消息
			if session.onMessage != nil {
				session.onMessage(session, messagePacket)
			}
		}
	}
}

// ! 发送消息循环
func (self *Session) sendMsgRun() {
	defer func() {
		x := recover()
		if x != nil {
			log.Println(x, string(debug.Stack()))
			core.LogError(x, string(debug.Stack()))
		}

		// 确保发送循环结束时设置关闭状态
		self.ShutDown = true
		self.ShutTime = time.Now().Unix()
		self.Ws.Close()
	}()

	for msg := range self.SendChan {
		if GetSessionMgr().Shutdown { //! 关服
			break
		}

		if string(msg) == "" || self.ShutDown {
			break
		}

		exit := false
		for {
			self.Ws.SetWriteDeadline(time.Now().Add(socketTimeOut * time.Second))
			err := self.Ws.WriteMessage(websocket.BinaryMessage, msg)
			if err != nil {
				neterr, ok := err.(net.Error)
				if ok && neterr.Timeout() {
					self.TryNum++
					if self.TryNum >= socketTryNum {
						exit = true
						break
					}
					continue
				}
				// 发送错误时，标记需要退出
				core.LogInfo("send message error:", err, "roomID:", self.RoomID)
				exit = true
				break
			} else {
				break
			}
		}
		if exit {
			break
		}
	}
}

// ! 接收消息循环
func (self *Session) receiveMsgRun() {
	defer func() {
		x := recover()
		if x != nil {
			log.Println(x, string(debug.Stack()))
			core.LogError(x, string(debug.Stack()))
		}

		// 确保在接收循环结束时正确关闭连接
		self.SetConnected(false)
		self.ShutDown = true
		self.ShutTime = time.Now().Unix()

		// 安全关闭WebSocket连接
		if self.Ws != nil {
			self.Ws.Close()
		}

		// 安全关闭接收通道，让logicRun能够正常退出
		// 使用recover来防止重复关闭通道的panic
		func() {
			defer func() {
				if r := recover(); r != nil {
					// 通道已经关闭，忽略panic
					core.LogInfo("RecvChan already closed", self.RoomID)
				}
			}()
			close(self.RecvChan)
		}()

		core.LogDebug("接收消息循环已结束，会话ID:", self.ID, "房间ID:", self.RoomID)
	}()

	// 初始化连接状态
	self.SetConnected(true)
	core.LogDebug("开始接收消息循环，会话ID:", self.ID, "房间ID:", self.RoomID)

	for {
		// 检查应用是否关闭
		if core.GetZoneApp().IsClosed() {
			core.LogDebug("应用关闭，退出接收循环，会话ID:", self.ID)
			break
		}

		// 检查会话是否已关闭
		if self.ShutDown {
			core.LogDebug("会话已关闭，退出接收循环，会话ID:", self.ID)
			break
		}

		// 检查连接状态 - 使用更严格的检查
		if !self.IsConnected() {
			core.LogDebug("连接已断开，退出接收循环，会话ID:", self.ID)
			break
		}

		// 改进的连接健康检查
		// 当有错误或超时时进行更频繁的检查
		if (self.readErrCount > 0 && self.readErrCount%2 == 0) ||
			(self.readTimeoutCount > 0 && self.readTimeoutCount%3 == 0) {
			if !self.IsConnectionHealthy() {
				core.LogInfo("连接健康检查失败，退出接收循环，会话ID:", self.ID)
				break
			}

			// 额外的WebSocket连接有效性检查
			if !self.IsWebSocketConnectionValid() {
				core.LogInfo("WebSocket连接无效，退出接收循环，会话ID:", self.ID)
				break
			}
		}

		// 设置读取超时
		self.Ws.SetReadDeadline(time.Now().Add(socketTimeOut * time.Second))

		// 尝试读取消息
		_, msg, err := self.Ws.ReadMessage()
		if err != nil {
			// 处理网络错误
			if neterr, ok := err.(net.Error); ok && neterr.Timeout() {
				// 超时错误，增加超时计数并检查是否应该继续
				shouldStop := self.IncrementReadTimeout()
				if shouldStop {
					core.LogError("读取超时次数过多，退出接收循环，会话ID:", self.ID)
					break
				}

				// 检查连接状态
				if self.IsConnected() && !self.ShutDown {
					core.LogDebug("读取超时，继续尝试，会话ID:", self.ID, "超时次数:", self.readTimeoutCount)
					continue
				} else {
					core.LogDebug("读取超时且连接状态异常，退出循环，会话ID:", self.ID)
					break
				}
			}

			// 其他错误，进行分类处理
			if err == io.EOF {
				core.LogInfo("客户端断开连接(EOF)，会话ID:", self.ID, "房间ID:", self.RoomID)
				break // EOF错误直接退出，不再尝试
			}

			// 检查是否是WebSocket关闭错误
			if IsNormalWebSocketClose(err) {
				// 正常关闭（1000）或客户端离开（1001），记录为信息级别
				closeDesc := GetWebSocketCloseDescription(err)
				core.LogInfo("WebSocket连接正常关闭，退出接收循环，会话ID:", self.ID, "关闭类型:", closeDesc)
				break
			} else if websocket.IsCloseError(err, websocket.CloseAbnormalClosure, websocket.CloseProtocolError,
				websocket.CloseUnsupportedData, websocket.ClosePolicyViolation, websocket.CloseMessageTooBig,
				websocket.CloseMandatoryExtension, websocket.CloseInternalServerErr) {
				// 异常关闭或协议错误，记录为错误级别
				closeDesc := GetWebSocketCloseDescription(err)
				core.LogError("WebSocket连接异常关闭，退出接收循环，会话ID:", self.ID, "关闭类型:", closeDesc)
				break
			}

			// 其他类型的错误，增加错误计数
			shouldStop := self.IncrementReadError()
			core.LogDebug("读取消息发生错误，会话ID:", self.ID, "错误类型:", err.Error(), "错误次数:", self.readErrCount)

			// 如果错误次数过多，退出循环
			if shouldStop {
				core.LogError("读取错误次数过多，强制退出接收循环，会话ID:", self.ID, "最后错误:", err.Error())
				break
			}

			// 其他错误继续尝试，但要限制重试次数
			continue
		}

		// 成功读取消息，更新状态
		self.UpdateLastReadTime()

		// 将消息放入接收通道
		if len(self.RecvChan) < recvChanSize {
			self.RecvChan <- msg
		} else {
			core.LogError("接收通道溢出，丢弃消息，会话ID:", self.ID, "消息长度:", len(msg))
		}
	}
}
