// Generated from Excel file: TreasureConfig.xlsx

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.5.1
// source: treasureconfig.proto

package Data

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Treasureconfig 配置数据
type Treasureconfig struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 编号
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 积分
	Score         int32 `protobuf:"varint,2,opt,name=score,proto3" json:"score,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Treasureconfig) Reset() {
	*x = Treasureconfig{}
	mi := &file_treasureconfig_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Treasureconfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Treasureconfig) ProtoMessage() {}

func (x *Treasureconfig) ProtoReflect() protoreflect.Message {
	mi := &file_treasureconfig_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Treasureconfig.ProtoReflect.Descriptor instead.
func (*Treasureconfig) Descriptor() ([]byte, []int) {
	return file_treasureconfig_proto_rawDescGZIP(), []int{0}
}

func (x *Treasureconfig) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Treasureconfig) GetScore() int32 {
	if x != nil {
		return x.Score
	}
	return 0
}

// TreasureconfigList 配置数据列表
type TreasureconfigList struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Items         []*Treasureconfig      `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TreasureconfigList) Reset() {
	*x = TreasureconfigList{}
	mi := &file_treasureconfig_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TreasureconfigList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TreasureconfigList) ProtoMessage() {}

func (x *TreasureconfigList) ProtoReflect() protoreflect.Message {
	mi := &file_treasureconfig_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TreasureconfigList.ProtoReflect.Descriptor instead.
func (*TreasureconfigList) Descriptor() ([]byte, []int) {
	return file_treasureconfig_proto_rawDescGZIP(), []int{1}
}

func (x *TreasureconfigList) GetItems() []*Treasureconfig {
	if x != nil {
		return x.Items
	}
	return nil
}

var File_treasureconfig_proto protoreflect.FileDescriptor

const file_treasureconfig_proto_rawDesc = "" +
	"\n" +
	"\x14treasureconfig.proto\x12\x04Data\"6\n" +
	"\x0eTreasureconfig\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x05R\x02id\x12\x14\n" +
	"\x05score\x18\x02 \x01(\x05R\x05score\"@\n" +
	"\x12TreasureconfigList\x12*\n" +
	"\x05items\x18\x01 \x03(\v2\x14.Data.TreasureconfigR\x05itemsB\x0eZ\fzone/pb/Datab\x06proto3"

var (
	file_treasureconfig_proto_rawDescOnce sync.Once
	file_treasureconfig_proto_rawDescData []byte
)

func file_treasureconfig_proto_rawDescGZIP() []byte {
	file_treasureconfig_proto_rawDescOnce.Do(func() {
		file_treasureconfig_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_treasureconfig_proto_rawDesc), len(file_treasureconfig_proto_rawDesc)))
	})
	return file_treasureconfig_proto_rawDescData
}

var file_treasureconfig_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_treasureconfig_proto_goTypes = []any{
	(*Treasureconfig)(nil),     // 0: Data.Treasureconfig
	(*TreasureconfigList)(nil), // 1: Data.TreasureconfigList
}
var file_treasureconfig_proto_depIdxs = []int32{
	0, // 0: Data.TreasureconfigList.items:type_name -> Data.Treasureconfig
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_treasureconfig_proto_init() }
func file_treasureconfig_proto_init() {
	if File_treasureconfig_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_treasureconfig_proto_rawDesc), len(file_treasureconfig_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_treasureconfig_proto_goTypes,
		DependencyIndexes: file_treasureconfig_proto_depIdxs,
		MessageInfos:      file_treasureconfig_proto_msgTypes,
	}.Build()
	File_treasureconfig_proto = out.File
	file_treasureconfig_proto_goTypes = nil
	file_treasureconfig_proto_depIdxs = nil
}
