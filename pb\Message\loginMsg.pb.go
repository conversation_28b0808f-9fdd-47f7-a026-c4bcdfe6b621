// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.5.1
// source: loginMsg.proto

package Message

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// class=LoginMsg
// cmd=1001 登陆验证
type LoginAuthC2S struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RoomID        string                 `protobuf:"bytes,1,opt,name=roomID,proto3" json:"roomID,omitempty"` // 直播间ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LoginAuthC2S) Reset() {
	*x = LoginAuthC2S{}
	mi := &file_loginMsg_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LoginAuthC2S) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoginAuthC2S) ProtoMessage() {}

func (x *LoginAuthC2S) ProtoReflect() protoreflect.Message {
	mi := &file_loginMsg_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoginAuthC2S.ProtoReflect.Descriptor instead.
func (*LoginAuthC2S) Descriptor() ([]byte, []int) {
	return file_loginMsg_proto_rawDescGZIP(), []int{0}
}

func (x *LoginAuthC2S) GetRoomID() string {
	if x != nil {
		return x.RoomID
	}
	return ""
}

type LoginAuthS2C struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Anchor        *AnchorDTO             `protobuf:"bytes,1,opt,name=anchor,proto3" json:"anchor,omitempty"` // 玩家简要数据
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LoginAuthS2C) Reset() {
	*x = LoginAuthS2C{}
	mi := &file_loginMsg_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LoginAuthS2C) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoginAuthS2C) ProtoMessage() {}

func (x *LoginAuthS2C) ProtoReflect() protoreflect.Message {
	mi := &file_loginMsg_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoginAuthS2C.ProtoReflect.Descriptor instead.
func (*LoginAuthS2C) Descriptor() ([]byte, []int) {
	return file_loginMsg_proto_rawDescGZIP(), []int{1}
}

func (x *LoginAuthS2C) GetAnchor() *AnchorDTO {
	if x != nil {
		return x.Anchor
	}
	return nil
}

// cmd=1002 踢出游戏
type LoginKickedC2S struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LoginKickedC2S) Reset() {
	*x = LoginKickedC2S{}
	mi := &file_loginMsg_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LoginKickedC2S) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoginKickedC2S) ProtoMessage() {}

func (x *LoginKickedC2S) ProtoReflect() protoreflect.Message {
	mi := &file_loginMsg_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoginKickedC2S.ProtoReflect.Descriptor instead.
func (*LoginKickedC2S) Descriptor() ([]byte, []int) {
	return file_loginMsg_proto_rawDescGZIP(), []int{2}
}

type LoginKickedS2C struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` // 踢下线原因(错误码定义)
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LoginKickedS2C) Reset() {
	*x = LoginKickedS2C{}
	mi := &file_loginMsg_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LoginKickedS2C) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoginKickedS2C) ProtoMessage() {}

func (x *LoginKickedS2C) ProtoReflect() protoreflect.Message {
	mi := &file_loginMsg_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoginKickedS2C.ProtoReflect.Descriptor instead.
func (*LoginKickedS2C) Descriptor() ([]byte, []int) {
	return file_loginMsg_proto_rawDescGZIP(), []int{3}
}

func (x *LoginKickedS2C) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

// cmd=1003 登出
type LoginOutC2S struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LoginOutC2S) Reset() {
	*x = LoginOutC2S{}
	mi := &file_loginMsg_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LoginOutC2S) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoginOutC2S) ProtoMessage() {}

func (x *LoginOutC2S) ProtoReflect() protoreflect.Message {
	mi := &file_loginMsg_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoginOutC2S.ProtoReflect.Descriptor instead.
func (*LoginOutC2S) Descriptor() ([]byte, []int) {
	return file_loginMsg_proto_rawDescGZIP(), []int{4}
}

type LoginOutS2C struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LoginOutS2C) Reset() {
	*x = LoginOutS2C{}
	mi := &file_loginMsg_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LoginOutS2C) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoginOutS2C) ProtoMessage() {}

func (x *LoginOutS2C) ProtoReflect() protoreflect.Message {
	mi := &file_loginMsg_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoginOutS2C.ProtoReflect.Descriptor instead.
func (*LoginOutS2C) Descriptor() ([]byte, []int) {
	return file_loginMsg_proto_rawDescGZIP(), []int{5}
}

var File_loginMsg_proto protoreflect.FileDescriptor

const file_loginMsg_proto_rawDesc = "" +
	"\n" +
	"\x0eloginMsg.proto\x12\n" +
	"PB.Message\x1a\fcommon.proto\"&\n" +
	"\fLoginAuthC2S\x12\x16\n" +
	"\x06roomID\x18\x01 \x01(\tR\x06roomID\"=\n" +
	"\fLoginAuthS2C\x12-\n" +
	"\x06anchor\x18\x01 \x01(\v2\x15.PB.Message.AnchorDTOR\x06anchor\"\x10\n" +
	"\x0eLoginKickedC2S\"$\n" +
	"\x0eLoginKickedS2C\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\"\r\n" +
	"\vLoginOutC2S\"\r\n" +
	"\vLoginOutS2Cb\x06proto3"

var (
	file_loginMsg_proto_rawDescOnce sync.Once
	file_loginMsg_proto_rawDescData []byte
)

func file_loginMsg_proto_rawDescGZIP() []byte {
	file_loginMsg_proto_rawDescOnce.Do(func() {
		file_loginMsg_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_loginMsg_proto_rawDesc), len(file_loginMsg_proto_rawDesc)))
	})
	return file_loginMsg_proto_rawDescData
}

var file_loginMsg_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_loginMsg_proto_goTypes = []any{
	(*LoginAuthC2S)(nil),   // 0: PB.Message.LoginAuthC2S
	(*LoginAuthS2C)(nil),   // 1: PB.Message.LoginAuthS2C
	(*LoginKickedC2S)(nil), // 2: PB.Message.LoginKickedC2S
	(*LoginKickedS2C)(nil), // 3: PB.Message.LoginKickedS2C
	(*LoginOutC2S)(nil),    // 4: PB.Message.LoginOutC2S
	(*LoginOutS2C)(nil),    // 5: PB.Message.LoginOutS2C
	(*AnchorDTO)(nil),      // 6: PB.Message.AnchorDTO
}
var file_loginMsg_proto_depIdxs = []int32{
	6, // 0: PB.Message.LoginAuthS2C.anchor:type_name -> PB.Message.AnchorDTO
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_loginMsg_proto_init() }
func file_loginMsg_proto_init() {
	if File_loginMsg_proto != nil {
		return
	}
	file_common_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_loginMsg_proto_rawDesc), len(file_loginMsg_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_loginMsg_proto_goTypes,
		DependencyIndexes: file_loginMsg_proto_depIdxs,
		MessageInfos:      file_loginMsg_proto_msgTypes,
	}.Build()
	File_loginMsg_proto = out.File
	file_loginMsg_proto_goTypes = nil
	file_loginMsg_proto_depIdxs = nil
}
