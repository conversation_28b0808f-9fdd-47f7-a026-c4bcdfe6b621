package core

var (
	IsDebug = false
)

// ! 配置
type Config struct {
	ServerId      int              `json:"serverid"`   //! 服
	ServerName    string           `json:"servername"` //! 服务器名称
	GameName      string           `json:"gamename"`   //! 游戏名字
	Lang          int              `json:"lang"`       //! 语言
	Host          string           `json:"wshost"`     //! 服务器
	OpenTime      string           `json:"opentime"`   //! 开服时间
	ServerVer     int              `json:"serverver"`  //! 服务器版本
	AdminCode     string           `json:"admincode"`  //! 后台token
	GM            int64            `json:"gm"`         //! gmID
	IsTestServer  bool             `json:"testserver"` //! 测试服务器
	Platform      int              `json:"platform"`   //! 运行平台 0:抖音 1:快手 2:
	UseEncrypt    bool             `json:"useEncrypt"` //! 是否加密
	DBConf        *DatabaseConfig  `json:"database"`   //! 数据库
	ExMsgConfig   *ExMsgConfig     `json:"exmsg"`
	LogConf       *LoggerConfig    `json:"log"`
	ServerExtCon  *ServerExtConfig `json:"serverext"`
	RateLimitConf *RateLimitConfig `json:"ratelimit"`
}

// ! 配置
type ConfigToml struct {
	ServerId   int    `toml:"serverid"`   //! 服
	ServerName string `toml:"servername"` //! 服务器名称
	Host       string `toml:"wshost"`     //! 服务器
	OpenTime   string `toml:"opentime"`   //! 开服时间
	ServerVer  int    `toml:"serverver"`  //! 服务器版本

	DBConf  *DatabaseConfig `toml:"database"` //! 数据库
	LogConf *LoggerConfig   `toml:"log"`
}

type ServerExtConfig struct {
	MasterSvr       string `json:"mastersvr"`
	MasterWarnClose int    `json:"masterwarnclose"`
	MaxPlayerNum    int    `json:"maxplayer"`
}

type LoggerConfig struct {
	MaxFileSize int64 `json:"maxfilesize"` //! 文件长度
	MaxFileNum  int   `json:"maxfilenum"`  //! 文件数量
	LogLevel    int   `json:"loglevel"`    //! 日志等级
	LogConsole  bool  `json:"logconsole"`  //! 是否输出控制台
}

type DatabaseConfig struct {
	DBUser    string `json:"dbuser"`    //! 游戏数据库
	DBLog     string `json:"dblog"`     //! 日志数据库
	DBId      int    `json:"dbid"`      //! dbid
	MaxDBConn int    `json:"maxdbconn"` //! 最大数据库连接数
	Redis     string `json:"redis"`     //! redis地址
	RedisDB   int    `json:"redisdb"`   //! redis db编号
	RedisAuth string `json:"redisauth"` //! redis认证
}

type ExMsgConfig struct {
	Comment int `json:"comment"` //! 每200毫秒处理弹幕消息数
	Gift    int `json:"gift"`    //! 每200毫秒处理弹幕消息数
	Like    int `json:"like"`    //! 每200毫秒处理弹幕消息数
}

// RateLimitConfig 频率限制配置
type RateLimitConfig struct {
	Enabled            bool     `json:"enabled"`               // 是否启用频率限制
	HTTPRequestsPerSec int64    `json:"http_requests_per_sec"` // HTTP每秒允许的请求数
	HTTPBurstCapacity  int64    `json:"http_burst_capacity"`   // HTTP突发容量
	WSRequestsPerSec   int64    `json:"ws_requests_per_sec"`   // WebSocket每秒允许的连接数
	WSBurstCapacity    int64    `json:"ws_burst_capacity"`     // WebSocket突发容量
	CleanupIntervalSec int      `json:"cleanup_interval_sec"`  // 清理间隔（秒）
	IPWhitelist        []string `json:"ip_whitelist"`          // IP白名单
	IPBlacklist        []string `json:"ip_blacklist"`          // IP黑名单
}
