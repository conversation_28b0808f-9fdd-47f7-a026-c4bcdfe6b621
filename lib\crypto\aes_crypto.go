package crypto

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"errors"
	"io"
)

var EnableEncrypt bool = false

// AES 加密配置
const (
	// 固定密钥 - 16字节用于AES-128
	AESKey = "Jfy4SyCbWASQM6H7"
	// CBC IV 长度 - 必须等于 AES 块大小
	IVSize = 16
	// AES 块大小
	BlockSize = 16
)

// PKCS7Padding 添加 PKCS7 填充
func PKCS7Padding(data []byte, blockSize int) []byte {
	padding := blockSize - len(data)%blockSize
	padtext := make([]byte, padding)
	for i := range padtext {
		padtext[i] = byte(padding)
	}
	return append(data, padtext...)
}

// PKCS7UnPadding 移除 PKCS7 填充
func PKCS7UnPadding(data []byte) ([]byte, error) {
	if len(data) == 0 {
		return nil, errors.New("data is empty")
	}

	padding := int(data[len(data)-1])
	if padding > len(data) || padding > BlockSize {
		return nil, errors.New("invalid padding")
	}

	// 验证填充的正确性
	for i := len(data) - padding; i < len(data); i++ {
		if data[i] != byte(padding) {
			return nil, errors.New("invalid padding")
		}
	}

	return data[:len(data)-padding], nil
}

// AESEncrypt 使用 AES-CBC 模式加密数据
// 返回格式: [IV:16][ciphertext:len(padded_plaintext)]
func AESEncrypt(plaintext []byte) ([]byte, error) {
	if len(plaintext) == 0 {
		return nil, errors.New("plaintext cannot be empty")
	}

	if !EnableEncrypt {
		return plaintext, nil
	}

	// 创建 AES cipher
	block, err := aes.NewCipher([]byte(AESKey))
	if err != nil {
		return nil, err
	}

	// 添加 PKCS7 填充
	paddedPlaintext := PKCS7Padding(plaintext, BlockSize)

	// 生成随机 IV
	iv := make([]byte, IVSize)
	if _, err := io.ReadFull(rand.Reader, iv); err != nil {
		return nil, err
	}

	// 创建 CBC 加密器
	mode := cipher.NewCBCEncrypter(block, iv)

	// 加密数据
	ciphertext := make([]byte, len(paddedPlaintext))
	mode.CryptBlocks(ciphertext, paddedPlaintext)

	// 组合结果: IV + ciphertext
	result := make([]byte, IVSize+len(ciphertext))
	copy(result[:IVSize], iv)
	copy(result[IVSize:], ciphertext)

	return result, nil
}

// AESDecrypt 使用 AES-CBC 模式解密数据
// 输入格式: [IV:16][ciphertext:len(padded_plaintext)]
func AESDecrypt(encrypted []byte) ([]byte, error) {
	if !EnableEncrypt {
		return encrypted, nil
	}
	if len(encrypted) < IVSize+BlockSize { // IV + 至少一个块
		return nil, errors.New("encrypted data too short")
	}

	// 验证数据长度是否为块大小的倍数
	if (len(encrypted)-IVSize)%BlockSize != 0 {
		return nil, errors.New("encrypted data length is not a multiple of block size")
	}

	// 创建 AES cipher
	block, err := aes.NewCipher([]byte(AESKey))
	if err != nil {
		return nil, err
	}

	// 提取 IV 和 ciphertext
	iv := encrypted[:IVSize]
	ciphertext := encrypted[IVSize:]

	// 创建 CBC 解密器
	mode := cipher.NewCBCDecrypter(block, iv)

	// 解密数据
	plaintext := make([]byte, len(ciphertext))
	mode.CryptBlocks(plaintext, ciphertext)

	// 移除 PKCS7 填充
	unpaddedPlaintext, err := PKCS7UnPadding(plaintext)
	if err != nil {
		return nil, err
	}

	return unpaddedPlaintext, nil
}

// IsEncrypted 检查数据是否可能是加密的
// 这是一个简单的启发式检查，基于数据长度和格式
func IsEncrypted(data []byte) bool {
	// 加密数据的最小长度: IV(16) + 最小消息块(16) = 32字节
	if len(data) < 32 {
		return false
	}

	// 检查数据长度是否符合 CBC 模式要求（IV + 块大小的倍数）
	if (len(data)-IVSize)%BlockSize != 0 {
		return false
	}

	// 检查是否符合我们的加密格式
	// 由于我们使用随机IV，无法通过内容判断，所以这里只做长度检查
	// 在实际应用中，可以考虑添加魔数或版本标识
	return true
}

// EncryptWithVersionFlag 带版本标识的加密
// 格式: [version:1][encrypted_data]
func EncryptWithVersionFlag(plaintext []byte) ([]byte, error) {
	encrypted, err := AESEncrypt(plaintext)
	if err != nil {
		return nil, err
	}

	// 添加版本标识 (0x01 表示加密版本)
	result := make([]byte, 1+len(encrypted))
	result[0] = 0x01 // 版本标识
	copy(result[1:], encrypted)

	return result, nil
}

// DecryptWithVersionFlag 带版本标识的解密
// 格式: [version:1][encrypted_data]
func DecryptWithVersionFlag(data []byte) ([]byte, bool, error) {
	if len(data) == 0 {
		return nil, false, errors.New("data cannot be empty")
	}

	// 检查版本标识
	version := data[0]
	switch version {
	case 0x01:
		// 加密版本，需要解密
		if len(data) < 2 {
			return nil, false, errors.New("encrypted data too short")
		}
		decrypted, err := AESDecrypt(data[1:])
		return decrypted, true, err
	case 0x00:
		// 未加密版本（向后兼容）
		return data[1:], false, nil
	default:
		// 未知版本，假设是未加密的原始数据（向后兼容）
		return data, false, nil
	}
}

// SafeEncrypt 安全加密函数，包含错误处理
func SafeEncrypt(plaintext []byte) []byte {
	if len(plaintext) == 0 {
		return plaintext
	}

	encrypted, err := EncryptWithVersionFlag(plaintext)
	if err != nil {
		// 加密失败时返回原始数据，确保系统继续运行
		// 在生产环境中应该记录这个错误
		return plaintext
	}

	return encrypted
}

// SafeDecrypt 安全解密函数，包含错误处理
func SafeDecrypt(data []byte) []byte {
	if len(data) == 0 {
		return data
	}

	decrypted, wasEncrypted, err := DecryptWithVersionFlag(data)
	if err != nil {
		// 解密失败时返回原始数据（可能是未加密的数据）
		return data
	}

	if wasEncrypted {
		return decrypted
	} else {
		return data
	}
}

// ValidateAESKey 验证AES密钥是否有效
func ValidateAESKey() error {
	if len(AESKey) != 16 {
		return errors.New("AES key must be 16 bytes for AES-128")
	}

	// 测试加密/解密
	testData := []byte("test message")
	encrypted, err := AESEncrypt(testData)
	if err != nil {
		return err
	}

	decrypted, err := AESDecrypt(encrypted)
	if err != nil {
		return err
	}

	if string(decrypted) != string(testData) {
		return errors.New("AES encryption/decryption test failed")
	}

	return nil
}

// GetEncryptionStats 获取加密统计信息
type EncryptionStats struct {
	KeyLength    int    `json:"key_length"`
	Algorithm    string `json:"algorithm"`
	Mode         string `json:"mode"`
	IVSize       int    `json:"iv_size"`
	BlockSize    int    `json:"block_size"`
	OverheadSize int    `json:"overhead_size"` // 加密开销字节数
}

func GetEncryptionStats() EncryptionStats {
	return EncryptionStats{
		KeyLength:    len(AESKey),
		Algorithm:    "AES-128",
		Mode:         "CBC",
		IVSize:       IVSize,
		BlockSize:    BlockSize,
		OverheadSize: IVSize + BlockSize + 1, // IV + 最大填充 + version flag
	}
}
