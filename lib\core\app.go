package core

// ! 程序入口接口
type IZoneApp interface {
	Wait()                            //! 增加一个等待计数
	Done()                            //! 完成一个等待计数
	GetConfig() *Config               //! 系统配置
	IsClosed() bool                   //! 系统是否关闭
	IsProcess() bool                  //! 是否提审模式
	GetPlayerOnline(serverId int) int //! 在线人数，如果id = -1，则是全服人数，统计在线人数
	GetPlayerTotal() int              //! 服务器人数，统计在内存内角色人数
	GetOpenServer() int64
	GetOpenTime() int
	GetTimeArea() int
	GetOpenDay() int64
	GetServerId() int
	GetServerName() string
	GetServerLang() int
	GetWorldLevel(bool) int
	IsHasName(name string) bool

	StartService() //! 开启服务
	StopService()  //! 关闭
	Close()
	GetLoginDay(login string) int
	GetOpenTimeKeyDay(timeStart int64) []int
	GetOpenTimeAllDay(timeStart int64) int

	BroadCastMsgToCamp(camp int, head string, body []byte)
}

type IGameApp interface {
	StartService(config *Config) //! 开启服务
	StopService()                //! 关闭

	Shutdown() bool

	HandleRPC(dataType int, data ...interface{}) interface{} //! 处理RPC
}

type IGateApp interface {
	//AddEvnet(int, interface{}) //! 触发事件

}

type ISession interface {
	SendMsg(head string, body []byte)   //! 发送消息
	SendMsgBatch(body []byte)           //! 打包发送消息，不再加密处理
	SendNetMsg(mainId int, body []byte) //! 发送消息
	SendPBMsg(body []byte)              //! 发送PB消息
}

// ! 系统属性
const (
	PROPERTY_OPEN_SERVER = "OpenServerTime"
	PROPERTY_OPEN_DAY    = "OpenDay"
	PROPERTY_WORLD_LEVEL = "WorldLevel"
)

const (
	PLAYER_ATT_LEVEL = 1
	PLAYER_ATT_VIP   = 2
	PLAYER_ATT_UNAME = 3
	PLAYER_ATT_ICON  = 4
)
