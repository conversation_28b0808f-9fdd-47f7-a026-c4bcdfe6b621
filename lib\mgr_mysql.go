package lib

import (
	"errors"
	"fmt"
	"os"
	"strings"
	"zone/lib/core"
	"zone/lib/db"
)

// 数据库字段检查
var mysqlMgr *MySqlMgr = nil

type MySqlMgr struct {
	tableCheck            []string    //! 游戏表
	logTableCheck         []string    //! 日志表
	fieldCheck            [][2]string //! 字段检查
	createTableStatements []string    //! 建表语句
	addField              [][3]string //! 添加的字段
	modifyField           [][4]string //! 修改字段
	dropField             [][3]string //! 删除字段

	modifyFieldLog [][4]string //! 修改字段的日志

	dbGameUrl string //! game db连接地址
	dbLogUrl  string //! log db连接地址
}

func GetSqlMgr() *MySqlMgr {
	if mysqlMgr == nil {
		mysqlMgr = &MySqlMgr{}
	}
	return mysqlMgr
}

func (self *MySqlMgr) SetDNS(gameDns, logDns string) {
	self.dbGameUrl = gameDns
	self.dbLogUrl = logDns
}

func (self *MySqlMgr) initData() {
	self.tableCheck = []string{}
	self.fieldCheck = [][2]string{}

	self.logTableCheck = []string{}

	self.addField = [][3]string{
		// {"san_liveplayer", "WonTotal", "ALTER TABLE `san_liveplayer` ADD COLUMN `WonTotal` int(11) NOT NULL COMMENT '胜利总场次' AFTER `joingametimes`;"},
		// {"san_liveplayer", "WonAgain", "ALTER TABLE `san_liveplayer` ADD COLUMN `WonAgain` int(11) NOT NULL COMMENT '连胜场次' AFTER `WonTotal`;"},
		// {"san_liveplayer", "RankWorld", "ALTER TABLE `san_liveplayer` ADD COLUMN `RankWorld` int(11) NOT NULL COMMENT '上周排名' AFTER `WonAgain`;"},
		// {"san_liveplayer", "LastGameResult", "ALTER TABLE `san_liveplayer` ADD COLUMN `LastGameResult` int(11) NOT NULL COMMENT '上场战斗结果' AFTER `RankWorld`;"},
	}

	self.modifyField = [][4]string{}

	self.modifyFieldLog = [][4]string{
		{"san_log", "cur", "int", "ALTER TABLE `san_log` MODIFY COLUMN `cur`  bigint(20)  NOT NULL COMMENT '当前值' AFTER `dec`;"},
		{"san_belog", "cur", "int", "ALTER TABLE `san_belog` MODIFY COLUMN `cur`  bigint(20) NOT NULL COMMENT '当前值' AFTER `dec`;"},
	}

	self.createTableStatements = []string{
		// self.Privilege(),
		// self.Resource(),
		// self.Treasure(),
		// self.Drawbox(),
		// self.Census(),
		// self.Abyss(),
		// self.Acient(),
		// self.Economics(),
		// self.Memory(),
		// self.Expedition(),
		// self.NewTurnTable(),
		// self.NewBoss(),
		// self.NewRank(),
		// self.NewRank(),
		// self.TreasureBusiness(),
		// self.UserRich(),
		// self.UserRastar(),
		// self.FightInfoCache(),
		// self.FightInfoUACache(),
		// self.UnionActivity(),
		// self.UserFriend(),
		// self.UserActivityDayBoss(),
		// self.UserNewWarOrder(),
		// self.ActivityDayBoss(),
		// self.ActivityChallenge(),
		// self.Shop13(),
		// self.ActivityWeekends(),
		// self.ActivityData(),
		// self.ActivityOptional(),
		// self.UserCrossServerArena(),
		// self.Shop14(),
		// self.Shop15(),
		// self.PreparatoryTask(),
		// self.BarrageInfo(),
		// self.TimeInfo(),
		// self.ActivityMemory(),
		// self.ServerSign(),
		// self.UserGiftOrder(),
		// self.SeekFate(),
		// self.ActivityShop(),
		// self.Champion(),
		// self.Rune(),
		// self.FightInfoChampion(),
		// self.UserDoLike(),
		//self.Minesweeper(),
	}

	self.dropField = [][3]string{}
}

func (self *MySqlMgr) createTables() {
	for _, stmt := range self.createTableStatements {
		_, _, res := db.GetDBMgr().DBUser.Exec(stmt)
		if !res {
			core.LogError("创建table失败")
			os.Exit(1)
		}
	}
}

// 检查字段是否存在
func (self *MySqlMgr) CheckMysql() {
	self.initData()
	var checkErr error
	for _, filedName := range self.fieldCheck {
		checkErr = self.CheckFiled(filedName[0], filedName[1])
		if checkErr != nil {
			core.LogError(checkErr.Error())
			os.Exit(1)
		}
	}

	for index := range self.tableCheck {
		checkErr = self.CheckTable(self.tableCheck[index])
		if checkErr != nil {
			core.LogError(checkErr.Error())
			os.Exit(1)
		}
	}

	for index := range self.logTableCheck {
		checkErr = self.CheckLogTable(self.logTableCheck[index])
		if checkErr != nil {
			core.LogError(checkErr.Error())
			os.Exit(1)
		}
	}

	self.createTables()
	self.checkAddField()
	self.modifyColumnType()
	self.modifyColumnTypeLog()
	self.checkDropField()
}

func (self *MySqlMgr) CheckFiled(tableName string, filedName string) error {
	sql := "SELECT * FROM information_schema.COLUMNS WHERE TABLE_SCHEMA = '%s' AND TABLE_NAME = '%s'  AND COLUMN_NAME = '%s'"
	dbName := self.getDbName()
	if dbName == "" {
		core.LogDebug("dbName is empty!")
		return nil
	}
	sqlStr := fmt.Sprintf(sql, dbName, tableName, filedName)
	res := db.GetDBMgr().DBUser.Query(sqlStr)
	//LogDebug("sqlStr:", sqlStr, ", CheckFiledParam:", res)
	if res {
		return nil
	}
	return errors.New(tableName + " has no filed:" + filedName)
}

func (self *MySqlMgr) CheckTable(tableName string) error {
	sql := "SELECT * FROM information_schema.COLUMNS WHERE TABLE_SCHEMA = '%s' AND TABLE_NAME = '%s'"
	dbName := self.getDbName()
	if dbName == "" {
		core.LogDebug("dbName is empty!")
		return nil
	}
	sqlStr := fmt.Sprintf(sql, dbName, tableName)
	res := db.GetDBMgr().DBUser.Query(sqlStr)
	//LogDebug("sqlStr:", sqlStr, ", CheckFiledParam:", res)
	if res {
		return nil
	}
	return errors.New(tableName + " not exists!")
}

func (self *MySqlMgr) getDbName() string {
	sqlSplit1 := strings.Split(self.dbGameUrl, "?")
	if len(sqlSplit1) < 1 {
		return ""
	}
	sqlSplit2 := strings.Split(sqlSplit1[0], "/")
	if len(sqlSplit2) < 2 {
		return ""
	}
	return sqlSplit2[1]
}

func (self *MySqlMgr) getDbLogName() string {
	sqlSplit1 := strings.Split(self.dbLogUrl, "?")
	if len(sqlSplit1) < 1 {
		return ""
	}
	sqlSplit2 := strings.Split(sqlSplit1[0], "/")
	if len(sqlSplit2) < 2 {
		return ""
	}
	return sqlSplit2[1]
}

func (self *MySqlMgr) CheckLogTable(tableName string) error {
	sql := "SELECT * FROM information_schema.COLUMNS WHERE TABLE_SCHEMA = '%s' AND TABLE_NAME = '%s'"
	dbName := self.getDbLogName()
	if dbName == "" {
		core.LogDebug("dbName is empty!")
		return nil
	}
	sqlStr := fmt.Sprintf(sql, dbName, tableName)
	res := db.GetDBMgr().DBLog.Query(sqlStr)
	if res {
		return nil
	}
	return errors.New(tableName + " not exists!")
}

func (self *MySqlMgr) checkAddField() {
	var checkErr error
	for _, stmt := range self.addField {
		checkErr = self.CheckFiled(stmt[0], stmt[1])
		if checkErr != nil { // 没有才插入
			_, _, res := db.GetDBMgr().DBUser.Exec(stmt[2])
			if !res {
				core.LogError("增加字段失败", stmt[2])
				os.Exit(1)
			}
		}
	}
}

func (self *MySqlMgr) CheckAndAddField(table, field, sql string) {
	var checkErr error
	checkErr = self.CheckFiled(table, field)
	if checkErr != nil { // 没有才插入
		_, _, res := db.GetDBMgr().DBUser.Exec(sql)
		if !res {
			core.LogError("增加字段失败", sql)
			os.Exit(1)
		}
	}
}

func (self *MySqlMgr) checkDropField() {
	var checkErr error
	for _, stmt := range self.dropField {
		checkErr = self.CheckFiled(stmt[0], stmt[1])
		if checkErr == nil { // 没有才插入
			_, _, res := db.GetDBMgr().DBUser.Exec(stmt[2])
			if !res {
				core.LogError("删除字段失败", stmt[2])
				os.Exit(1)
			}
		}
	}

}

func (self *MySqlMgr) modifyColumnType() {
	for _, stmt := range self.modifyField {
		if len(stmt) != 4 {
			continue
		}

		isStmtOk := true
		for _, v := range stmt {
			if v == "" {
				isStmtOk = false
				break
			}
		}

		if !isStmtOk {
			break
		}

		sql := "SELECT DATA_TYPE FROM information_schema.COLUMNS WHERE TABLE_SCHEMA = '%s' AND TABLE_NAME = '%s'  AND COLUMN_NAME = '%s'"
		dbName := self.getDbName()
		if dbName == "" {
			core.LogError("dbName is empty!")
			os.Exit(1)
		}

		sqlStr := fmt.Sprintf(sql, dbName, stmt[0], stmt[1])
		res, fieldName := db.GetDBMgr().DBUser.QueryColomn(sqlStr)
		if res && fieldName == stmt[2] {
			_, _, res := db.GetDBMgr().DBUser.Exec(stmt[3])
			if !res {
				core.LogError("修改字段类型失败", stmt[3])
				os.Exit(1)
			} else {
				core.LogDebug("修改字段类型成功!")
			}
		}
	}
}

func (self *MySqlMgr) modifyColumnTypeLog() {
	for _, stmt := range self.modifyFieldLog {
		if len(stmt) != 4 {
			continue
		}

		isStmtOk := true
		for _, v := range stmt {
			if v == "" {
				isStmtOk = false
				break
			}
		}

		if !isStmtOk {
			break
		}

		sql := "SELECT DATA_TYPE FROM information_schema.COLUMNS WHERE TABLE_SCHEMA = '%s' AND TABLE_NAME = '%s'  AND COLUMN_NAME = '%s'"
		dbName := self.getDbLogName()
		if dbName == "" {
			core.LogError("dbName is empty!")
			os.Exit(1)
		}

		sqlStr := fmt.Sprintf(sql, dbName, stmt[0], stmt[1])
		res, fieldName := db.GetDBMgr().DBLog.QueryColomn(sqlStr)
		if res && fieldName == stmt[2] {
			_, _, res := db.GetDBMgr().DBLog.Exec(stmt[3])
			if !res {
				core.LogError("修改字段类型失败", stmt[3])
				os.Exit(1)
			} else {
				core.LogDebug("修改字段类型成功!")
			}
		}
	}
}

func (self *MySqlMgr) Privilege() string {
	return `CREATE TABLE IF NOT EXISTS san_privilege (
		uid bigint(20) NOT NULL COMMENT '玩家唯一Id',
		info text NOT NULL COMMENT '数据',
		PRIMARY KEY (uid)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8;`
}
func (self *MySqlMgr) Resource() string {
	return `CREATE TABLE IF NOT EXISTS san_resource (
		uid bigint(20) NOT NULL COMMENT '玩家唯一Id',
		resourceinfo text NOT NULL COMMENT '数据',
		PRIMARY KEY (uid)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8;`
}
func (self *MySqlMgr) Treasure() string {
	return `CREATE TABLE IF NOT EXISTS san_treasure (
		uid bigint(20) NOT NULL COMMENT '玩家唯一Id',
		treasureinfo text NOT NULL COMMENT '数据',
		PRIMARY KEY (uid)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8;`
}
func (self *MySqlMgr) Drawbox() string {
	return `CREATE TABLE IF NOT EXISTS san_drawbox (
		uid bigint(20) NOT NULL COMMENT '玩家唯一Id',
		info text NOT NULL COMMENT '数据',
		PRIMARY KEY (uid)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8;`
}
func (self *MySqlMgr) Census() string {
	return `CREATE TABLE IF NOT EXISTS san_census (
		uid bigint(20) NOT NULL COMMENT '玩家唯一Id',
		info text NOT NULL COMMENT '统计数据',
		PRIMARY KEY (uid)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8;`
}
func (self *MySqlMgr) Abyss() string {
	return `CREATE TABLE IF NOT EXISTS san_abyss (
		uid bigint(20) NOT NULL COMMENT '玩家唯一Id',
		herostate text NOT NULL COMMENT '英雄',
		abyss text NOT NULL COMMENT '单人团体',
		PRIMARY KEY (uid)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8;`
}
func (self *MySqlMgr) Acient() string {
	return `CREATE TABLE IF NOT EXISTS san_acient (
		uid bigint(20) NOT NULL COMMENT '玩家唯一Id',
		level bigint(20) NOT NULL COMMENT '难度等级',
		nextrefreshtime bigint(20) NOT NULL COMMENT '下次刷新地图时间',
		nextorderrefreshtime bigint(20) NOT NULL COMMENT '下次宝库刷新时间',
		acientinfo text NOT NULL COMMENT '关卡信息',
		boxstate text NOT NULL COMMENT '宝箱奖励状态',
		warorder text NOT NULL COMMENT '宝库',
		PRIMARY KEY (uid)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8;`
}
func (self *MySqlMgr) Economics() string {
	return `CREATE TABLE IF NOT EXISTS san_economics (
		uid bigint(20) NOT NULL COMMENT '玩家唯一Id',
		stage bigint(20) NOT NULL COMMENT '期数',
		nextrefreshtime bigint(20) NOT NULL COMMENT '下次刷新时间',
		cardtime bigint(20) NOT NULL COMMENT '抽卡',
		giftinfo text NOT NULL COMMENT '礼包',
		PRIMARY KEY (uid)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8;`
}
func (self *MySqlMgr) Memory() string {
	return `CREATE TABLE IF NOT EXISTS san_memory (
		uid bigint(20) NOT NULL COMMENT '玩家唯一Id',
		info text NOT NULL COMMENT '关卡',
		PRIMARY KEY (uid)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8;`
}
func (self *MySqlMgr) Expedition() string {
	return `CREATE TABLE IF NOT EXISTS san_expedition (
		uid bigint(20) NOT NULL COMMENT '玩家唯一Id',
		periods int(11) NOT NULL COMMENT '期数',
		taskinfo text NOT NULL COMMENT '任务',
		nextweektime bigint(20) NOT NULL COMMENT '下次周刷新',
		temple text NOT NULL COMMENT '神庙',
		PRIMARY KEY (uid)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8;`
}
func (self *MySqlMgr) NewTurnTable() string {
	return `CREATE TABLE IF NOT EXISTS san_newturntable (
		uid bigint(20) NOT NULL COMMENT '玩家唯一Id',
		turntableinfo text NOT NULL COMMENT '任务信息',
		ver text NOT NULL COMMENT '版本',
		PRIMARY KEY (uid)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8;`
}
func (self *MySqlMgr) NewBoss() string {
	return `CREATE TABLE IF NOT EXISTS san_newboss (
		uid bigint(20) NOT NULL COMMENT '玩家唯一Id',
		starttime bigint(20) NOT NULL COMMENT '开始时间',
		endtime bigint(20) NOT NULL COMMENT '结束时间',
		historymaxlevel int(11) NOT NULL COMMENT '历史最大等级',
		nowlevel int(11) NOT NULL COMMENT '当前等级',
		challengetimes int(11) NOT NULL COMMENT '挑战次数',
		PRIMARY KEY (uid)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8;`
}
func (self *MySqlMgr) NewRank() string {
	return `CREATE TABLE IF NOT EXISTS san_newrank(
        id bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '唯一Id',
		rankid int(11) NOT NULL COMMENT '',
		rankpos int(11) NOT NULL COMMENT '',
        info text NOT NULL COMMENT '具体数据',
        PRIMARY KEY (id) USING BTREE
		) ENGINE=InnoDB DEFAULT CHARSET=utf8;`
}
func (self *MySqlMgr) TreasureBusiness() string {
	return `CREATE TABLE IF NOT EXISTS san_treasurebusiness(
        id int(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '唯一Id',
		periods int(11) NOT NULL COMMENT '',
		isopen int(11) NOT NULL COMMENT '',
		starttime bigint(20) NOT NULL COMMENT '开始时间',
		centretime bigint(20) NOT NULL COMMENT '',
		endtime bigint(20) NOT NULL COMMENT '结束时间',
        PRIMARY KEY (id) USING BTREE
		) ENGINE=InnoDB DEFAULT CHARSET=utf8;`
}
func (self *MySqlMgr) UserRich() string {
	return `CREATE TABLE IF NOT EXISTS san_userrich(
        uid bigint(20) NOT NULL COMMENT '玩家唯一Id',
		richinfo text NOT NULL COMMENT '秘境信息',
		userrichinfo text NOT NULL COMMENT '玩家信息',
		PRIMARY KEY (uid)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8;`
}
func (self *MySqlMgr) UserRastar() string {
	return `CREATE TABLE IF NOT EXISTS san_userrastar(
        uid bigint(20) NOT NULL COMMENT '玩家唯一Id',
		thingsinfo text NOT NULL COMMENT '秘宝',
		info text NOT NULL COMMENT '羁绊',
		PRIMARY KEY (uid)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8;`
}
func (self *MySqlMgr) FightInfoCache() string {
	return `CREATE TABLE IF NOT EXISTS san_fightinfocache (
		uid bigint(20) NOT NULL COMMENT '玩家唯一Id',
		fightInfo text NOT NULL COMMENT '战斗结构',
		PRIMARY KEY (uid)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8;`
}
func (self *MySqlMgr) FightInfoUACache() string {
	return `CREATE TABLE IF NOT EXISTS san_fightinfouacache (
		uid bigint(20) NOT NULL COMMENT '玩家唯一Id',
		fightInfo text NOT NULL COMMENT '战斗结构',
		PRIMARY KEY (uid)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8;`
}
func (self *MySqlMgr) UnionActivity() string {
	return `CREATE TABLE IF NOT EXISTS san_unionactivity (
		id int(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '唯一Id',
		period int(11) NOT NULL COMMENT '',
		stage int(11) NOT NULL COMMENT '',
		starttime bigint(20) NOT NULL COMMENT '开始时间',
		attacktime bigint(20) NOT NULL COMMENT '',
		endtime bigint(20) NOT NULL COMMENT '结束时间',
		info mediumtext NOT NULL COMMENT '羁绊',
        PRIMARY KEY (id) USING BTREE
		) ENGINE=InnoDB DEFAULT CHARSET=utf8;`
}

func (self *MySqlMgr) UserFriend() string {
	return `CREATE TABLE IF NOT EXISTS san_userfriend (
		uid bigint(20) NOT NULL COMMENT '玩家唯一Id',
		friends text NOT NULL COMMENT '好友',
		applys text NOT NULL COMMENT '申请列表',
		applieds text NOT NULL COMMENT '被申请列表',
		black text NOT NULL COMMENT '黑名单',
		recommend text NOT NULL COMMENT '推荐好友',
		chatsendinfo text NOT NULL COMMENT '发送的私聊',
		chatgetinfo text NOT NULL COMMENT '收到的私聊',
		sendsign text NOT NULL COMMENT '赠送标记',
		getsign text NOT NULL COMMENT '获取标记',
		PRIMARY KEY (uid)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8;`
}
func (self *MySqlMgr) UserActivityDayBoss() string {
	return `CREATE TABLE IF NOT EXISTS san_useractivitydayboss (
		uid bigint(20) NOT NULL COMMENT '玩家唯一Id',
		activitybossinfo text NOT NULL COMMENT '好友',
		PRIMARY KEY (uid)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8;`
}

// 新战令
func (self *MySqlMgr) UserNewWarOrder() string {
	return `CREATE TABLE IF NOT EXISTS san_newwarorder (
			uid bigint(20) NOT NULL COMMENT '玩家唯一Id',
			orderlist text NOT NULL COMMENT '战令列表',
			PRIMARY KEY (uid)
			) ENGINE=InnoDB DEFAULT CHARSET=utf8;`
}
func (self *MySqlMgr) ActivityDayBoss() string {
	return `CREATE TABLE IF NOT EXISTS san_activitydayboss (
			id int(11) NOT NULL COMMENT 'Id',
			period int(11) NOT NULL COMMENT '期数',
			starttime  bigint(20) NOT NULL COMMENT '开始时间',
			endtime  bigint(20) NOT NULL COMMENT '结束时间',
			dayrewardtime bigint(20) NOT NULL COMMENT '每日结算奖励时间',
			activitydaybosstop text NOT NULL COMMENT '宿命',
			PRIMARY KEY (id)
			) ENGINE=InnoDB DEFAULT CHARSET=utf8;`
}
func (self *MySqlMgr) ActivityChallenge() string {
	return `CREATE TABLE IF NOT EXISTS san_activitychallenge (
			uid bigint(20) NOT NULL COMMENT '玩家唯一Id',
			starttime  bigint(20) NOT NULL COMMENT '开始时间',
			endtime  bigint(20) NOT NULL COMMENT '结束时间',
			activityinfo text NOT NULL COMMENT '英雄挑战',
			PRIMARY KEY (uid)
			) ENGINE=InnoDB DEFAULT CHARSET=utf8;`
}
func (self *MySqlMgr) Shop13() string {
	return `CREATE TABLE IF NOT EXISTS san_newshop13 (
			uid bigint(20) NOT NULL COMMENT '玩家唯一Id',
			shoptype int(11) NOT NULL COMMENT '商店类型',
			shopgood text NOT NULL COMMENT '道具购买状态',
			refindex  int(11) NOT NULL COMMENT '刷新次数(随机商店免费刷新次数)',
			shopnextgood text NOT NULL COMMENT '道具购买状态',
			todayrefcount int(11) NOT NULL COMMENT '商店类型',
			sysreftime  bigint(20) NOT NULL COMMENT '系统刷新时间（非随机商店）',
			lastupdtime  bigint(20) NOT NULL COMMENT '上次刷新时间（随机商店）',
			refresh int(11) NOT NULL COMMENT '钻石消耗刷新次数',
			PRIMARY KEY (uid)
			) ENGINE=InnoDB DEFAULT CHARSET=utf8;`
}
func (self *MySqlMgr) ActivityWeekends() string {
	return `CREATE TABLE IF NOT EXISTS san_activityweekends (
			uid bigint(20) NOT NULL COMMENT '玩家唯一Id',
			n3 int(11) NOT NULL COMMENT '期数',
			ngroup int(11) NOT NULL COMMENT '组',
			info text NOT NULL COMMENT '信息',
			PRIMARY KEY (uid)
			) ENGINE=InnoDB DEFAULT CHARSET=utf8;`
}

func (self *MySqlMgr) ActivityData() string {
	return `CREATE TABLE IF NOT EXISTS san_activitydata (
		id int(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '唯一Id',
		activityrank text NOT NULL COMMENT '活动排行',
		activitytask text NOT NULL COMMENT '活动任务',
        PRIMARY KEY (id) USING BTREE
		) ENGINE=InnoDB DEFAULT CHARSET=utf8;`
}
func (self *MySqlMgr) ActivityOptional() string {
	return `CREATE TABLE IF NOT EXISTS san_optional (
			uid bigint(20) NOT NULL COMMENT '玩家唯一Id',
			n3 int(11) NOT NULL COMMENT '期数',
			ngroup int(11) NOT NULL COMMENT '组',
			info text NOT NULL COMMENT '信息',
			PRIMARY KEY (uid)
			) ENGINE=InnoDB DEFAULT CHARSET=utf8;`
}
func (self *MySqlMgr) UserCrossServerArena() string {
	return `CREATE TABLE IF NOT EXISTS san_usercrossserverarena (
			uid bigint(20) NOT NULL COMMENT '玩家唯一Id',
			period int(11) NOT NULL COMMENT '期数',
			taskinfo text NOT NULL COMMENT '任务',
			cardnum int(11) NOT NULL COMMENT '当前翻牌数',
			wintimes int(11) NOT NULL COMMENT '胜利次数',
			cardinfo text NOT NULL COMMENT '翻牌信息',
			likeinfo text NOT NULL COMMENT '点赞信息',
			PRIMARY KEY (uid)
			) ENGINE=InnoDB DEFAULT CHARSET=utf8;`
}

func (self *MySqlMgr) Shop14() string {
	return `CREATE TABLE IF NOT EXISTS san_newshop14 (
			uid bigint(20) NOT NULL COMMENT '玩家唯一Id',
			shoptype int(11) NOT NULL COMMENT '商店类型',
			shopgood text NOT NULL COMMENT '道具购买状态',
			refindex  int(11) NOT NULL COMMENT '刷新次数(随机商店免费刷新次数)',
			shopnextgood text NOT NULL COMMENT '道具购买状态',
			todayrefcount int(11) NOT NULL COMMENT '商店类型',
			sysreftime  bigint(20) NOT NULL COMMENT '系统刷新时间（非随机商店）',
			lastupdtime  bigint(20) NOT NULL COMMENT '上次刷新时间（随机商店）',
			refresh int(11) NOT NULL COMMENT '钻石消耗刷新次数',
			PRIMARY KEY (uid)
			) ENGINE=InnoDB DEFAULT CHARSET=utf8;`
}
func (self *MySqlMgr) Shop15() string {
	return `CREATE TABLE IF NOT EXISTS san_newshop15 (
			uid bigint(20) NOT NULL COMMENT '玩家唯一Id',
			shoptype int(11) NOT NULL COMMENT '商店类型',
			shopgood text NOT NULL COMMENT '道具购买状态',
			refindex  int(11) NOT NULL COMMENT '刷新次数(随机商店免费刷新次数)',
			shopnextgood text NOT NULL COMMENT '道具购买状态',
			todayrefcount int(11) NOT NULL COMMENT '商店类型',
			sysreftime  bigint(20) NOT NULL COMMENT '系统刷新时间（非随机商店）',
			lastupdtime  bigint(20) NOT NULL COMMENT '上次刷新时间（随机商店）',
			refresh int(11) NOT NULL COMMENT '钻石消耗刷新次数',
			PRIMARY KEY (uid)
			) ENGINE=InnoDB DEFAULT CHARSET=utf8;`
}
func (self *MySqlMgr) PreparatoryTask() string {
	return `CREATE TABLE IF NOT EXISTS san_userpreparatorytask (
			uid bigint(20) NOT NULL COMMENT '玩家唯一Id',
			taskinfo text NOT NULL COMMENT '任务',
			PRIMARY KEY (uid)
			) ENGINE=InnoDB DEFAULT CHARSET=utf8;`
}
func (self *MySqlMgr) BarrageInfo() string {
	return `CREATE TABLE IF NOT EXISTS san_barrageinfo (
			keyid int(11) NOT NULL COMMENT '类型',
			barrageinfo text NOT NULL COMMENT '弹幕内容',
			PRIMARY KEY (keyid)
			) ENGINE=InnoDB DEFAULT CHARSET=utf8;`
}
func (self *MySqlMgr) TimeInfo() string {
	return `CREATE TABLE IF NOT EXISTS san_timeinfo (
			id int(11) NOT NULL COMMENT '',
			timeinfo text NOT NULL COMMENT '时间配置',
			PRIMARY KEY (id)
			) ENGINE=InnoDB DEFAULT CHARSET=utf8;`
}

func (self *MySqlMgr) ActivityMemory() string {
	return `CREATE TABLE IF NOT EXISTS san_activitymemory (
		uid bigint(20) NOT NULL COMMENT '玩家唯一Id',
        n3 int(11) NOT NULL COMMENT '期数',
        ngroup int(11) NOT NULL COMMENT '组',
		info text NOT NULL COMMENT '关卡',
		PRIMARY KEY (uid)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8;`
}

func (self *MySqlMgr) ServerSign() string {
	return `CREATE TABLE IF NOT EXISTS san_serversign (
		uid bigint(20) NOT NULL COMMENT '玩家唯一Id',
		doLike text NOT NULL COMMENT '点赞记录',
		PRIMARY KEY (uid)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8;`
}
func (self *MySqlMgr) UserGiftOrder() string {
	return `CREATE TABLE IF NOT EXISTS san_giftorder (
			uid bigint(20) NOT NULL COMMENT '玩家唯一Id',
			orderlist text NOT NULL COMMENT '战令列表',
			PRIMARY KEY (uid)
			) ENGINE=InnoDB DEFAULT CHARSET=utf8;`
}
func (self *MySqlMgr) SeekFate() string {
	return `CREATE TABLE IF NOT EXISTS san_seekfate (
			uid bigint(20) NOT NULL COMMENT '玩家唯一Id',
			info text NOT NULL COMMENT '寻缘',
			PRIMARY KEY (uid)
			) ENGINE=InnoDB DEFAULT CHARSET=utf8;`
}
func (self *MySqlMgr) ActivityShop() string {
	return `CREATE TABLE IF NOT EXISTS san_activityshop (
			uid bigint(20) NOT NULL COMMENT '玩家唯一Id',
			activityshop text NOT NULL COMMENT '商店信息',
			PRIMARY KEY (uid)
			) ENGINE=InnoDB DEFAULT CHARSET=utf8;`
}

func (self *MySqlMgr) Champion() string {
	return `CREATE TABLE IF NOT EXISTS san_champion (
			id int(11) NOT NULL COMMENT '',
			period int(11) NOT NULL COMMENT '',
			stage int(11) NOT NULL COMMENT '',
			signtime  bigint(20) NOT NULL COMMENT '数据生成时间',
			scoretime  bigint(20) NOT NULL COMMENT '积分赛开始时间',
			eliminatetime  bigint(20) NOT NULL COMMENT '淘汰赛开始时间',
			endtime  bigint(20) NOT NULL COMMENT '结束时间',
			nexttime  bigint(20) NOT NULL COMMENT '清空数据时间',
			scoreinfo mediumtext NOT NULL COMMENT '积分赛信息',
			eliminateinfo mediumtext NOT NULL COMMENT '淘汰赛信息',
			PRIMARY KEY (id)
			) ENGINE=InnoDB DEFAULT CHARSET=utf8;`
}
func (self *MySqlMgr) Rune() string {
	return `CREATE TABLE IF NOT EXISTS san_rune (
			uid bigint(20) NOT NULL COMMENT '玩家唯一Id',
			runeitems text NOT NULL COMMENT '符石',
			PRIMARY KEY (uid)
			) ENGINE=InnoDB DEFAULT CHARSET=utf8;`
}

func (self *MySqlMgr) FightInfoChampion() string {
	return `CREATE TABLE IF NOT EXISTS san_fightinfochampion (
		uid bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '唯一Id',
		fightinfo text NOT NULL COMMENT '战斗结构',
        PRIMARY KEY (uid) USING BTREE
		) ENGINE=InnoDB DEFAULT CHARSET=utf8;`
}

func (self *MySqlMgr) UserDoLike() string {
	return `CREATE TABLE IF NOT EXISTS san_userdolike (
			uid bigint(20) NOT NULL COMMENT '玩家唯一Id',
			dolike text NOT NULL COMMENT '点赞信息',
			PRIMARY KEY (uid)
			) ENGINE=InnoDB DEFAULT CHARSET=utf8;`
}

func (self *MySqlMgr) Minesweeper() string {
	return `CREATE TABLE IF NOT EXISTS san_minesweeper (
		uid bigint(20) UNSIGNED NOT NULL COMMENT '唯一Id',
		mapinfo text NOT NULL COMMENT '战斗结构',
		itemlist text NOT NULL COMMENT '道具栏',
		herostate text NOT NULL COMMENT '英雄血量',
		skipbattle int(11) NOT NULL COMMENT '跳过战斗',
		isfirst int(11) NOT NULL COMMENT '第一次完整通关',
        PRIMARY KEY (uid) USING BTREE
		) ENGINE=InnoDB DEFAULT CHARSET=utf8;`
}
