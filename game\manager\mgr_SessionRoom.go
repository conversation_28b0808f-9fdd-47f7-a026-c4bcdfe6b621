package manager

import (
	"sync"
	"zone/game/mods"
	"zone/lib/network"
)

type SessionRoomMgr struct {
	MapSessionRoom *sync.Map
}

var s_SessionRoomMgr *SessionRoomMgr

func GetSessionRoomMgr() *SessionRoomMgr {
	if s_SessionRoomMgr == nil {
		s_SessionRoomMgr = new(SessionRoomMgr)
		s_SessionRoomMgr.MapSessionRoom = new(sync.Map)

		// 注册到mods包的管理器注册表
		mods.RegisterSessionRoomManager(s_SessionRoomMgr)
	}

	return s_SessionRoomMgr
}

func (self *SessionRoomMgr) GetRoom(session *network.Session) *mods.LiveRoom {
	if room, ok := self.MapSessionRoom.Load(session); ok {
		liveRoom := room.(*mods.LiveRoom)
		if liveRoom != nil {
			return liveRoom
		}
	}
	return nil
}

func (self *SessionRoomMgr) OnCloseSession(session *network.Session) {
	if room, ok := self.MapSessionRoom.Load(session); ok {
		liveRoom := room.(*mods.LiveRoom)
		if liveRoom != nil {
			// 检查session是否匹配，避免误操作
			if liveRoom.RoomId == session.RoomID {
				// 安全地清理room中的session引用
				liveRoom.SafeCloseSession(session)
			}
		}
		// 从映射中删除session-room关联
		self.MapSessionRoom.Delete(session)
	}
}

func (self *SessionRoomMgr) Add(session *network.Session, room *mods.LiveRoom) {
	self.MapSessionRoom.Store(session, room)
}

func (self *SessionRoomMgr) Delete(session *network.Session) {
	self.MapSessionRoom.Delete(session)
}
