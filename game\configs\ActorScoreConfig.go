package configs

import (
	"fmt"
	"sync"
	"zone/lib/config"
	"zone/lib/core"
	"zone/pb/Data"
)

// 主播ID为自定义ID，从1开始一直加
type ActorScoreConfig struct {
	MapActorScore *sync.Map
}

var s_ActorScoreConfig *ActorScoreConfig = nil

func GetActorScoreConfig() *ActorScoreConfig {
	if s_ActorScoreConfig == nil {
		s_ActorScoreConfig = new(ActorScoreConfig)
	}

	return s_ActorScoreConfig
}

func (self *ActorScoreConfig) Init() {
	// 使用新的数据加载器
	var configs []*Data.Actorconfig
	err := config.GetDataLoader().LoadData("actorconfig", &configs)
	if err != nil {
		core.LogError("加载ActorConfig失败:", err)
		return
	}

	self.MapActorScore = new(sync.Map)
	for _, config := range configs {
		self.MapActorScore.Store(int(config.Id), config)
	}

	core.LogInfo(fmt.Sprintf("加载ActorConfig成功，共%d条记录", len(configs)))
}

func (self *ActorScoreConfig) GetActorScoreConfig(id int) *Data.Actorconfig {
	if config, ok := self.MapActorScore.Load(id); ok {
		return config.(*Data.Actorconfig)
	}
	return nil
}
