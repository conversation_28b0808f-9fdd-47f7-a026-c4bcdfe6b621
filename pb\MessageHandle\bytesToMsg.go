package MessageHandle

import (
	"encoding/binary"
	"fmt"
	"reflect"
	"zone/lib/crypto"
	"zone/lib/network"

	"google.golang.org/protobuf/proto"
)

// BytesToMsg 将字节数组转换为PBMsg
func BytesToMsg(v []byte) *network.PBMsg {
	// AES 解密处理 - 在函数开始时对输入数据进行解密
	// 使用安全解密函数，确保即使解密失败也不会影响系统运行
	decryptedData := crypto.SafeDecrypt(v)

	// 使用解密后的数据继续执行原有的消息解析逻辑
	// Check if we have at least 8 bytes for the header (2 * 4 bytes)
	if len(decryptedData) < 8 {
		return nil
	}

	// Parse MainID from first 4 bytes
	mainID := int32(binary.LittleEndian.Uint32(decryptedData[0:4]))

	// Parse Length from next 4 bytes
	length := int(binary.LittleEndian.Uint32(decryptedData[4:8]))

	// Validate that we have enough bytes for the message data
	if len(decryptedData) < 8+length {
		return nil
	}

	// Get the message type based on MainID
	msgType, exists := MessageC2STypeMap[mainID]

	// Create message instance if type exists in our mapping
	if exists && length > 0 {
		// Create a new instance of the message type
		msgPtr := reflect.New(msgType)
		msgInstance := msgPtr.Interface()

		// Check if the message instance implements proto.Message interface
		if protoMsg, ok := msgInstance.(proto.Message); ok {
			// Extract message data from byte array (starting from byte 8)
			msgData := decryptedData[8 : 8+length]

			// Unmarshal the protobuf data into the message instance
			if err := proto.Unmarshal(msgData, protoMsg); err != nil {
				// If unmarshaling fails, return PBMsg with nil message
				fmt.Printf("Error unmarshaling message for MainID %d: %v\n", mainID, err)
				return network.NewPBMsgWithLength(mainID, length, nil)
			} else {
				// Successfully unmarshaled, create PBMsg with the message instance
				return network.NewPBMsgWithLength(mainID, length, msgInstance)
			}
		} else {
			// If not a proto.Message, return PBMsg with nil message
			fmt.Printf("Message instance for MainID %d does not implement proto.Message\n", mainID)
			return network.NewPBMsgWithLength(mainID, length, nil)
		}
	} else {
		if !exists {
			fmt.Printf("No message type mapping found for MainID %d\n", mainID)
		}
		msgPtr := reflect.New(msgType)
		msgInstance := msgPtr.Interface()
		// Return PBMsg even if no message type mapping exists
		return network.NewPBMsgWithLength(mainID, length, msgInstance)
	}
}
