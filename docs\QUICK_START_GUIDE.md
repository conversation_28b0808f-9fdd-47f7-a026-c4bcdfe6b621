# MapBattle游戏服务器快速开始指南

## 🚀 环境准备

### 系统要求
- **操作系统**: Windows 10+ / Linux / macOS
- **Go版本**: 1.24.3+
- **MySQL**: 5.7+
- **Redis**: 6.0+
- **内存**: 最低4GB，推荐8GB+
- **存储**: 最低10GB可用空间

### 依赖安装

#### Windows环境
```cmd
# 1. 安装Go (下载地址: https://golang.org/dl/)
# 2. 安装MySQL
# 3. 安装Redis
# 4. 验证安装
go version
mysql --version
redis-cli --version
```

#### Linux环境
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install golang-go mysql-server redis-server

# CentOS/RHEL
sudo yum install golang mysql-server redis

# 验证安装
go version
mysql --version
redis-cli --version
```

## 📦 项目部署

### 1. 获取代码
```bash
# 克隆项目（如果使用Git）
git clone [项目地址]
cd MapBattle/Server/zone

# 或直接解压项目文件包
```

### 2. 安装Go依赖
```bash
# 下载依赖
go mod tidy

# 更新vendor目录（可选）
go mod vendor
```

### 3. 数据库配置

#### MySQL数据库初始化
```sql
-- 创建数据库
CREATE DATABASE mapbattle CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE live_log_001 CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户（可选）
CREATE USER 'gameserver'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON mapbattle.* TO 'gameserver'@'localhost';
GRANT ALL PRIVILEGES ON live_log_001.* TO 'gameserver'@'localhost';
FLUSH PRIVILEGES;

-- 导入数据库结构
USE mapbattle;
SOURCE sql/mapbattle.sql;

USE live_log_001;
SOURCE sql/live_log_001.sql;
```

#### Redis配置
```bash
# 启动Redis服务
# Windows: 运行redis-server.exe
# Linux: sudo systemctl start redis

# 测试连接
redis-cli ping
# 应该返回: PONG
```

### 4. 配置文件设置
编辑 `config.json` 文件：

```json
{
  "serverid": 1,
  "servername": "test001",
  "wshost": "0.0.0.0:10027",
  "database": {
    "dbuser": "root:your_password@tcp(127.0.0.1:3306)/mapbattle?charset=utf8&timeout=10s",
    "dblog": "root:your_password@tcp(127.0.0.1:3306)/live_log_001?charset=utf8&timeout=10s",
    "redis": "127.0.0.1:6379",
    "redisauth": "",
    "redisdb": 0
  }
}
```

## 🔧 代码生成

### 1. 编译pb_exporter工具
```bash
cd tools
go build -o pb_exporter.exe pb_exporter.go
```

### 2. 生成Protocol Buffer代码
```bash
# Windows
generate_all.bat

# Linux/macOS
chmod +x generate_all.sh
./generate_all.sh
```

### 3. 验证生成结果
检查以下目录是否有文件生成：
- `pb/Message/` - Protocol Buffer Go文件
- `pb/Data/` - 配置数据Go文件
- `game/Request/` - S2C消息发送函数
- `game/Response/` - C2S消息处理函数
- `bytes/` - 二进制配置数据

## 🏃‍♂️ 启动服务器

### 1. 编译主程序
```bash
go build -o main.exe main.go
```

### 2. 启动服务器
```bash
# Windows
main.exe

# Linux/macOS
./main
```

### 3. 验证启动
服务器启动后应该看到类似输出：
```
服务器绑定地址: 0.0.0.0:10027
服务器版本: 0
正在启动区域服务器...
Starting service on 0.0.0.0:10027
```

## 🧪 功能测试

### 1. WebSocket连接测试
```bash
# 使用wscat工具测试（需要先安装: npm install -g wscat）
wscat -c ws://localhost:10027

# 或使用浏览器开发者工具
const ws = new WebSocket('ws://localhost:10027');
ws.onopen = () => console.log('连接成功');
ws.onmessage = (e) => console.log('收到消息:', e.data);
```

### 2. HTTP API测试
```bash
# 健康检查
curl http://localhost:10027/health

# 服务器状态
curl http://localhost:10027/stats
```

### 3. 数据库连接测试
```bash
# 检查MySQL连接
mysql -h127.0.0.1 -uroot -p -e "SHOW DATABASES;"

# 检查Redis连接
redis-cli ping
```

## 🔍 故障排查

### 常见问题

#### 1. 端口被占用
```bash
# Windows查看端口占用
netstat -ano | findstr :10027

# Linux查看端口占用
lsof -i :10027

# 解决方案：修改config.json中的wshost端口
```

#### 2. 数据库连接失败
```bash
# 检查MySQL服务状态
# Windows: services.msc查看MySQL服务
# Linux: sudo systemctl status mysql

# 检查连接字符串格式
# 格式: username:password@tcp(host:port)/database?charset=utf8&timeout=10s
```

#### 3. Redis连接失败
```bash
# 检查Redis服务状态
# Windows: 任务管理器查看redis-server进程
# Linux: sudo systemctl status redis

# 检查Redis配置
redis-cli config get "*"
```

#### 4. 代码生成失败
```bash
# 检查protoc工具是否存在
tools/protoc.exe --version

# 检查Excel文件格式
# 确保Excel文件在正确的目录中
# 检查文件是否被其他程序占用
```

## 📊 性能测试

### 1. 基础性能测试
```bash
cd DBTest
go run main.go -players=100 -duration=30
```

### 2. 压力测试
```bash
cd StressTesting
./stress-testing.exe -players=500 -duration=60
```

### 3. 监控指标
- **连接数**: 当前WebSocket连接数量
- **QPS**: 每秒处理的消息数量
- **延迟**: 消息处理平均延迟
- **内存使用**: 服务器内存占用
- **CPU使用**: 服务器CPU占用率

## 🛠️ 开发调试

### 1. 启用调试模式
```json
{
  "log": {
    "loglevel": 0,
    "logconsole": true
  }
}
```

### 2. 查看日志
```bash
# 实时查看日志
tail -f log/$(date +%Y%m%d).log

# Windows
type log\20250621.log
```

### 3. 性能分析
```bash
# 启用pprof（在代码中添加）
import _ "net/http/pprof"

# 访问性能分析页面
http://localhost:6060/debug/pprof/
```

## 📚 下一步

### 学习资源
1. **技术文档**: 阅读 `docs/MAPBATTLE_TECHNICAL_DOCUMENTATION.md`
2. **API文档**: 查看Protocol Buffer消息定义
3. **代码示例**: 参考 `examples/` 目录
4. **工具文档**: 阅读 `tools/README.md`

### 开发建议
1. **熟悉架构**: 理解分层架构和模块职责
2. **消息协议**: 学习Protocol Buffer消息定义
3. **数据流程**: 了解数据存储和缓存策略
4. **错误处理**: 掌握错误处理和日志记录
5. **性能优化**: 学习并发处理和性能调优

### 扩展功能
1. **新增消息类型**: 修改.proto文件并重新生成代码
2. **添加游戏功能**: 在game/目录下添加新的业务逻辑
3. **优化性能**: 调整缓存策略和连接池配置
4. **监控告警**: 集成监控系统和告警机制

---

🎉 **恭喜！** 您已经成功启动了MapBattle游戏服务器！

如有问题，请参考技术文档或联系开发团队。
