// JoysGames copyrights this specification. No part of this specification may be
// reproduced in any form or means, without the prior written consent of JoysGames.
//
// This specification is preliminary and is subject to change at any time without notice.
// JoysGames assumes no responsibility for any errors contained herein.
//
// The above copyright notice and this permission notice shall be included
// in all copies or substantial portions of the Software.
// @package JGServer
// @copyright joysgames.cn All rights reserved.
// @version v1.0

package core

import "reflect"

type SliceCallback func(value interface{}, key int) bool

type ReflectCallback func(value reflect.Value, key int) bool

type CallSliceOperation func(value interface{}) bool

// 查找切片索引
func FindSlice(arr interface{}, cb SliceCallback) int {
	valType := reflect.TypeOf(arr).Kind()
	if valType != reflect.Slice {
		return -1
	}
	varArr := reflect.ValueOf(arr)
	for i := 0; i < varArr.Len(); i++ {
		item := varArr.Index(i)
		if cb(item.Interface(), i) {
			return i
		}
	}
	return -1
}

// 遍历切片,直到函数cb返回false
func ForEachSlice(arr interface{}, cb CallSliceOperation) {
	valType := reflect.TypeOf(arr).Kind()
	if valType != reflect.Slice {
		return
	}
	varArr := reflect.ValueOf(arr)
	for i := 0; i < varArr.Len(); i++ {
		item := varArr.Index(i)
		if !cb(item.Interface()) {
			break
		}
	}
}

// 删除切片
func RemoveSliceByIdx(arr []interface{}, index int) []interface{} {
	j := 0
	for key, value := range arr {
		if key != index {
			arr[j] = value
			j += 1
		}
	}
	return arr[:j]
}

// 查找反射切片索引
func FindReflectSlice(arr []reflect.Value, cb ReflectCallback) int {
	for key, value := range arr {
		if cb(value, key) {
			return key
		}
	}
	return -1
}

// 根据索引查找切片索引
func RemoveReflectSliceByIdx(arr []reflect.Value, index int) []reflect.Value {
	j := 0
	for key, value := range arr {
		if key != index {
			arr[j] = value
			j += 1
		}
	}
	return arr[:j]
}

// 根据值删除反射切片
func RemoveReflectSliceByVal(arr []reflect.Value, item reflect.Value) []reflect.Value {
	j := 0
	for _, value := range arr {
		if value != item {
			arr[j] = value
			j += 1
		}
	}
	return arr[:j]
}
