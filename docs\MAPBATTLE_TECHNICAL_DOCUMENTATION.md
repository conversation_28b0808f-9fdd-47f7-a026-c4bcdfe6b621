# MapBattle游戏服务器技术文档

## 📋 项目概览

### 项目基本信息
- **项目名称**: MapBattle游戏服务器
- **项目类型**: 实时多人在线游戏服务器
- **技术栈**: Go 1.24.3, Protocol Buffer, WebSocket, HTTP, Redis, MySQL
- **主要功能**: 实时游戏对战、直播互动、玩家管理、数据持久化

### 核心特性
- 🎮 **实时游戏对战**: 支持多人实时对战，帧同步机制
- 📺 **直播集成**: 与快手、抖音等直播平台深度集成
- 🔄 **高并发处理**: 支持3500+并发连接，优化的连接池管理
- 💾 **混合存储**: Redis缓存 + MySQL持久化的混合存储架构
- 🛡️ **安全机制**: AES加密、频率限制、连接管理
- 🔧 **自动化工具**: Protocol Buffer代码生成、Excel配置转换

## 🏗️ 项目架构

### 目录结构
```
Server/zone/
├── main.go                 # 主入口文件
├── config.json            # 主配置文件
├── app/                   # 应用层
│   └── app_zone.go        # 区域服务器应用
├── gate/                  # 网关层
│   ├── app_gate.go        # 网关应用
│   ├── MsgHandleMap.go    # 消息处理映射
│   ├── messagemap.go      # 消息类型映射
│   └── bytesToMsg.go      # 消息反序列化
├── game/                  # 游戏逻辑层
│   ├── app_game.go        # 游戏应用
│   ├── Request/           # S2C消息发送函数
│   ├── Response/          # C2S消息处理函数
│   ├── SessionRoom/       # 会话房间管理
│   └── player/            # 玩家相关模块
├── lib/                   # 核心库
│   ├── network/           # 网络通信
│   ├── db/               # 数据库管理
│   ├── core/             # 核心功能
│   ├── storage/          # 存储管理
│   ├── crypto/           # 加密解密
│   ├── ratelimit/        # 频率限制
│   └── utils/            # 工具函数
├── pb/                   # Protocol Buffer文件
│   ├── Message/          # 消息定义
│   └── Data/             # 配置数据
├── tools/                # 开发工具
│   ├── pb_exporter.go    # PB代码生成工具
│   └── excel_to_pb/      # Excel转PB工具
├── bytes/                # 配置数据文件
├── DBTest/               # 数据库性能测试
└── traffic_monitor/      # 流量监控
```

### 技术栈详情
```go
// 主要依赖 (go.mod)
require (
    github.com/gin-gonic/gin v1.10.1        // HTTP框架
    github.com/gorilla/websocket v1.5.3     // WebSocket支持
    github.com/golang/protobuf v1.5.4       // Protocol Buffer
    github.com/gomodule/redigo v1.9.2        // Redis客户端
    github.com/go-sql-driver/mysql v1.9.3   // MySQL驱动
    github.com/xuri/excelize/v2 v2.9.1       // Excel处理
)
```

## 🌐 网络通信架构

### WebSocket连接管理
```go
// Session结构 - 核心连接管理
type Session struct {
    ID        int64           // 会话唯一标识
    RoomID    int             // 所属房间ID
    Ws        *websocket.Conn // WebSocket连接
    SendChan  chan []byte     // 发送消息通道
    RecvChan  chan []byte     // 接收消息通道
    
    // 连接状态管理
    connMutex    sync.RWMutex
    isConnected  bool
    readErrCount int
    lastReadTime time.Time
}
```

### 消息处理流程
1. **消息接收**: WebSocket → receiveMsgRun() → RecvChan
2. **消息解析**: bytesToMsg() → Protocol Buffer反序列化
3. **消息路由**: MsgHandleMap → 对应的Response函数
4. **消息发送**: Request函数 → SendChan → WebSocket

### HTTP API架构
- **Gin框架**: 高性能HTTP路由和中间件
- **频率限制**: 基于令牌桶算法的API限流
- **CORS支持**: 跨域资源共享配置
- **健康检查**: 系统状态监控端点

## 💾 数据存储架构

### 混合存储策略
```go
// ProductionHybridTaskManager - 生产级混合存储
type ProductionHybridTaskManager struct {
    memoryCache  *sync.Map      // 内存缓存层
    redisMgr     *db.RedisMgr   // Redis连接管理
    redisEnabled bool           // Redis可用性标志
    
    // 性能优化
    batchSize     int
    batchInterval time.Duration
    batchChan     chan *batchOperation
}
```

### 数据层次结构
1. **L1缓存**: 内存缓存 (sync.Map) - 毫秒级访问
2. **L2缓存**: Redis缓存 - 亚毫秒级访问
3. **持久化**: MySQL数据库 - 可靠存储

### 数据库配置
```json
{
  "database": {
    "dbuser": "root:root@tcp(127.0.0.1:3306)/mapbattle?charset=utf8&timeout=10s",
    "dblog": "root:root@tcp(127.0.0.1:3306)/live_log_001?charset=utf8&timeout=10s",
    "redis": "127.0.0.1:6379",
    "redisauth": "123456",
    "redisdb": 8,
    "maxdbconn": 360
  }
}
```

## 🎮 游戏逻辑架构

### 房间管理系统
```go
// LiveRoom - 游戏房间核心结构
type LiveRoom struct {
    RoomId         int                    // 房间ID
    RoomPlatformId string                // 平台房间ID
    Session        *network.Session      // 会话连接
    PlayState      int                   // 游戏状态
    Players        map[int]*PlayerInfo   // 玩家列表
    GameData       *GameRoomData         // 游戏数据
    
    // 游戏逻辑
    IsOver         bool                  // 游戏结束标志
    WinCondition   *WinCondition         // 胜利条件
    AllySystem     *AllySystem           // 联盟系统
}
```

### 玩家系统
- **玩家状态管理**: 在线状态、游戏状态、房间归属
- **技能系统**: 玩家技能、装备、属性计算
- **积分系统**: 击杀积分、排行榜、赛季统计

### 游戏状态机
1. **房间创建** → **等待玩家** → **游戏开始** → **游戏进行** → **游戏结束**
2. **状态同步**: 实时帧同步，确保所有客户端状态一致
3. **断线重连**: 支持玩家断线重连，状态恢复

## 📨 消息处理架构

### Protocol Buffer消息系统
```go
// 消息ID常量定义
const (
    ErrorCode = 101
    HeartBeat = 102
    LoginAuth = 1001
    AddPlayer = 2001
    CreateRoom = 3000
    GameStart = 3007
    // ... 更多消息类型
)

// 消息类型映射
var MessageC2STypeMap = map[int32]reflect.Type{
    LoginAuth: reflect.TypeOf(Message.LoginAuthC2S{}),
    AddPlayer: reflect.TypeOf(Message.AddPlayerC2S{}),
    // ... 完整映射表
}
```

### 消息处理映射
```go
// MsgHandleMap - 消息处理函数映射
var MsgHandleMap = map[int32]MessageHandler{
    LoginAuth: Response.LoginAuthResponse,
    AddPlayer: Response.AddPlayerResponse,
    CreateRoom: Response.CreateRoomResponse,
    // ... 完整处理映射
}
```

### 消息序列化/反序列化
1. **发送流程**: Go结构体 → Protocol Buffer → AES加密 → WebSocket
2. **接收流程**: WebSocket → AES解密 → Protocol Buffer → Go结构体
3. **错误处理**: 消息格式验证、版本兼容性检查

## 🔧 工具和代码生成

### pb_exporter工具
```bash
# 基本用法
./pb_exporter.exe

# 禁用Excel处理
./pb_exporter.exe --UnExcel

# 功能特性
- 自动编译.proto文件为Go代码
- 生成Request文件(S2C消息发送函数)
- 生成Response文件(C2S消息处理函数)
- 生成消息处理映射文件
- Excel配置转换为Protocol Buffer
```

### Excel到Protocol Buffer转换
1. **扫描Excel文件**: 自动发现配置文件
2. **生成.proto文件**: 根据Excel结构生成Protocol Buffer定义
3. **编译Go代码**: 生成对应的Go结构体
4. **数据转换**: Excel数据 → 二进制文件(.bytes)

### 自动化脚本
```batch
# Windows批处理脚本
generate_all.bat

# 功能包括:
- 编译pb_exporter工具
- 处理所有.proto文件
- 转换Excel配置
- 生成完整的消息处理代码
```

## 🛡️ 安全和性能

### 加密机制
```go
// AES-CBC加密 (客户端兼容)
func SafeEncrypt(data []byte) []byte {
    // 使用固定密钥和随机IV
    // 确保向后兼容性
}
```

### 频率限制
```go
// 令牌桶算法实现
type RateLimiter struct {
    requestsPerSecond float64
    burstCapacity     int
    tokens           float64
    lastUpdate       time.Time
}
```

### 连接池管理
- **最大连接数**: 3500并发连接
- **IP限制**: 单IP连接数限制
- **健康检查**: 定期清理无效连接
- **优雅关闭**: 支持服务器平滑重启

### 性能优化
1. **批处理消息**: 减少网络开销
2. **内存池**: 减少GC压力
3. **连接复用**: WebSocket长连接
4. **数据压缩**: 大消息自动压缩

## 📊 监控和日志

### 流量监控
```go
// TrafficMonitor - 实时流量监控
type TrafficMonitor struct {
    httpStats    *HTTPStats
    wsStats      *WebSocketStats
    alertManager *AlertManager
}
```

### 日志系统
- **分级日志**: Debug, Info, Warn, Error
- **文件轮转**: 自动日志文件管理
- **性能日志**: 关键操作耗时统计
- **错误追踪**: 详细的错误堆栈信息

### 性能指标
- **QPS**: 每秒查询数
- **延迟**: 请求响应时间
- **连接数**: 实时连接统计
- **内存使用**: 系统资源监控

## 🚀 部署和运维

### 配置管理
```json
{
  "serverid": 7,
  "wshost": "0.0.0.0:10027",
  "network": {
    "maxplayer": 3500,
    "msgfilter": true
  },
  "ratelimit": {
    "enabled": true,
    "http_requests_per_sec": 10,
    "ws_requests_per_sec": 5
  }
}
```

### 启动流程
1. **配置加载**: 读取config.json配置文件
2. **数据库连接**: 初始化MySQL和Redis连接
3. **服务启动**: 启动HTTP和WebSocket服务
4. **管理器初始化**: 初始化各种游戏管理器
5. **后台服务**: 启动定时任务和监控服务

### 健康检查
- **数据库连接**: 定期ping检查
- **Redis连接**: 连接池状态监控
- **内存使用**: 内存泄漏检测
- **goroutine监控**: 协程数量统计

## 📚 API接口文档

### WebSocket消息协议
```protobuf
// 登录认证消息
message LoginAuthC2S {
    string token = 1;
    string platform = 2;
    string version = 3;
}

message LoginAuthS2C {
    bool success = 1;
    string message = 2;
    PlayerInfo player = 3;
}
```

### HTTP API端点
- `GET /`: WebSocket连接升级
- `GET /health`: 健康检查
- `POST /api/room/create`: 创建房间
- `GET /api/stats`: 服务器统计信息

### 错误码定义
```go
const (
    ERROR_SUCCESS = 0
    ERROR_INVALID_TOKEN = 1001
    ERROR_ROOM_FULL = 3001
    ERROR_GAME_STARTED = 3002
)
```

## 🔍 关键组件详解

### 会话管理系统

#### Session生命周期
```go
// Session创建流程
func (sessionMgr *SessionMgr) GetNewSession(websocketConn *websocket.Conn, request *http.Request) *Session {
    // 1. 生成唯一会话ID
    newSessionID := atomic.AddInt64(&sessionIDCounter, 1)

    // 2. 创建会话对象
    session := &Session{
        ID:       newSessionID,
        Ws:       websocketConn,
        SendChan: make(chan []byte, sendChanSize),
        RecvChan: make(chan []byte, recvChanSize),
        // 连接状态管理字段
        isConnected:  false,
        readErrCount: 0,
        lastReadTime: time.Now(),
    }

    // 3. 注册到管理器
    sessionMgr.MapSession[newSessionID] = session
    return session
}
```

#### 连接状态管理
```go
// 连接健康检查
func (session *Session) IsConnectionHealthy() bool {
    // 检查基本连接状态
    if !session.isConnected || session.ShutDown {
        return false
    }

    // 检查长时间无数据
    timeSinceLastRead := time.Since(session.lastReadTime)
    if timeSinceLastRead > time.Minute*5 {
        return false
    }

    // 检查错误计数
    if session.readErrCount > 3 {
        return false
    }

    return true
}
```

### 消息批处理系统
```go
// MessageBatch - 消息批处理优化
type MessageBatch struct {
    messages    [][]byte
    maxSize     int
    maxInterval time.Duration
    timer       *time.Timer
    mutex       sync.Mutex
    session     *Session
}

// 批处理发送逻辑
func (batch *MessageBatch) AddMessage(data []byte) {
    batch.mutex.Lock()
    defer batch.mutex.Unlock()

    batch.messages = append(batch.messages, data)

    // 达到批处理大小或超时时发送
    if len(batch.messages) >= batch.maxSize {
        batch.flush()
    }
}
```

### 数据加载器系统
```go
// DataLoader - 统一数据加载接口
type DataLoader struct {
    dataMap map[string]interface{}
    mutex   sync.RWMutex
}

// 动态数据加载
func (loader *DataLoader) LoadData(fileName string, target interface{}) error {
    // 1. 读取bytes文件
    data, err := ioutil.ReadFile(fmt.Sprintf("bytes/%s.bytes", fileName))
    if err != nil {
        return err
    }

    // 2. Protocol Buffer反序列化
    return proto.Unmarshal(data, target.(proto.Message))
}
```

## 🎯 设计模式和最佳实践

### 单例模式应用
```go
// 管理器单例模式
var s_SessionMgr *SessionMgr = nil

func GetSessionMgr() *SessionMgr {
    if s_SessionMgr == nil {
        s_SessionMgr = &SessionMgr{
            MapSession: make(sessionMap),
            Lock:       &sync.RWMutex{},
        }
    }
    return s_SessionMgr
}
```

### 工厂模式
```go
// 消息工厂模式
func CreateMessage(msgID int32) (proto.Message, error) {
    msgType, exists := MessageC2STypeMap[msgID]
    if !exists {
        return nil, fmt.Errorf("unknown message ID: %d", msgID)
    }

    // 使用反射创建消息实例
    msgValue := reflect.New(msgType).Interface()
    return msgValue.(proto.Message), nil
}
```

### 观察者模式
```go
// 事件系统
type EventManager struct {
    listeners map[string][]EventListener
    mutex     sync.RWMutex
}

func (em *EventManager) Subscribe(event string, listener EventListener) {
    em.mutex.Lock()
    defer em.mutex.Unlock()
    em.listeners[event] = append(em.listeners[event], listener)
}
```

### 并发处理策略

#### Goroutine管理
```go
// 会话运行模式
func (session *Session) Run() {
    // 启动三个goroutine分别处理不同任务
    go session.sendMsgRun()    // 发送消息处理
    go session.logicRun()      // 逻辑处理
    session.receiveMsgRun()    // 接收消息处理(主goroutine)
}
```

#### 通道通信
```go
// 通道配置
const (
    sendChanSize  = 2000  // 发送通道缓冲区
    recvChanSize  = 2000  // 接收通道缓冲区
    broadcastQueueSize = 5000  // 广播队列大小
)
```

#### 锁策略
```go
// 读写锁优化
type Session struct {
    connMutex sync.RWMutex  // 连接状态读写锁
    // 读多写少的场景使用RWMutex
}

// 使用示例
func (session *Session) IsConnected() bool {
    session.connMutex.RLock()
    defer session.connMutex.RUnlock()
    return session.isConnected && !session.ShutDown
}
```

### 错误处理机制

#### 分层错误处理
```go
// 网络层错误处理
func (session *Session) receiveMsgRun() {
    defer func() {
        if panicInfo := recover(); panicInfo != nil {
            stackTrace := string(debug.Stack())
            core.LogError("会话接收异常:", session.ID, "异常:", panicInfo)
        }
        // 确保资源清理
        session.cleanup()
    }()
}
```

#### 错误分类和恢复
```go
// 错误类型分类处理
func (session *Session) handleReadError(err error) bool {
    if neterr, ok := err.(net.Error); ok && neterr.Timeout() {
        // 超时错误 - 可恢复
        return false
    }

    if err == io.EOF {
        // 连接关闭 - 不可恢复
        return true
    }

    if websocket.IsCloseError(err, websocket.CloseNormalClosure) {
        // WebSocket正常关闭 - 不可恢复
        return true
    }

    // 其他错误 - 增加错误计数
    return session.IncrementReadError()
}
```

## 🔧 配置参数详解

### 网络配置
```json
{
  "network": {
    "maxplayer": 3500,        // 最大玩家连接数
    "blackip": [],            // IP黑名单
    "whiteid": [],            // 用户白名单
    "msgfilter": true         // 消息过滤开关
  }
}
```

### 频率限制配置
```json
{
  "ratelimit": {
    "enabled": true,                    // 启用频率限制
    "http_requests_per_sec": 10,        // HTTP请求限制(每秒)
    "http_burst_capacity": 20,          // HTTP突发容量
    "ws_requests_per_sec": 5,           // WebSocket连接限制(每秒)
    "ws_burst_capacity": 10,            // WebSocket突发容量
    "cleanup_interval_sec": 300,        // 清理间隔(秒)
    "ip_whitelist": ["127.0.0.1"],     // IP白名单
    "ip_blacklist": []                  // IP黑名单
  }
}
```

### 数据库连接池配置
```json
{
  "database": {
    "maxdbconn": 360,                   // 最大数据库连接数
    "dbuser": "root:root@tcp(127.0.0.1:3306)/mapbattle?charset=utf8&timeout=10s",
    "dblog": "root:root@tcp(127.0.0.1:3306)/live_log_001?charset=utf8&timeout=10s"
  }
}
```

### 日志配置
```json
{
  "log": {
    "maxfilesize": 500,     // 单个日志文件最大大小(MB)
    "maxfilenum": 50,       // 最大日志文件数量
    "loglevel": 0,          // 日志级别(0=Debug, 1=Info, 2=Warn, 3=Error)
    "logconsole": true,     // 是否输出到控制台
    "sdk": true             // 是否启用SDK日志
  }
}
```

## 📈 性能优化方案

### 内存优化
```go
// 对象池模式减少GC压力
var messagePool = sync.Pool{
    New: func() interface{} {
        return make([]byte, 1024)
    },
}

// 使用对象池
func processMessage() {
    buffer := messagePool.Get().([]byte)
    defer messagePool.Put(buffer)
    // 使用buffer处理消息
}
```

### 网络优化
```go
// 连接复用和批处理
type ConnectionPool struct {
    maxConnections int
    sessions       map[int64]*Session
    batchProcessor *BatchProcessor
}

// 批处理消息发送
func (cp *ConnectionPool) BroadcastMessage(msg []byte) {
    cp.batchProcessor.AddBroadcast(msg)
}
```

### 数据库优化
```go
// 混合存储策略
func (manager *ProductionHybridTaskManager) Get(key string) (interface{}, error) {
    // L1: 内存缓存
    if value, ok := manager.memoryCache.Load(key); ok {
        return value, nil
    }

    // L2: Redis缓存
    if manager.redisEnabled {
        if value, err := manager.getFromRedis(key); err == nil {
            manager.memoryCache.Store(key, value)
            return value, nil
        }
    }

    // L3: 数据库
    return manager.getFromDatabase(key)
}
```

## 🛠️ 开发工具详解

### pb_exporter工具使用指南

#### 工具功能概述
```bash
# pb_exporter.exe - Protocol Buffer代码生成工具
# 主要功能:
# 1. 编译.proto文件为Go代码
# 2. 生成Request/Response处理函数
# 3. 生成消息映射文件
# 4. Excel配置转换为Protocol Buffer
# 5. 生成二进制配置数据文件
```

#### 使用方法
```bash
# 基本用法 - 处理所有功能
./pb_exporter.exe

# 禁用Excel处理 - 仅处理Protocol Buffer
./pb_exporter.exe --UnExcel

# 使用批处理脚本
generate_all.bat
```

#### 生成的文件结构
```
生成文件:
├── pb/Message/*.pb.go          # Protocol Buffer Go文件
├── pb/Data/*.pb.go             # Excel配置Go文件
├── game/Request/*.Request.go   # S2C消息发送函数
├── game/Response/*.Response.go # C2S消息处理函数
├── gate/MsgHandleMap.go        # 消息处理映射
├── gate/messagemap.go          # 消息类型映射
└── bytes/*.bytes               # 二进制配置数据
```

#### Request函数生成示例
```go
// 自动生成的Request函数
func LoginAuthRequest(session *network.Session, success bool, message string, playerInfo *Message.PlayerInfo) {
    loginauthS2C := &Message.LoginAuthS2C{
        Success: success,
        Message: message,
        Player:  playerInfo,
    }
    pbMsg := network.NewPBMsg(LoginAuth, loginauthS2C)
    session.SendPBMsg(pbMsg.MsgToBytes())
}
```

#### Response函数生成示例
```go
// 自动生成的Response函数
func LoginAuthResponse(session *network.Session, msg any) {
    loginauth := msg.(*Message.LoginAuthC2S)
    core.LogDebug("LoginAuthC2S:", loginauth)

    // TODO: 实现具体的业务逻辑
    // 验证token
    // 加载玩家数据
    // 发送响应消息

    // 示例：发送成功响应
    LoginAuthRequest(session, true, "登录成功", playerInfo)
}
```

### Excel配置转换系统

#### Excel文件格式要求
```excel
# 配置文件命名: ConfigName.xlsx
# 工作表结构:
Row 1: 字段名称 (id, name, value, ...)
Row 2: 字段类型 (int32, string, repeated int32, ...)
Row 3: 字段注释 (ID, 名称, 数值列表, ...)
Row 4: 导出标志 (1=导出, 0=不导出)
Row 5+: 实际数据
```

#### 支持的数据类型
```protobuf
// 基本类型
int32, int64, float, double, bool, string

// 重复字段
repeated int32, repeated string

// 嵌套消息
message NestedConfig {
    int32 id = 1;
    string name = 2;
}
```

#### 自动生成的.proto文件
```protobuf
// 从Excel自动生成
syntax = "proto3";
package PB.Data;
option go_package = "zone/pb/Data";

message BattleConfig {
    int32 id = 1;           // ID
    string name = 2;        // 名称
    repeated int32 values = 3; // 数值列表
}

message BattleConfigArray {
    repeated BattleConfig items = 1;
}
```

### 构建和部署脚本

#### Windows批处理脚本
```batch
@echo off
echo "=== MapBattle服务器构建脚本 ==="

echo "1. 编译pb_exporter工具..."
cd tools
go build -o pb_exporter.exe pb_exporter.go
if errorlevel 1 (
    echo "pb_exporter编译失败"
    pause
    exit /b 1
)

echo "2. 生成Protocol Buffer代码..."
pb_exporter.exe
if errorlevel 1 (
    echo "代码生成失败"
    pause
    exit /b 1
)

echo "3. 编译主程序..."
cd ..
go build -o main.exe main.go
if errorlevel 1 (
    echo "主程序编译失败"
    pause
    exit /b 1
)

echo "构建完成！"
pause
```

#### Linux部署脚本
```bash
#!/bin/bash
set -e

echo "=== MapBattle服务器部署脚本 ==="

# 1. 更新代码
echo "更新代码..."
git pull origin main

# 2. 编译工具
echo "编译pb_exporter工具..."
cd tools
go build -o pb_exporter pb_exporter.go

# 3. 生成代码
echo "生成Protocol Buffer代码..."
./pb_exporter

# 4. 编译主程序
echo "编译主程序..."
cd ..
go build -o zone_server main.go

# 5. 重启服务
echo "重启服务..."
systemctl restart mapbattle-server

echo "部署完成！"
```

## 🧪 测试和质量保证

### 单元测试
```go
// 会话管理测试
func TestSessionManagement(t *testing.T) {
    session := &network.Session{}
    session.SetConnected(true)

    assert.True(t, session.IsConnected())

    // 测试错误计数
    for i := 0; i < 3; i++ {
        shouldStop := session.IncrementReadError()
        assert.False(t, shouldStop)
    }

    // 达到阈值应该停止
    shouldStop := session.IncrementReadError()
    assert.True(t, shouldStop)
    assert.False(t, session.IsConnected())
}
```

### 性能测试
```go
// 数据库性能测试
func BenchmarkDatabaseOperations(b *testing.B) {
    manager := storage.NewProductionHybridTaskManager()

    b.ResetTimer()
    for i := 0; i < b.N; i++ {
        key := fmt.Sprintf("test_key_%d", i)
        value := fmt.Sprintf("test_value_%d", i)

        // 测试写入性能
        manager.Set(key, value, time.Hour)

        // 测试读取性能
        _, err := manager.Get(key)
        if err != nil {
            b.Fatal(err)
        }
    }
}
```

### 压力测试
```bash
# 使用专门的压力测试工具
cd StressTesting
./stress-testing.exe -players=1000 -duration=300

# 测试指标:
# - 并发连接数
# - 消息吞吐量
# - 响应延迟
# - 错误率
```

### 数据库性能测试
```bash
# 独立的数据库性能测试
cd DBTest
go run main.go -players=1000 -concurrency=50 -duration=60 -type=all

# 测试结果包括:
# - MySQL直接访问性能
# - Redis缓存性能
# - 混合存储性能对比
```

## 🚀 生产环境部署

### 系统要求
```yaml
# 最低配置
CPU: 4核心
内存: 8GB
存储: 100GB SSD
网络: 100Mbps

# 推荐配置
CPU: 8核心
内存: 16GB
存储: 500GB SSD
网络: 1Gbps
```

### 服务配置
```systemd
# /etc/systemd/system/mapbattle-server.service
[Unit]
Description=MapBattle Game Server
After=network.target mysql.service redis.service

[Service]
Type=simple
User=gameserver
WorkingDirectory=/opt/mapbattle
ExecStart=/opt/mapbattle/zone_server
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
```

### 监控配置
```yaml
# Prometheus监控配置
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'mapbattle-server'
    static_configs:
      - targets: ['localhost:8080']
    metrics_path: '/metrics'
    scrape_interval: 5s
```

### 日志管理
```bash
# 日志轮转配置 /etc/logrotate.d/mapbattle
/opt/mapbattle/log/*.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 gameserver gameserver
    postrotate
        systemctl reload mapbattle-server
    endscript
}
```

## 🔍 故障排查指南

### 常见问题和解决方案

#### WebSocket连接问题
```bash
# 问题: "repeated read on failed websocket connection"
# 原因: 在失败连接上重复读取
# 解决: 已优化错误处理逻辑，降低错误阈值

# 检查连接状态
curl -I http://localhost:10027/

# 查看WebSocket连接日志
tail -f log/$(date +%Y%m%d).log | grep "WebSocket"
```

#### 数据库连接问题
```bash
# 问题: 数据库连接超时
# 检查MySQL连接
mysql -h127.0.0.1 -uroot -p -e "SHOW PROCESSLIST;"

# 检查Redis连接
redis-cli -h 127.0.0.1 -p 6379 ping

# 查看连接池状态
curl http://localhost:8080/stats/database
```

#### 内存泄漏问题
```bash
# 监控内存使用
ps aux | grep zone_server

# 查看goroutine数量
curl http://localhost:8080/debug/pprof/goroutine?debug=1

# 生成内存profile
go tool pprof http://localhost:8080/debug/pprof/heap
```

### 性能调优建议

#### 系统级优化
```bash
# 增加文件描述符限制
echo "* soft nofile 65536" >> /etc/security/limits.conf
echo "* hard nofile 65536" >> /etc/security/limits.conf

# 优化网络参数
echo "net.core.somaxconn = 65536" >> /etc/sysctl.conf
echo "net.ipv4.tcp_max_syn_backlog = 65536" >> /etc/sysctl.conf
sysctl -p
```

#### 应用级优化
```go
// 调整GOMAXPROCS
runtime.GOMAXPROCS(runtime.NumCPU() * 2)

// 优化GC参数
debug.SetGCPercent(100)

// 调整通道缓冲区大小
const (
    sendChanSize = 4000  // 增加发送缓冲区
    recvChanSize = 4000  // 增加接收缓冲区
)
```

---

*本文档持续更新，最后更新时间: 2025-06-21*

## 📞 技术支持

如有技术问题，请参考以下资源：
- 📖 项目Wiki: [内部文档链接]
- 🐛 问题反馈: [Issue跟踪系统]
- 💬 技术讨论: [团队沟通渠道]
- 📧 紧急联系: [技术负责人邮箱]
