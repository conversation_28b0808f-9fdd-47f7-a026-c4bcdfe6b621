// Code generated by pb_exporter.go. DO NOT EDIT.
// Generated at: 2025-06-12 16:35:42

package Response

import (
	"zone/game/Request"
	"zone/game/mods"
	// "zone/lib/core"
	"zone/lib/network"
	"zone/pb/Message"
)

func PlayerOperateResponse(session *network.Session, msg any) {
	// 将 msg 转换为对应的 C2S 消息结构指针
	playeroperate := msg.(*Message.PlayerOperateC2S)
	// core.LogDebug("PlayerOperateC2S:", playeroperate)

	var room = mods.GetSessionRoomMgr().GetRoom(session)
	if room == nil {

		Request.ErrorCodeRequest(session, 0, 0, "查找房间失败，请重启直播间")
		return
	}
	room.DoneOp(playeroperate)

	// TODO: 实现具体的业务逻辑
	// 从 playeroperate 中提取字段值
	// 处理业务逻辑
	// 发送响应消息

	// 示例：创建响应消息
	// PlayerOperateRequire(session, /* 参数 */)
}
