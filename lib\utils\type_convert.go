package utils

import (
	"fmt"
	"math"
)

// SafeIntToInt32 安全地将int转换为int32，检查溢出
func SafeIntToInt32(value int) (int32, error) {
	if value > math.MaxInt32 {
		return 0, fmt.Errorf("value %d exceeds int32 max value %d", value, math.MaxInt32)
	}
	if value < math.MinInt32 {
		return 0, fmt.Errorf("value %d is less than int32 min value %d", value, math.MinInt32)
	}
	return int32(value), nil
}

// MustIntToInt32 将int转换为int32，溢出时panic（用于确定不会溢出的场景）
func MustIntToInt32(value int) int32 {
	result, err := SafeIntToInt32(value)
	if err != nil {
		panic(err)
	}
	return result
}

// SafeInt64ToInt32 安全地将int64转换为int32，检查溢出
func SafeInt64ToInt32(value int64) (int32, error) {
	if value > math.MaxInt32 {
		return 0, fmt.Errorf("value %d exceeds int32 max value %d", value, math.MaxInt32)
	}
	if value < math.MinInt32 {
		return 0, fmt.Errorf("value %d is less than int32 min value %d", value, math.MinInt32)
	}
	return int32(value), nil
}

// MustInt64ToInt32 将int64转换为int32，溢出时panic
func MustInt64ToInt32(value int64) int32 {
	result, err := SafeInt64ToInt32(value)
	if err != nil {
		panic(err)
	}
	return result
}

// SafeInt32ToInt 将int32转换为int（总是安全的）
func SafeInt32ToInt(value int32) int {
	return int(value)
}

// ClampIntToInt32 将int限制在int32范围内
func ClampIntToInt32(value int) int32 {
	if value > math.MaxInt32 {
		return math.MaxInt32
	}
	if value < math.MinInt32 {
		return math.MinInt32
	}
	return int32(value)
}

// ClampInt64ToInt32 将int64限制在int32范围内
func ClampInt64ToInt32(value int64) int32 {
	if value > math.MaxInt32 {
		return math.MaxInt32
	}
	if value < math.MinInt32 {
		return math.MinInt32
	}
	return int32(value)
}

// IsInt32Range 检查int值是否在int32范围内
func IsInt32Range(value int) bool {
	return value >= math.MinInt32 && value <= math.MaxInt32
}

// IsInt64InInt32Range 检查int64值是否在int32范围内
func IsInt64InInt32Range(value int64) bool {
	return value >= math.MinInt32 && value <= math.MaxInt32
}
