package models

import (
	"sync"
	"zone/lib/core"
	"zone/lib/db"
	"zone/lib/utils"
)

type AnchorDB struct {
	Id         int       `db:"id"`         //! ID
	OpenId     string    `db:"openid"`     //! 主播ID
	Icon       string    `db:"icon"`       //! 头像
	Name       string    `db:"name"`       //! 点赞
	PlayerList string    `db:"playerlist"` //! 玩家列表
	GiftValue  int64     `db:"giftvalue"`  //! 礼物总价值
	LiveTimes  int64     `db:"livetimes"`  //! 直播次数
	Time       int64     `db:"time"`       //! 最后一次开播
	CloseTime  int64     `db:"closetime"`  //! 关闭时间
	ScorePool  int64     `db:"scorePool"`
	MapPlayer  *sync.Map //! 玩家价值

	db.DataUpdate //! 数据库接口
}

// Decode 修复后的解码方法 - 正确处理JSON到sync.Map的转换
func (self *AnchorDB) Decode() {
	// 确保MapPlayer已初始化
	if self.MapPlayer == nil {
		self.MapPlayer = new(sync.Map)
	}

	// 处理空字符串情况
	if self.PlayerList == "" || self.PlayerList == "{}" {
		return
	}
	// 使用专用的JSONToSyncMapInt64函数进行反序列化
	if err := utils.JSONToSyncMapInt64(self.PlayerList, self.MapPlayer); err != nil {
		core.LogError("主播玩家列表解码失败", "Id:", self.Id, "Name:", self.Name, "PlayerList:", self.PlayerList, "Error:", err)
		// 重置为空，避免后续错误
		self.PlayerList = "{}"
	}

	// // 解析JSON到临时map
	// var tempMap map[string]interface{}
	// if err := json.Unmarshal([]byte(self.PlayerList), &tempMap); err != nil {
	// 	core.LogError("主播玩家列表解码失败", "OpenId:", self.OpenId, "PlayerList:", self.PlayerList, "Error:", err)
	// 	// 重置为空，避免后续错误
	// 	self.PlayerList = "{}"
	// 	return
	// }

	// // 清空现有数据
	// self.MapPlayer = new(sync.Map)

	// // 将数据加载到sync.Map
	// for key, value := range tempMap {
	// 	// 转换key为int64
	// 	liveId, err := strconv.ParseInt(key, 10, 64)
	// 	if err != nil {
	// 		core.LogError("无效的玩家ID", "Key:", key, "OpenId:", self.OpenId)
	// 		continue
	// 	}

	// 	// 安全的类型转换为int64
	// 	var playerValue int64
	// 	switch v := value.(type) {
	// 	case float64:
	// 		playerValue = int64(v)
	// 	case int64:
	// 		playerValue = v
	// 	case int:
	// 		playerValue = int64(v)
	// 	case int32:
	// 		playerValue = int64(v)
	// 	case string:
	// 		if val, err := strconv.ParseInt(v, 10, 64); err == nil {
	// 			playerValue = val
	// 		} else {
	// 			core.LogError("无效的玩家价值", "Value:", v, "OpenId:", self.OpenId)
	// 			continue
	// 		}
	// 	default:
	// 		core.LogError("不支持的玩家价值类型", "Value:", v, "Type:", fmt.Sprintf("%T", v), "OpenId:", self.OpenId)
	// 		continue
	// 	}

	// 	self.MapPlayer.Store(liveId, playerValue)
	// }
}

// Encode 修复后的编码方法 - 添加错误处理
func (self *AnchorDB) Encode() {
	if self.MapPlayer == nil {
		self.PlayerList = "{}"
		return
	}

	content, err := utils.SyncMapInt64ToJSON(self.MapPlayer)
	if err != nil {
		core.LogError("主播玩家列表编码失败", "OpenId:", self.OpenId, "Error:", err)
		self.PlayerList = "{}" // 设置为空JSON对象，避免数据库存储问题
		return
	}
	self.PlayerList = content
}

// SetPlayerValue 修复后的设置玩家价值方法 - 修正逻辑错误并添加同步
func (self *AnchorDB) SetPlayerValue(liveId int64, value int64) {
	// 确保MapPlayer已初始化
	if self.MapPlayer == nil {
		self.MapPlayer = new(sync.Map)
	}

	// 更新总礼物价值
	self.GiftValue += value

	// 获取当前值并更新 - 修正LoadOrStore的逻辑
	currentValue := int64(0)
	if data, exists := self.MapPlayer.Load(liveId); exists {
		if val, ok := data.(int64); ok {
			currentValue = val
		} else {
			core.LogError("玩家价值类型错误", "LiveId:", liveId, "Value:", data, "OpenId:", self.OpenId)
		}
	}

	// 设置新值
	newValue := currentValue + value
	self.MapPlayer.Store(liveId, newValue)

	// 立即同步到JSON字段
	self.Encode()
}

func (self *AnchorDB) OnSave(sql bool) {
	self.Encode()
	self.Update(sql, false)
}
