# ServerUrl配置管理系统（简化版）

## 概述

ServerUrl配置管理系统是一个简化的数据库驱动的配置管理解决方案，支持动态配置更新和基础缓存功能。

## 系统架构

### 数据层次结构
1. **内存缓存**: 管理器内存缓存 - 毫秒级访问
2. **持久化**: MySQL数据库 - 可靠存储

### 核心组件
- **WebSocketConfigManager**: 配置数据访问层，负责数据库CRUD操作
- **WebSocketConfigService**: 配置服务层，提供业务逻辑和缓存管理
- **WebSocketConfigAPI**: HTTP API层，提供RESTful接口

## 数据库表结构

### serverurl 配置表（简化版）
```sql
CREATE TABLE `serverurl` (
  `id` int(11) NOT NULL COMMENT '平台编号：0-抖音，1-快手，99-本地开发环境',
  `websocket_url` varchar(255) NOT NULL COMMENT 'WebSocket地址',
  `http_url` varchar(255) NOT NULL COMMENT 'HTTP地址',
  `use_encrypt` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用加密：0-否，1-是',
  PRIMARY KEY (`id`)
);
```

### 默认配置数据
```sql
INSERT INTO `serverurl` (`id`, `websocket_url`, `http_url`, `use_encrypt`) VALUES
(0, 'wss://ysyq.ziyewangluo.com', 'https://ysyq.ziyewangluo.com', 1),
(1, 'wss://kstest.ziyewangluo.com', 'https://ysyq.ziyewangluo.com', 1),
(99, 'ws://127.0.0.1:10027', 'http://127.0.0.1:10027', 0);
```

## API接口文档

### 基础URL
```
http://localhost:10027/api/websocket-config
```

### 1. 创建配置
```http
POST /api/websocket-config/
Content-Type: application/json

{
  "config_name": "WebSocket地址-新平台",
  "config_key": "websocket_url",
  "config_value": "wss://new-platform.example.com",
  "config_type": "string",
  "platform": 2,
  "environment": "production",
  "description": "新平台WebSocket地址",
  "created_by": "admin"
}
```

### 2. 获取所有配置
```http
GET /api/websocket-config/
```

### 3. 获取单个配置
```http
GET /api/websocket-config/websocket_url?platform=0&environment=production
```

### 4. 更新配置
```http
PUT /api/websocket-config/websocket_url?platform=0&environment=production
Content-Type: application/json

{
  "config_value": "wss://updated-url.example.com",
  "description": "更新后的WebSocket地址",
  "updated_by": "admin"
}
```

### 5. 删除配置（软删除）
```http
DELETE /api/websocket-config/websocket_url?platform=0&environment=production&deleted_by=admin
```

### 6. 获取配置历史
```http
GET /api/websocket-config/websocket_url/history?platform=0&environment=production&limit=20
```

### 7. 刷新缓存
```http
POST /api/websocket-config/cache/refresh?operator=admin
```

### 8. 获取缓存统计
```http
GET /api/websocket-config/cache/stats
```

### 9. 测试配置
```http
GET /api/websocket-config/test?platform=0&local=false
```

## 使用示例

### 在代码中获取配置
```go
// 获取配置服务实例
configService := storage.GetWebSocketConfigService()

// 获取WebSocket URL
wsURL := configService.GetWebSocketURL(0, false) // 平台0，非本地环境

// 获取HTTP URL
httpURL := configService.GetHTTPURL(0, false)

// 获取加密开关
useEncrypt := configService.GetUseEncrypt(false)

// 获取连接超时时间
timeout := configService.GetConnectionTimeout()

// 获取最大连接数
maxConn := configService.GetMaxConnections()
```

### 在WebSocketHandler中的使用
```go
func (w *WebSocketHandler) GetWebSocketUrl(c *gin.Context) {
    var req WebSocketUrlRequest
    // ... 参数解析 ...
    
    var response WebSocketUrlResponse
    response.WebSocket = w.ConfigService.GetWebSocketURL(req.Platform, req.Local)
    response.Http = w.ConfigService.GetHTTPURL(req.Platform, req.Local)
    response.UseEncrypt = w.ConfigService.GetUseEncrypt(req.Local)
    
    c.JSON(http.StatusOK, response)
}
```

## 配置类型说明

### 支持的配置类型
- **string**: 字符串类型，如URL地址
- **int**: 整数类型，如超时时间、连接数
- **bool**: 布尔类型，如开关配置
- **json**: JSON对象类型，用于复杂配置

### 平台类型
- **-1**: 全平台通用配置
- **0**: 抖音平台
- **1**: 快手平台
- **其他**: 自定义平台

### 环境类型
- **production**: 生产环境
- **test**: 测试环境
- **local**: 本地开发环境

## 缓存机制

### 两级缓存架构
1. **服务级缓存**: 预加载常用配置，提供最快访问速度
2. **管理器级缓存**: 按需加载所有配置，减少数据库访问

### 缓存更新策略
- 配置更新时自动清除相关缓存
- 支持手动刷新所有缓存
- 服务启动时自动预加载常用配置

## 版本控制和审计

### 版本管理
- 每次配置更新自动递增版本号
- 支持查看配置变更历史
- 记录操作者和操作时间

### 审计日志
- 所有配置变更都记录在历史表中
- 包含操作类型：CREATE、UPDATE、DELETE
- 支持按配置键查询变更历史

## 错误处理和容错

### 容错机制
- 配置服务初始化失败不影响服务器启动
- 获取配置失败时使用默认值或备用配置
- 数据库连接异常时使用缓存数据

### 错误日志
- 详细的错误日志记录
- 区分不同级别的日志（Debug、Info、Error）
- 便于问题排查和监控

## 性能优化

### 缓存优化
- 内存缓存减少数据库访问
- 预加载常用配置提高响应速度
- 批量操作减少数据库连接开销

### 数据库优化
- 合理的索引设计
- 软删除避免数据丢失
- 分页查询支持大数据量

## 部署和维护

### 初始化步骤
1. 执行SQL脚本创建数据表
2. 插入默认配置数据
3. 服务器启动时自动初始化配置服务

### 运维操作
- 通过API接口动态修改配置
- 监控缓存命中率和性能指标
- 定期清理过期的历史记录

### 备份和恢复
- 定期备份配置数据
- 支持配置导出和导入
- 历史记录支持配置回滚

## 注意事项

1. **配置键唯一性**: 同一平台和环境下配置键必须唯一
2. **类型安全**: 确保配置值与配置类型匹配
3. **缓存一致性**: 配置更新后及时刷新缓存
4. **权限控制**: 生产环境配置修改需要严格的权限控制
5. **监控告警**: 建议对配置服务进行监控和告警
