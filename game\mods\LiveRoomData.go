package mods

import (
	"sync"
)

type LiveRoomData struct {
	MapPlayer *sync.Map // key 为 live id 值为 *LivePlayer
	GiftValue int64     // 房间礼物总价值
	LikeCount int64     // 点赞数量

}

func NewLiveRoomData() *LiveRoomData {
	liveRoomData := &LiveRoomData{}
	liveRoomData.Init()
	return liveRoomData
}

func (self *LiveRoomData) Init() {
	self.MapPlayer = new(sync.Map)
	self.GiftValue = 0
	self.LikeCount = 0
}

func (self *LiveRoomData) AddPlayer(livePlayer *LivePlayer) bool {
	if self.HasPlayer(livePlayer.LiveId) {
		return false
	}
	self.MapPlayer.Store(livePlayer.LiveId, livePlayer)
	return true
}

func (self *LiveRoomData) RemovePlayer(liveId int64) {
	self.MapPlayer.Delete(liveId)
}

func (self *LiveRoomData) GetPlayer(liveId int64) *LivePlayer {
	if p, ok := self.MapPlayer.Load(liveId); ok {
		return p.(*LivePlayer)
	}
	return nil
}

// 数据访问方法（只读）
func (self *LiveRoomData) GetMapPlayer() *sync.Map {
	return self.MapPlayer
}

func (self *LiveRoomData) HasPlayer(uid int64) bool {
	// 通过sync.Map的Load方法检查键是否存在
	if _, ok := self.MapPlayer.Load(uid); ok {
		return true
	}
	return false
}
