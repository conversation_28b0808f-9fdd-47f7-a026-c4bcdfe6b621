// Code generated by pb_exporter.go. DO NOT EDIT.
package Request

import (
	"zone/lib/network"
	"zone/pb/Message"
)

func AddPlayerRequest(session *network.Session, player *Message.PlayerDTO, forcesit int32, oldblockid int32, smallblockindex int32, winstreakpool int32) {
	addplayerS2C := &Message.AddPlayerS2C{
		Player: player,
		ForceSit: int32(forcesit),
		OldBlockID: int32(oldblockid),
		SmallBlockIndex: int32(smallblockindex),
		WinStreakPool: int32(winstreakpool),
	}
	pbMsg := network.NewPBMsg(AddPlayer, addplayerS2C)
	session.SendPBMsg(pbMsg.MsgToBytes())
}
