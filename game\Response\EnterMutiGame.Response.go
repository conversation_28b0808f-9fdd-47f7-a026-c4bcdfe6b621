package Response

import (
	"zone/game/mods"
	"zone/lib/core"
	"zone/lib/network"
	"zone/pb/Message"
)

func EnterMutiGameResponse(session *network.Session, msg any) {
	// 将 msg 转换为对应的 C2S 消息结构指针
	_ = msg.(*Message.EnterMutiGameC2S) // 目前EnterMutiGameC2S为空结构，暂时不使用
	core.LogDebug("收到EnterMutiGameC2S消息:", "SessionID:", session.GetId(), "RoomID:", session.RoomID)

	// 检查session是否有效
	if session == nil || !session.IsConnected() {
		core.LogError("无效的session，无法处理EnterMutiGame请求", "SessionID:", session.GetId())
		return
	}

	// 获取多人游戏管理器
	multiGameMgr := mods.GetMultiGameMgr()
	if multiGameMgr == nil {
		core.LogError("无法获取多人游戏匹配管理器")
		return
	}

	// multiGameMgr.GetMultiGameById()
}
