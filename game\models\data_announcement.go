package models

import (
	"zone/lib/core"
	"zone/lib/db"
)

type AnnouncementDB struct {
	Id          int    `db:"id"`           //! ID
	Content     string `db:"content"`      //! 公告内容
	StartTime   int64  `db:"start_time"`   //! 公告生效起始时间
	EndTime     int64  `db:"end_time"`     //! 公告失效结束时间
	Status      int    `db:"status"`       //! 公告状态：0-禁用，1-启用
	Priority    int    `db:"priority"`     //! 公告优先级，数值越大优先级越高
	CreatedTime int64  `db:"created_time"` //! 创建时间
	UpdatedTime int64  `db:"updated_time"` //! 更新时间
	CreatedBy   string `db:"created_by"`   //! 创建者

	db.DataUpdate //! 数据库接口
}

// IsActive 检查公告是否在有效期内且状态为启用（时区安全）
func (a *AnnouncementDB) IsActive() bool {
	now := core.TimeServer().Unix()
	return a.Status == 1 &&
		now >= a.StartTime &&
		now <= a.EndTime
}

// IsExpired 检查公告是否已过期（时区安全）
func (a *AnnouncementDB) IsExpired() bool {
	return core.TimeServer().Unix() > a.EndTime
}

// IsTargetRoom 检查指定房间是否为目标房间
// 由于移除了目标房间功能，所有房间都是目标房间
func (a *AnnouncementDB) IsTargetRoom(roomID int) bool {
	return true
}
