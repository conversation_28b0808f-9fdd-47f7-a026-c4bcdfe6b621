package ratelimit

import (
	"net"
	"net/http"
	"strings"
	"time"
	"zone/lib/core"

	"github.com/gin-gonic/gin"
)

// RateLimitResponse 频率限制响应结构
type RateLimitResponse struct {
	Error   string `json:"error"`
	Message string `json:"message"`
	Code    int    `json:"code"`
	RetryAfter int `json:"retry_after"` // 建议重试时间（秒）
}

// GetRealClientIP 获取真实的客户端IP地址
// 考虑代理、负载均衡器等情况
func GetRealClientIP(c *gin.Context) string {
	// 检查多个可能的头部字段，按优先级排序
	headers := []string{
		"CF-Connecting-IP",     // Cloudflare
		"True-Client-IP",       // Akamai
		"X-Real-IP",           // Nginx proxy_pass
		"X-Forwarded-For",     // 标准代理头
		"X-Client-IP",         // Apache mod_proxy
		"X-Cluster-Client-IP", // 集群环境
	}

	for _, header := range headers {
		if ip := c.GetHeader(header); ip != "" {
			// X-Forwarded-For 可能包含多个IP，取第一个
			if header == "X-Forwarded-For" {
				ips := strings.Split(ip, ",")
				if len(ips) > 0 {
					ip = strings.TrimSpace(ips[0])
				}
			}
			
			// 验证IP格式
			if validIP := validateIP(ip); validIP != "" {
				return validIP
			}
		}
	}

	// 降级到RemoteAddr
	if ip, _, err := net.SplitHostPort(c.Request.RemoteAddr); err == nil {
		if validIP := validateIP(ip); validIP != "" {
			return validIP
		}
	}

	// 最后的降级选项
	return c.ClientIP()
}

// validateIP 验证IP地址格式
func validateIP(ip string) string {
	// 去除空格
	ip = strings.TrimSpace(ip)
	
	// 检查是否为空
	if ip == "" {
		return ""
	}
	
	// 解析IP地址
	parsedIP := net.ParseIP(ip)
	if parsedIP == nil {
		return ""
	}
	
	// 过滤掉本地和私有IP（可选，根据需求调整）
	if parsedIP.IsLoopback() || parsedIP.IsPrivate() {
		// 在开发环境中可能需要允许私有IP
		// 这里暂时返回，生产环境可以考虑过滤
		return ip
	}
	
	return ip
}

// HTTPRateLimitMiddleware 创建HTTP频率限制中间件
func HTTPRateLimitMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取客户端IP
		clientIP := GetRealClientIP(c)
		
		// 检查频率限制
		manager := GetGlobalRateLimiterManager()
		if !manager.CheckHTTPRateLimit(clientIP) {
			// 频率限制触发，返回429状态码
			core.LogDebug("HTTP请求频率限制触发", "IP:", clientIP, "路径:", c.Request.URL.Path)
			
			// 设置响应头
			c.Header("X-RateLimit-Limit", "10") // 可以从配置中获取
			c.Header("X-RateLimit-Remaining", "0")
			c.Header("X-RateLimit-Reset", getResetTime())
			c.Header("Retry-After", "60") // 建议60秒后重试
			
			// 返回JSON响应
			response := RateLimitResponse{
				Error:   "rate_limit_exceeded",
				Message: "请求频率过高，请稍后重试",
				Code:    429,
				RetryAfter: 60,
			}
			
			c.JSON(http.StatusTooManyRequests, response)
			c.Abort()
			return
		}
		
		// 继续处理请求
		c.Next()
	}
}

// WebSocketRateLimitMiddleware 创建WebSocket频率限制中间件
func WebSocketRateLimitMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 只对WebSocket升级请求进行检查
		if !isWebSocketUpgradeRequest(c.Request) {
			c.Next()
			return
		}
		
		// 获取客户端IP
		clientIP := GetRealClientIP(c)
		
		// 检查频率限制
		manager := GetGlobalRateLimiterManager()
		if !manager.CheckWebSocketRateLimit(clientIP) {
			// 频率限制触发
			core.LogDebug("WebSocket连接频率限制触发", "IP:", clientIP)
			
			// 对于WebSocket升级请求，返回HTTP错误
			c.Header("X-RateLimit-Limit", "5") // WebSocket连接限制通常更严格
			c.Header("X-RateLimit-Remaining", "0")
			c.Header("X-RateLimit-Reset", getResetTime())
			c.Header("Retry-After", "120") // WebSocket重试时间更长
			
			response := RateLimitResponse{
				Error:   "websocket_rate_limit_exceeded",
				Message: "WebSocket连接频率过高，请稍后重试",
				Code:    429,
				RetryAfter: 120,
			}
			
			c.JSON(http.StatusTooManyRequests, response)
			c.Abort()
			return
		}
		
		// 继续处理WebSocket升级
		c.Next()
	}
}

// isWebSocketUpgradeRequest 检查是否为WebSocket升级请求
func isWebSocketUpgradeRequest(req *http.Request) bool {
	// 检查Connection头
	connectionHeader := req.Header.Get("Connection")
	hasUpgradeConnection := strings.Contains(strings.ToLower(connectionHeader), "upgrade")
	
	// 检查Upgrade头
	upgradeHeader := req.Header.Get("Upgrade")
	hasWebSocketUpgrade := strings.EqualFold(upgradeHeader, "websocket")
	
	// 检查方法
	isGetMethod := req.Method == "GET"
	
	return hasUpgradeConnection && hasWebSocketUpgrade && isGetMethod
}

// getResetTime 获取重置时间戳
func getResetTime() string {
	// 返回下一分钟的时间戳
	nextMinute := time.Now().Add(time.Minute).Truncate(time.Minute)
	return nextMinute.Format(time.RFC3339)
}

// RateLimitStatsMiddleware 创建频率限制统计中间件（可选）
// 用于监控和调试
func RateLimitStatsMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 记录请求开始时间
		start := time.Now()
		
		// 获取客户端IP
		clientIP := GetRealClientIP(c)
		
		// 继续处理请求
		c.Next()
		
		// 记录请求完成时间和状态
		duration := time.Since(start)
		statusCode := c.Writer.Status()
		
		// 如果是429状态码，记录频率限制事件
		if statusCode == http.StatusTooManyRequests {
			core.LogInfo("频率限制事件",
				"IP:", clientIP,
				"路径:", c.Request.URL.Path,
				"方法:", c.Request.Method,
				"用户代理:", c.Request.UserAgent(),
				"处理时间:", duration,
			)
		}
	}
}

// CustomRateLimitMiddleware 创建自定义频率限制中间件
// 允许为特定路径设置不同的限制
func CustomRateLimitMiddleware(config *RateLimiterConfig) gin.HandlerFunc {
	// 为这个中间件创建专用的限制器
	limiter := NewIPRateLimiter(config)
	
	return func(c *gin.Context) {
		clientIP := GetRealClientIP(c)
		
		if !limiter.IsAllowed(clientIP) {
			core.LogDebug("自定义频率限制触发", "IP:", clientIP, "路径:", c.Request.URL.Path)
			
			response := RateLimitResponse{
				Error:   "custom_rate_limit_exceeded",
				Message: "请求频率过高，请稍后重试",
				Code:    429,
				RetryAfter: 60,
			}
			
			c.JSON(http.StatusTooManyRequests, response)
			c.Abort()
			return
		}
		
		c.Next()
	}
}

// IPWhitelistMiddleware 创建IP白名单中间件
// 与频率限制配合使用
func IPWhitelistMiddleware(whitelist []string) gin.HandlerFunc {
	whitelistMap := make(map[string]bool)
	for _, ip := range whitelist {
		whitelistMap[ip] = true
	}
	
	return func(c *gin.Context) {
		clientIP := GetRealClientIP(c)
		
		// 如果IP在白名单中，跳过后续的频率限制检查
		if whitelistMap[clientIP] {
			c.Set("ip_whitelisted", true)
		}
		
		c.Next()
	}
}

// IPBlacklistMiddleware 创建IP黑名单中间件
func IPBlacklistMiddleware(blacklist []string) gin.HandlerFunc {
	blacklistMap := make(map[string]bool)
	for _, ip := range blacklist {
		blacklistMap[ip] = true
	}
	
	return func(c *gin.Context) {
		clientIP := GetRealClientIP(c)
		
		// 如果IP在黑名单中，直接拒绝
		if blacklistMap[clientIP] {
			core.LogInfo("黑名单IP访问被拒绝", "IP:", clientIP)
			
			response := RateLimitResponse{
				Error:   "ip_blocked",
				Message: "访问被拒绝",
				Code:    403,
				RetryAfter: 0,
			}
			
			c.JSON(http.StatusForbidden, response)
			c.Abort()
			return
		}
		
		c.Next()
	}
}
