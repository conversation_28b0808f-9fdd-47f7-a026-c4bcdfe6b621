package manager

import (
	"sync"
	"zone/game/mods"
	"zone/lib/core"
)

type MultiGameMgr struct {
	GameNum   int32     //! 房间数量
	GameIdMax int32     //! 房间ID 最大序号
	GameMap   *sync.Map //! 房间管理器
}

var s_MultiGameMgr *MultiGameMgr = nil

func GetMultiGameMgr() *MultiGameMgr {
	if s_MultiGameMgr == nil {
		s_MultiGameMgr = new(MultiGameMgr)
		s_MultiGameMgr.GameNum = 0
		s_MultiGameMgr.GameIdMax = 1

		s_MultiGameMgr.GameMap = new(sync.Map)

		// 注册到mods包的管理器注册表
		mods.RegisterMultiGameManager(s_MultiGameMgr)
	}

	return s_MultiGameMgr
}

func (gameMgr *MultiGameMgr) NewMultiGame(roomA, roomB *RoomInfo) *mods.LiveMultiGame {
	defer func() {
		if r := recover(); r != nil {
			core.LogError("MultiGameMgr: NewMultiGame 发生错误", "Error:", r)
		}
	}()

	// 验证输入参数
	if roomA == nil || roomB == nil {
		core.LogError("MultiGameMgr: NewMultiGame 失败，房间信息为空")
		return nil
	}

	if roomA.Room == nil || roomB.Room == nil {
		core.LogError("MultiGameMgr: NewMultiGame 失败，房间对象为空")
		return nil
	}

	// 创建多人游戏
	game := mods.NewLiveMultiGame(gameMgr.GameIdMax)
	if game == nil {
		core.LogError("MultiGameMgr: 创建 LiveMultiGame 失败")
		return nil
	}

	// 添加房间到游戏
	err1 := game.AddRoom(roomA.Room)
	if err1 != nil {
		core.LogError("MultiGameMgr: 添加 RoomA 到多人游戏失败",
			"RoomID:", roomA.Room.RoomId,
			"Error:", err1)
		return nil
	}

	err2 := game.AddRoom(roomB.Room)
	if err2 != nil {
		core.LogError("MultiGameMgr: 添加 RoomB 到多人游戏失败",
			"RoomID:", roomB.Room.RoomId,
			"Error:", err2)
		return nil
	}

	// 将游戏添加到管理器
	gameMgr.GameMap.Store(gameMgr.GameIdMax, game)
	gameMgr.GameIdMax++

	core.LogInfo("MultiGameMgr: 多人游戏创建成功",
		"GameID:", game.GetGameID(),
		"RoomA_ID:", roomA.Room.RoomId,
		"RoomB_ID:", roomB.Room.RoomId)

	return game
}

func (gameMgr *MultiGameMgr) GetMultiGameById(gamerId int32) *mods.LiveMultiGame {
	if value, ok := gameMgr.GameMap.Load(gamerId); ok {
		liveMultiGame := value.(*mods.LiveMultiGame)
		return liveMultiGame
	}
	return nil
}
