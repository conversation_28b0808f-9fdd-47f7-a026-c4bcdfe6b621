package bytedance

import (
	"crypto/md5"
	"encoding/base64"
	"fmt"
	"sort"
	"strconv"
	"strings"
	"time"
	"zone/lib/core"
	"zone/lib/httpclient"
	"zone/lib/network"
	"zone/lib/utils"
)

const (
	LIVE_MSG_TYPE_COMMENT = "live_comment"  //! 直播评论
	LIVE_MSG_TYPE_GIFT    = "live_gift"     //! 直播礼物
	LIVE_MSG_TYPE_LIKE    = "live_like"     //! 直播点赞
	LIVE_MSG_TYPE_FANS    = "live_fansclub" //! 直播粉丝团

	LIVE_MSG_COMMENT_SECRET = "ayfsabc321"
	LIVE_MSG_GIFT_SECRET    = "ayfsabc321"
	LIVE_MSG_LIKE_SECRET    = "ayfsabc321"
	LIVE_MSG_FANS_SECRET    = "ayfsabc321"

	LIVE_APP_ID     = "tt7f0509b8e00ee3ce10"
	LIVE_SECRET     = "40a03203fb8743c6f4b6e6f136398060b55ffb12"
	LIVE_GRANT_TYPE = "client_credential"
)

var GetAccessTokenTimer *time.Timer = nil

func DoGetAccessToken() *time.Timer {

	timer := time.NewTimer(time.Second * time.Duration(3600))
	go func() {
		<-timer.C
		GetAccessToken()
	}()
	return timer
}

func GetAccessToken() {
	if network.IsTestServer {
		return
	}

	reqData := &JsTokenReq{
		AppId:     LIVE_APP_ID,
		Secret:    LIVE_SECRET,
		GrantType: LIVE_GRANT_TYPE,
	}

	// 使用通用HTTP请求函数
	opts, err := httpclient.CreateJSONRequest("POST", URLGetToken, reqData)
	if err != nil {
		core.LogError("创建GetAccessToken请求失败:", err)
		return
	}

	bodyData, err := httpclient.DoHTTPRequest(opts)
	if err != nil {
		core.LogError("执行GetAccessToken请求失败:", err)
		return
	}

	var ret JsTokenResult
	if err := httpclient.ParseJSONResponse(bodyData, &ret); err != nil {
		core.LogError("解析GetAccessToken响应失败:", err)
		return
	}

	//! 如果有错误
	if ret.ErrNo != 0 {
		core.LogError("getAccessToken Err No:", ret.ErrNo, ret.ErrTips)
	} else {
		network.AccessToken = ret.Data.AccessToken
		network.ExpiresIn = ret.Data.ExpiresIn
		core.LogInfo("GetAccessToken：", network.AccessToken, "network.ExpiresIn:", network.ExpiresIn, "获取时间：", utils.GetTimerString())
		if GetAccessTokenTimer != nil {
			GetAccessTokenTimer.Stop()
		}
		GetAccessTokenTimer = DoGetAccessToken()
		//! 获得成功
		return
	}
}

func LiveInfoReq(token string, msgType string) (*JsLiveInfoResInfo, error) {
	core.LogInfo("请求直播房间ID：", token, msgType, "network.AccessToken:", network.AccessToken)
	if network.IsTestServer {
		re := &JsLiveInfoResInfo{
			RoomId: int64(utils.HF_Atoi(token)),
			// 优化：使用strconv.FormatInt替代fmt.Sprintf，性能提升3-5倍
			AnchorOpenId: strconv.FormatInt(core.TimeServer().UnixMicro(), 10),
			AvatarUrl:    "https://p9-juejin.byteimg.com/tos-cn-i-k3u1fbpfcp/99999999999999999999999999999~tplv-k3u1fbpfcp-watermark.image",
			NickName:     "测试主播",
		}
		return re, nil
	}

	reqData := &JsLiveInfoReq{
		Token: token,
	}

	// 使用通用HTTP请求函数
	opts, err := httpclient.CreateJSONRequest("POST", URLLiveInfo, reqData)
	if err != nil {
		return nil, fmt.Errorf("创建LiveInfoReq请求失败: %w", err)
	}

	// 添加X-Token头部
	opts.Headers[httpclient.HeaderXToken] = network.AccessToken

	bodyData, err := httpclient.DoHTTPRequest(opts)
	if err != nil {
		return nil, fmt.Errorf("执行LiveInfoReq请求失败: %w", err)
	}

	core.LogInfo("GetRoomId:", string(bodyData))
	var ret JsLiveInfoRes
	if err := httpclient.ParseJSONResponse(bodyData, &ret); err != nil {
		return nil, fmt.Errorf("解析LiveInfoReq响应失败: %w", err)
	}

	//! 如果有错误
	if ret.ErrNo != 0 {
		core.LogError(ret.ErrMsg)
		if strings.Contains(ret.ErrMsg, "access token is expired") {
			// GetAccessToken()
		}
		return nil, fmt.Errorf("LiveInfoReq API错误: %s", ret.ErrMsg)
	}

	//! 获得成功
	return &ret.Data.Info, nil
}

func Reporting(roomId string, ackType int, msgid string, msgType string) {
	if network.IsTestServer {
		return
	}

	data := &JsReportingData{
		MsgId:      msgid,
		MsgType:    msgType,
		ClientTime: time.Now().UnixMilli(),
	}

	var datas = make([]*JsReportingData, 0)
	datas = append(datas, data)

	reqData := &JsReporting{
		RoomId:  roomId,
		AppId:   LIVE_APP_ID,
		AckType: ackType,
		Data:    utils.HF_JtoA(datas),
	}

	// 使用通用HTTP请求函数
	opts, err := httpclient.CreateJSONRequest("POST", URLReporting, reqData)
	if err != nil {
		core.LogError("创建Reporting请求失败:", err)
		return
	}

	// 添加access-token头部
	opts.Headers[httpclient.HeaderAccessToken] = network.AccessToken

	bodyData, err := httpclient.DoHTTPRequest(opts)
	if err != nil {
		core.LogError("执行Reporting请求失败:", err)
		return
	}

	var ret JsReportingRes
	if err := httpclient.ParseJSONResponse(bodyData, &ret); err != nil {
		core.LogError("解析Reporting响应失败:", err)
		return
	}

	//! 如果有错误
	if ret.ErrNo != 0 {
		core.LogError("Reporting Err No:", ret.ErrNo, ret.ErrMsg)
		return
	}
}

// LiveStart ! 开始直播
func LiveStart(roomId, msgType string) bool {
	core.LogInfo("开启直播，开始接受推送数据：", roomId, msgType, "network.AccessToken:", network.AccessToken)
	if network.IsTestServer {
		return true
	}

	reqData := &JsLiveStartReq{
		RoomId:  roomId,
		AppId:   LIVE_APP_ID,
		MsgType: msgType,
	}

	// 使用通用HTTP请求函数
	opts, err := httpclient.CreateJSONRequest("POST", URLLiveStart, reqData)
	if err != nil {
		core.LogError("创建LiveStart请求失败:", err)
		return false
	}

	// 添加access-token头部
	opts.Headers[httpclient.HeaderAccessToken] = network.AccessToken

	bodyData, err := httpclient.DoHTTPRequest(opts)
	if err != nil {
		core.LogError("执行LiveStart请求失败:", err)
		return false
	}

	var ret JsLiveStartRes
	if err := httpclient.ParseJSONResponse(bodyData, &ret); err != nil {
		core.LogError("解析LiveStart响应失败:", err)
		return false
	}

	//! 如果有错误
	if ret.ErrNo != 0 {
		core.LogError(ret.ErrMsg)
		if strings.Contains(ret.ErrMsg, "Invalid Access Token") {
			// GetAccessToken()
		}
		return false
	} else {
		//! 获得成功
		core.LogInfo("开启成功：", ret.LogId, ret.Data.TaskId)
		return true
	}
}

// LiveStop ! 停止直播
func LiveStop(roomId, msgType string) bool {
	core.LogInfo("停止直播，关闭接受推送数据：", roomId, msgType, "network.AccessToken:", network.AccessToken)
	if network.IsTestServer {
		return true
	}

	reqData := &JsLiveStartReq{
		RoomId:  roomId,
		AppId:   LIVE_APP_ID,
		MsgType: msgType,
	}

	// 使用通用HTTP请求函数
	opts, err := httpclient.CreateJSONRequest("POST", URLLiveStop, reqData)
	if err != nil {
		core.LogError("创建LiveStop请求失败:", err)
		return false
	}

	// 添加access-token头部
	opts.Headers[httpclient.HeaderAccessToken] = network.AccessToken

	bodyData, err := httpclient.DoHTTPRequest(opts)
	if err != nil {
		core.LogError("执行LiveStop请求失败:", err)
		return false
	}

	var ret JsLiveStartRes
	if err := httpclient.ParseJSONResponse(bodyData, &ret); err != nil {
		core.LogError("解析LiveStop响应失败:", err)
		return false
	}

	core.LogInfo(string(bodyData))
	//! 如果有错误
	if ret.ErrNo != 0 {
		switch ret.ErrNo {
		case 40023:
			core.LogError("getAccessToken Err No:", ret.ErrNo, "Required Parameters Are Absent")
		case 40022:
			core.LogError("getAccessToken Err No:", ret.ErrNo, "Invalid Access Token")
		case 10001:
			core.LogError("getAccessToken Err No:", ret.ErrNo, "Service Unavailable")
		case 5003019:
			core.LogError("getAccessToken Err No:", ret.ErrNo, "PushTaskCanNotStart")
		}
		return false
	} else {
		//! 获得成功
		return true
	}
}

// LiveStatus ! 直播状态
func LiveStatus(roomId, msgType string) bool {
	if network.IsTestServer {
		return true
	}

	params := map[string]string{
		"roomid":   roomId,
		"appid":    LIVE_APP_ID,
		"msg_type": msgType,
	}
	reqUrl := httpclient.BuildQueryURL(URLLiveStatus, params)

	// 使用通用HTTP请求函数
	opts := httpclient.HTTPRequestOptions{
		Method:  "GET",
		URL:     reqUrl,
		Headers: httpclient.CreateAuthHeaders(network.AccessToken),
	}

	bodyData, err := httpclient.DoHTTPRequest(opts)
	if err != nil {
		core.LogInfo("查询直播状态错误:", err)
		return false
	}

	var ret JsLiveTaskRes
	if err := httpclient.ParseJSONResponse(bodyData, &ret); err != nil {
		core.LogInfo("查询直播状态解析错误:", err, string(bodyData))
		return false
	}

	core.LogInfo("查询直播状态返回值：", string(bodyData))

	//! 如果有错误
	if ret.ErrNo != 0 {
		core.LogError(ret.ErrMsg)
		if strings.Contains(ret.ErrMsg, "status=5003019") {
			return true
		} else {
			return false
		}
	} else {
		core.LogInfo("直播状态：", msgType, ret.Data.Status)
		//! 获得成功
		return ret.Data.Status == 3
	}
}

// 返回是否需要翻页, 与获取到的数据
func GetFailData(roomId, msgType string, page, size int) (bool, []*JsFailData) {
	if network.IsTestServer {
		return false, nil
	}

	params := map[string]string{
		"roomid":   roomId,
		"appid":    LIVE_APP_ID,
		"msg_type": msgType,
		// 优化：使用strconv.Itoa替代fmt.Sprintf，性能提升3-5倍
		"page_num":  strconv.Itoa(page),
		"page_size": strconv.Itoa(size),
	}
	reqUrl := httpclient.BuildQueryURL(URLFailData, params)

	// 使用通用HTTP请求函数
	opts := httpclient.HTTPRequestOptions{
		Method:  "GET",
		URL:     reqUrl,
		Headers: httpclient.CreateAuthHeaders(network.AccessToken),
	}

	bodyData, err := httpclient.DoHTTPRequest(opts)
	if err != nil {
		core.LogError("执行GetFailData请求失败:", err)
		return false, nil
	}

	var ret JsFailDataRes
	if err := httpclient.ParseJSONResponse(bodyData, &ret); err != nil {
		core.LogError("解析GetFailData响应失败:", err)
		return false, nil
	}

	//! 如果有错误
	if ret.ErrNo != 0 {
		core.LogError("GetFailData Err No:", ret.ErrNo, ret.ErrMsg)
		return false, nil
	} else {
		dataSize := len(ret.Data.DataList)
		if dataSize > 0 {
			core.LogError("获取到失效订单:", string(bodyData))
			return dataSize == size, ret.Data.DataList
		} else {
			return false, nil
		}
	}
}

func GameInfo(roomid, anchorid string, roundid string, starttime int64, endtime int64, endstatus int, ranklists []*network.RankList) {
	if network.IsTestServer {
		return
	}

	reqData := &GameInfoRequest{
		AppID:        LIVE_APP_ID,
		RoundID:      roundid,
		AnchorOpenID: anchorid,
		RoomID:       roomid,
		StartTime:    starttime,
		EndTime:      endtime,
		EndStatus:    endstatus,
		RankLists:    ranklists,
	}

	content := utils.HF_JtoA(&reqData)
	authorization := GetByteAuthorization("POST", "/api/live_data/game/info", time.Now().Unix(), content)

	core.LogError("authorization:", authorization)
	if authorization == "" {
		core.LogError("GetByteAuthorization Error:")
		return
	}

	// 使用通用HTTP请求函数
	opts, err := httpclient.CreateJSONRequest("POST", URLGameInfo, reqData)
	if err != nil {
		core.LogError("创建GameInfo请求失败:", err)
		return
	}

	// 添加Byte-Authorization头部
	opts.Headers[httpclient.HeaderByteAuth] = authorization

	bodyData, err := httpclient.DoHTTPRequest(opts)
	if err != nil {
		core.LogError("执行GameInfo请求失败:", err)
		return
	}

	// 尝试解析成功响应
	var sucret GameInfoResSuc
	if err := httpclient.ParseJSONResponse(bodyData, &sucret); err == nil {
		if sucret.Code == 1 {
			return
		}
	}

	// 解析失败响应
	var failret GameInfoResFail
	if err := httpclient.ParseJSONResponse(bodyData, &failret); err != nil {
		core.LogError("解析GameInfo响应失败:", err)
		return
	}
	core.LogInfo(failret.ErrMsg)
}

func GetByteAuthorization(request, url string, time int64, body string) string {
	// core.LogInfo("失败订单查询：", msgType)
	nonce_str := httpclient.GenerateSecureRandomString()
	// 优化：使用strings.Builder替代fmt.Sprintf，性能提升2-3倍
	var contentBuilder strings.Builder
	contentBuilder.WriteString(request)
	contentBuilder.WriteByte('\n')
	contentBuilder.WriteString(url)
	contentBuilder.WriteByte('\n')
	contentBuilder.WriteString(strconv.FormatInt(time, 10))
	contentBuilder.WriteByte('\n')
	contentBuilder.WriteString(nonce_str)
	contentBuilder.WriteByte('\n')
	contentBuilder.WriteString(body)
	contentBuilder.WriteByte('\n')
	content := contentBuilder.String()
	core.LogInfo("GetByteAuthorization：", content)
	signature, err := network.SignSHA256RSA([]byte(content))
	if err != nil {
		core.LogError("SignSHA256RSA Err:", err)
		return ""
	}
	base64Str := base64.StdEncoding.EncodeToString(signature)

	// 优化：使用strings.Builder替代fmt.Sprintf，性能提升2-3倍
	var resultBuilder strings.Builder
	resultBuilder.WriteString("SHA256-RSA2048 appid=\"")
	resultBuilder.WriteString(LIVE_APP_ID)
	resultBuilder.WriteString("\",nonce_str=\"")
	resultBuilder.WriteString(nonce_str)
	resultBuilder.WriteString("\",timestamp=\"")
	resultBuilder.WriteString(strconv.FormatInt(time, 10))
	resultBuilder.WriteString("\",key_version=\"1\",signature=\"")
	resultBuilder.WriteString(base64Str)
	resultBuilder.WriteByte('"')
	result := resultBuilder.String()

	return result
}

// DouYinSignature !
/**
比如：
   header := map[string]string{
                "x-nonce-str": "123456",
                "x-timestamp": "456789",
                "x-roomid":    "268",
                "x-msg-type":  "live_gift",
        }
   bodyStr := "abc123你好"
   secret := "123abc"

rawData为：x-msg-type=live_gift&x-nonce-str=123456&x-roomid=268&x-timestamp=456789abc123你好123abc
signature为：PDcKhdlsrKEJif6uMKD2dw==
*/
func DouYinSignature(header map[string]string, bodyStr, secret string) string {
	keyList := make([]string, 0, 4)
	for key, _ := range header {
		keyList = append(keyList, key)
	}
	sort.Slice(keyList, func(i, j int) bool {
		return keyList[i] < keyList[j]
	})
	kvList := make([]string, 0, 4)
	for _, key := range keyList {
		kvList = append(kvList, key+"="+header[key])
	}
	urlParams := strings.Join(kvList, "&")
	rawData := urlParams + bodyStr + secret
	md5Result := md5.Sum([]byte(rawData))
	// core.LogInfo("DouYinSignature", rawData)
	return base64.StdEncoding.EncodeToString(md5Result[:])
}
