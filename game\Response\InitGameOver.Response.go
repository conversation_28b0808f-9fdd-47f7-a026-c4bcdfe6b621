// Code generated by pb_exporter.go. DO NOT EDIT.
// Generated at: 2025-06-12 16:35:42

package Response

import (
	"zone/game/Request"
	"zone/game/mods"
	"zone/lib/network"
)

func InitGameOverResponse(session *network.Session, msg any) {
	// 将 msg 转换为对应的 C2S 消息结构指针
	// initgameover := msg.(*Message.InitGameOverC2S)
	// core.LogDebug("InitGameOverC2S:", initgameover)

	var room = mods.GetSessionRoomMgr().GetRoom(session)
	if room == nil {

		Request.ErrorCodeRequest(session, 0, 0, "查找房间失败，请重启直播间")
		return
	}
	room.InitGameOver()
	// TODO: 实现具体的业务逻辑
	// 从 initgameover 中提取字段值
	// 处理业务逻辑
	// 发送响应消息

	// 示例：创建响应消息
	// InitGameOverRequire(session, /* 参数 */)
}
