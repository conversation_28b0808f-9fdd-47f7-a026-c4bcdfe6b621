package api

import (
	"zone/lib/core"

	"github.com/gin-gonic/gin"
)

// RegisterAnnouncementRoutes 注册公告相关的API路由
func RegisterAnnouncementRoutes(router *gin.Engine) {
	// 创建公告处理器
	announcementHandler := NewAnnouncementHandler()

	// 创建API路由组
	apiGroup := router.Group("/api")

	// 注意：频率限制中间件已在全局级别添加，无需重复添加
	// apiGroup.Use(ratelimit.HTTPRateLimitMiddleware()) // 已移除重复的中间件

	// 公告管理路由组
	announcementGroup := apiGroup.Group("/announcements")
	{
		// 创建公告
		announcementGroup.POST("", announcementHandler.CreateAnnouncement)

		// 获取公告列表
		announcementGroup.GET("", announcementHandler.GetAnnouncementList)

		// 获取单个公告
		announcementGroup.GET("/:id", announcementHandler.GetAnnouncement)

		// 更新公告
		announcementGroup.PUT("/:id", announcementHandler.UpdateAnnouncement)

		// 删除公告
		announcementGroup.DELETE("/:id", announcementHandler.DeleteAnnouncement)
	}
}

func RegisterServerUrlRoutes(router *gin.Engine, config *core.Config) {
	// 创建公告处理器
	serverUrlHandler := NewServerUrlHandler(config)
	router.POST("/api/serverurl", serverUrlHandler.GetServerUrl)
}

func RegisterScoreModifyRoutes(router *gin.Engine) {
	// 创建公告处理器
	scoreModifyHandler := NewScoreModifyHandler()

	// 创建API路由组
	apiGroup := router.Group("/api")

	// 注意：频率限制中间件已在全局级别添加，无需重复添加
	// apiGroup.Use(ratelimit.HTTPRateLimitMiddleware()) // 已移除重复的中间件

	// 公告管理路由组
	scoreGroup := apiGroup.Group("/Score/Modify")
	{
		// 更新公告
		scoreGroup.PUT("/:id", scoreModifyHandler.AddScore)
	}
}

// RegisterAllAPIRoutes 注册所有API路由
func RegisterAllAPIRoutes(router *gin.Engine, config *core.Config) {
	// 注册公告API路由
	RegisterAnnouncementRoutes(router)
	// 注册WebSocket URL获取路由
	RegisterServerUrlRoutes(router, config)
	// 注册ServerUrl配置管理路由
	RegisterServerUrlConfigRoutes(router)
	// 可以在这里添加其他API路由组
	// RegisterUserRoutes(router)
	// RegisterGameRoutes(router)
	RegisterScoreModifyRoutes(router)
}
