// Generated from Excel file: GiftConfig.xlsx

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.5.1
// source: giftconfig.proto

package Data

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Giftconfig 配置数据
type Giftconfig struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 编号
	Id int32 `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty"`
	// 平台ID
	GiftId []string `protobuf:"bytes,2,rep,name=GiftId,proto3" json:"GiftId,omitempty"`
	// 礼物名称
	Name []string `protobuf:"bytes,3,rep,name=name,proto3" json:"name,omitempty"`
	// 盲盒
	BlindBox int32 `protobuf:"varint,4,opt,name=BlindBox,proto3" json:"BlindBox,omitempty"`
	// 积分池积分
	GiftPoolScore int32 `protobuf:"varint,5,opt,name=GiftPoolScore,proto3" json:"GiftPoolScore,omitempty"`
	// 积分
	Score         int32 `protobuf:"varint,6,opt,name=Score,proto3" json:"Score,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Giftconfig) Reset() {
	*x = Giftconfig{}
	mi := &file_giftconfig_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Giftconfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Giftconfig) ProtoMessage() {}

func (x *Giftconfig) ProtoReflect() protoreflect.Message {
	mi := &file_giftconfig_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Giftconfig.ProtoReflect.Descriptor instead.
func (*Giftconfig) Descriptor() ([]byte, []int) {
	return file_giftconfig_proto_rawDescGZIP(), []int{0}
}

func (x *Giftconfig) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Giftconfig) GetGiftId() []string {
	if x != nil {
		return x.GiftId
	}
	return nil
}

func (x *Giftconfig) GetName() []string {
	if x != nil {
		return x.Name
	}
	return nil
}

func (x *Giftconfig) GetBlindBox() int32 {
	if x != nil {
		return x.BlindBox
	}
	return 0
}

func (x *Giftconfig) GetGiftPoolScore() int32 {
	if x != nil {
		return x.GiftPoolScore
	}
	return 0
}

func (x *Giftconfig) GetScore() int32 {
	if x != nil {
		return x.Score
	}
	return 0
}

// GiftconfigList 配置数据列表
type GiftconfigList struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Items         []*Giftconfig          `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GiftconfigList) Reset() {
	*x = GiftconfigList{}
	mi := &file_giftconfig_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GiftconfigList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GiftconfigList) ProtoMessage() {}

func (x *GiftconfigList) ProtoReflect() protoreflect.Message {
	mi := &file_giftconfig_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GiftconfigList.ProtoReflect.Descriptor instead.
func (*GiftconfigList) Descriptor() ([]byte, []int) {
	return file_giftconfig_proto_rawDescGZIP(), []int{1}
}

func (x *GiftconfigList) GetItems() []*Giftconfig {
	if x != nil {
		return x.Items
	}
	return nil
}

var File_giftconfig_proto protoreflect.FileDescriptor

const file_giftconfig_proto_rawDesc = "" +
	"\n" +
	"\x10giftconfig.proto\x12\x04Data\"\xa0\x01\n" +
	"\n" +
	"Giftconfig\x12\x0e\n" +
	"\x02Id\x18\x01 \x01(\x05R\x02Id\x12\x16\n" +
	"\x06GiftId\x18\x02 \x03(\tR\x06GiftId\x12\x12\n" +
	"\x04name\x18\x03 \x03(\tR\x04name\x12\x1a\n" +
	"\bBlindBox\x18\x04 \x01(\x05R\bBlindBox\x12$\n" +
	"\rGiftPoolScore\x18\x05 \x01(\x05R\rGiftPoolScore\x12\x14\n" +
	"\x05Score\x18\x06 \x01(\x05R\x05Score\"8\n" +
	"\x0eGiftconfigList\x12&\n" +
	"\x05items\x18\x01 \x03(\v2\x10.Data.GiftconfigR\x05itemsB\x0eZ\fzone/pb/Datab\x06proto3"

var (
	file_giftconfig_proto_rawDescOnce sync.Once
	file_giftconfig_proto_rawDescData []byte
)

func file_giftconfig_proto_rawDescGZIP() []byte {
	file_giftconfig_proto_rawDescOnce.Do(func() {
		file_giftconfig_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_giftconfig_proto_rawDesc), len(file_giftconfig_proto_rawDesc)))
	})
	return file_giftconfig_proto_rawDescData
}

var file_giftconfig_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_giftconfig_proto_goTypes = []any{
	(*Giftconfig)(nil),     // 0: Data.Giftconfig
	(*GiftconfigList)(nil), // 1: Data.GiftconfigList
}
var file_giftconfig_proto_depIdxs = []int32{
	0, // 0: Data.GiftconfigList.items:type_name -> Data.Giftconfig
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_giftconfig_proto_init() }
func file_giftconfig_proto_init() {
	if File_giftconfig_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_giftconfig_proto_rawDesc), len(file_giftconfig_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_giftconfig_proto_goTypes,
		DependencyIndexes: file_giftconfig_proto_depIdxs,
		MessageInfos:      file_giftconfig_proto_msgTypes,
	}.Build()
	File_giftconfig_proto = out.File
	file_giftconfig_proto_goTypes = nil
	file_giftconfig_proto_depIdxs = nil
}
