package manager

import (
	"database/sql"
	"fmt"
	"strconv"
	"strings"
	"zone/game/mods"
	"zone/lib"
	"zone/lib/db"
	"zone/lib/storage"
)

type ColumnConfig struct {
	TableName   string // 表名
	ColumnName  string // 字段名
	ColumnType  string // 数据类型（如 VARCHAR(255)）
	IsNullable  bool   // 是否允许NULL
	DefaultVal  string // 默认值
	AfterColumn string // 在哪个字段后添加（可选）
}

type SeasonMgr struct {
	Season int
}

var s_seasonmgr *SeasonMgr = nil

func GetSeasonMgr() *SeasonMgr {
	if s_seasonmgr == nil {
		s_seasonmgr = new(SeasonMgr)
		s_seasonmgr.Season = 0

		// 注册到mods包的管理器注册表
		mods.RegisterSeasonManager(s_seasonmgr)
	}

	return s_seasonmgr
}

func (self *SeasonMgr) GetSeason() {

	// query := `
	//     SELECT COLUMN_NAME
	//     FROM INFORMATION_SCHEMA.COLUMNS
	//     WHERE TABLE_SCHEMA = ?
	//       AND TABLE_NAME = ?
	//     ORDER BY ORDINAL_POSITION
	// `
	rows, err := db.GetDBMgr().DBUser.QueryAny("SHOW COLUMNS FROM season")
	if err != nil {
		return //nil, fmt.Errorf("SHOW查询失败: %v", err)
	}
	defer rows.Close()
	for rows.Next() {
		var (
			Field   string
			Type    string
			Null    string
			Key     string
			Default sql.NullString
			Extra   string
		)
		if err := rows.Scan(&Field, &Type, &Null, &Key, &Default, &Extra); err != nil {
			return
			// return nil, fmt.Errorf("解析列信息失败: %v", err)
		}
		if strings.HasPrefix(Field, "Season") {
			fmt.Printf("找到列: %s\n", Field)
			remaining := strings.TrimPrefix(Field, "Season")
			if remaining == "" {
				return
			}
			// 转换为整数
			num, err := strconv.Atoi(remaining)
			if err != nil {
				return
			}
			if num > self.Season {
				self.Season = num
			}
		}
	}

	if self.Season == 0 {
		self.AddSeason()
	}
}

func (self *SeasonMgr) AddSeason() {
	// 优化：使用strconv.Itoa替代fmt.Sprintf，性能提升3-5倍
	field := "Season" + strconv.Itoa(self.Season+1)
	// 优化：使用strings.Builder构建SQL语句，性能提升2-3倍
	var sqlBuilder strings.Builder
	sqlBuilder.WriteString("ALTER TABLE `")
	sqlBuilder.WriteString(storage.TABLE_Season)
	sqlBuilder.WriteString("` ADD COLUMN `")
	sqlBuilder.WriteString(field)
	sqlBuilder.WriteString("` bigint(20) NOT NULL DEFAULT 0;")
	sql := sqlBuilder.String()
	lib.GetSqlMgr().CheckAndAddField(storage.TABLE_Season, field, sql)
	self.Season += 1
}

func (self *SeasonMgr) CheckPlayer(liveid int, score int64) {
	var hasValue bool
	// 优化：使用strings.Builder构建SQL查询，性能提升2-3倍
	var queryBuilder strings.Builder
	queryBuilder.WriteString("SELECT EXISTS( SELECT 1 FROM ")
	queryBuilder.WriteString(storage.TABLE_Season)
	queryBuilder.WriteString(" WHERE id = ")
	queryBuilder.WriteString(strconv.Itoa(liveid))
	queryBuilder.WriteString(" ) AS has_value")
	query := queryBuilder.String()

	err := db.GetDBMgr().DBUser.GetDB().QueryRow(query).Scan(&hasValue)
	if err != nil {
		panic(err)
	}

	// 优化：使用strconv.Itoa替代fmt.Sprintf
	field := "Season" + strconv.Itoa(self.Season)

	if hasValue {
		// 优化：使用strings.Builder构建INSERT语句
		var insertBuilder strings.Builder
		insertBuilder.WriteString("INSERT INTO ")
		insertBuilder.WriteString(storage.TABLE_Season)
		insertBuilder.WriteString(" (id, ")
		insertBuilder.WriteString(field)
		insertBuilder.WriteString(") VALUES (")
		insertBuilder.WriteString(strconv.Itoa(liveid))
		insertBuilder.WriteString(", ")
		insertBuilder.WriteString(strconv.FormatInt(score, 10))
		insertBuilder.WriteString(") ON DUPLICATE KEY UPDATE ")
		insertBuilder.WriteString(field)
		insertBuilder.WriteString(" = VALUES(")
		insertBuilder.WriteString(field)
		insertBuilder.WriteString(");")
		query = insertBuilder.String()

		_, err := db.GetDBMgr().DBUser.GetDB().Exec(query)
		if err != nil {
			panic(err)
		}
	} else {
		// 优化：使用strings.Builder构建content字符串
		var contentBuilder strings.Builder
		for i := 1; i <= self.Season; i++ {
			if i > 1 {
				contentBuilder.WriteByte(',')
			}
			contentBuilder.WriteString("Season")
			contentBuilder.WriteString(strconv.Itoa(i))
			contentBuilder.WriteString(" = ")
			if i == self.Season {
				contentBuilder.WriteString(strconv.FormatInt(score, 10))
			} else {
				contentBuilder.WriteByte('0')
			}
		}
		content := contentBuilder.String()

		// 优化：使用strings.Builder构建最终INSERT语句
		var finalBuilder strings.Builder
		finalBuilder.WriteString("INSERT INTO ")
		finalBuilder.WriteString(storage.TABLE_Season)
		finalBuilder.WriteString(" SET id = ")
		finalBuilder.WriteString(strconv.Itoa(liveid))
		finalBuilder.WriteString(", ")
		finalBuilder.WriteString(content)
		finalBuilder.WriteString(" ON DUPLICATE KEY UPDATE ")
		finalBuilder.WriteString(field)
		finalBuilder.WriteString(" = VALUES(")
		finalBuilder.WriteString(field)
		finalBuilder.WriteString(");")
		query = finalBuilder.String()

		_, err := db.GetDBMgr().DBUser.GetDB().Exec(query)
		if err != nil {
			panic(err)
		}
	}
}

func (self *SeasonMgr) SetPlayer(liveid int, score int64) {
	// 优化：使用strconv.Itoa替代fmt.Sprintf
	field := "Season" + strconv.Itoa(self.Season)

	// 优化：使用strings.Builder构建SQL语句，性能提升2-3倍
	var queryBuilder strings.Builder
	queryBuilder.WriteString("INSERT INTO ")
	queryBuilder.WriteString(storage.TABLE_Season)
	queryBuilder.WriteString(" (id, ")
	queryBuilder.WriteString(field)
	queryBuilder.WriteString(") VALUES (")
	queryBuilder.WriteString(strconv.Itoa(liveid))
	queryBuilder.WriteString(", ")
	queryBuilder.WriteString(strconv.FormatInt(score, 10))
	queryBuilder.WriteString(") ON DUPLICATE KEY UPDATE ")
	queryBuilder.WriteString(field)
	queryBuilder.WriteString(" = VALUES(")
	queryBuilder.WriteString(field)
	queryBuilder.WriteString(");")
	query := queryBuilder.String()

	_, err := db.GetDBMgr().DBUser.GetDB().Exec(query)
	if err != nil {
		panic(err)
	}
}
