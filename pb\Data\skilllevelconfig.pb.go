// Generated from Excel file: SkillLevelConfig.xlsx

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.5.1
// source: skilllevelconfig.proto

package Data

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Skilllevelconfig 配置数据
type Skilllevelconfig struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 编号
	Id int32 `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty"`
	// 技能cd（最小单位倍数）
	Cd            int32 `protobuf:"varint,2,opt,name=cd,proto3" json:"cd,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Skilllevelconfig) Reset() {
	*x = Skilllevelconfig{}
	mi := &file_skilllevelconfig_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Skilllevelconfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Skilllevelconfig) ProtoMessage() {}

func (x *Skilllevelconfig) ProtoReflect() protoreflect.Message {
	mi := &file_skilllevelconfig_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Skilllevelconfig.ProtoReflect.Descriptor instead.
func (*Skilllevelconfig) Descriptor() ([]byte, []int) {
	return file_skilllevelconfig_proto_rawDescGZIP(), []int{0}
}

func (x *Skilllevelconfig) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Skilllevelconfig) GetCd() int32 {
	if x != nil {
		return x.Cd
	}
	return 0
}

// SkilllevelconfigList 配置数据列表
type SkilllevelconfigList struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Items         []*Skilllevelconfig    `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SkilllevelconfigList) Reset() {
	*x = SkilllevelconfigList{}
	mi := &file_skilllevelconfig_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SkilllevelconfigList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SkilllevelconfigList) ProtoMessage() {}

func (x *SkilllevelconfigList) ProtoReflect() protoreflect.Message {
	mi := &file_skilllevelconfig_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SkilllevelconfigList.ProtoReflect.Descriptor instead.
func (*SkilllevelconfigList) Descriptor() ([]byte, []int) {
	return file_skilllevelconfig_proto_rawDescGZIP(), []int{1}
}

func (x *SkilllevelconfigList) GetItems() []*Skilllevelconfig {
	if x != nil {
		return x.Items
	}
	return nil
}

var File_skilllevelconfig_proto protoreflect.FileDescriptor

const file_skilllevelconfig_proto_rawDesc = "" +
	"\n" +
	"\x16skilllevelconfig.proto\x12\x04Data\"2\n" +
	"\x10Skilllevelconfig\x12\x0e\n" +
	"\x02Id\x18\x01 \x01(\x05R\x02Id\x12\x0e\n" +
	"\x02cd\x18\x02 \x01(\x05R\x02cd\"D\n" +
	"\x14SkilllevelconfigList\x12,\n" +
	"\x05items\x18\x01 \x03(\v2\x16.Data.SkilllevelconfigR\x05itemsB\x0eZ\fzone/pb/Datab\x06proto3"

var (
	file_skilllevelconfig_proto_rawDescOnce sync.Once
	file_skilllevelconfig_proto_rawDescData []byte
)

func file_skilllevelconfig_proto_rawDescGZIP() []byte {
	file_skilllevelconfig_proto_rawDescOnce.Do(func() {
		file_skilllevelconfig_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_skilllevelconfig_proto_rawDesc), len(file_skilllevelconfig_proto_rawDesc)))
	})
	return file_skilllevelconfig_proto_rawDescData
}

var file_skilllevelconfig_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_skilllevelconfig_proto_goTypes = []any{
	(*Skilllevelconfig)(nil),     // 0: Data.Skilllevelconfig
	(*SkilllevelconfigList)(nil), // 1: Data.SkilllevelconfigList
}
var file_skilllevelconfig_proto_depIdxs = []int32{
	0, // 0: Data.SkilllevelconfigList.items:type_name -> Data.Skilllevelconfig
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_skilllevelconfig_proto_init() }
func file_skilllevelconfig_proto_init() {
	if File_skilllevelconfig_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_skilllevelconfig_proto_rawDesc), len(file_skilllevelconfig_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_skilllevelconfig_proto_goTypes,
		DependencyIndexes: file_skilllevelconfig_proto_depIdxs,
		MessageInfos:      file_skilllevelconfig_proto_msgTypes,
	}.Build()
	File_skilllevelconfig_proto = out.File
	file_skilllevelconfig_proto_goTypes = nil
	file_skilllevelconfig_proto_depIdxs = nil
}
