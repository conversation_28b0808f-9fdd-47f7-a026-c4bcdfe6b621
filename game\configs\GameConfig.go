package configs

import (
	"fmt"
	"sort"
	"zone/game/models"
	"zone/lib/config"
	"zone/lib/core"
	"zone/lib/db"
	"zone/lib/storage"
	"zone/lib/utils"
	"zone/pb/Data"
)

// 主播ID为自定义ID，从1开始一直加
type GameConfig struct {
	MapName       *utils.CountableSyncMap
	MapID         *utils.CountableSyncMap
	FamilyData    *utils.CountableSyncMap //! 全局数据
	FamilyRank    []*models.FamilyTopNode //! 排行
	MapFamilyRank *utils.CountableSyncMap
	MapTreasure   *utils.CountableSyncMap
}

// BlockNameConfig 保留用于兼容性，但使用PB数据填充
type BlockNameConfig struct {
	Id   int
	Name string
}

var s_GameConfig *GameConfig = nil

func GetGameConfig() *GameConfig {
	if s_GameConfig == nil {
		s_GameConfig = new(GameConfig)
	}

	return s_GameConfig
}

func (self *GameConfig) Init() {

	self.FamilyRank = make([]*models.FamilyTopNode, 0)
	self.FamilyData = new(utils.CountableSyncMap)
	self.MapFamilyRank = new(utils.CountableSyncMap)
	var familyInfo models.FamilyInfoDB
	sql := fmt.Sprintf("select * from `%s`", storage.TABLE_Family)
	res := db.GetDBMgr().DBUser.GetAllData(sql, &familyInfo)
	for i := 0; i < len(res); i++ {
		data := res[i].(*models.FamilyInfoDB)
		data.Init(storage.TABLE_Family, data, true)
		data.Decode()
		self.FamilyData.Store(data.Name, data)
	}

	self.MapName = new(utils.CountableSyncMap)
	self.MapID = new(utils.CountableSyncMap)

	self.MapTreasure = new(utils.CountableSyncMap)
	// 使用新的数据加载器加载TreasureConfig
	var treasureConfigs []*Data.Treasureconfig
	err := config.GetDataLoader().LoadData("treasureconfig", &treasureConfigs)
	if err != nil {
		core.LogError("加载TreasureConfig失败:", err)
		return
	}
	for _, config := range treasureConfigs {
		self.MapTreasure.Store(int(config.Id), int(config.Score))
	}
	core.LogInfo(fmt.Sprintf("加载TreasureConfig成功，共%d条记录", len(treasureConfigs)))
}

func (self *GameConfig) GetTreasureScore(id int) int {
	if config, ok := self.MapTreasure.Load(id); ok {
		return config.(int)
	}
	return 0
}

func (self *GameConfig) GetBlockNameConfigByID(id int) *BlockNameConfig {
	if config, ok := self.MapID.Load(id); ok {
		return config.(*BlockNameConfig)
	}
	return nil
}

func (self *GameConfig) GetBlockNameConfigByName(name string) *BlockNameConfig {
	if config, ok := self.MapName.Load(name); ok {
		return config.(*BlockNameConfig)
	}
	return nil
}

func (self *GameConfig) SetFamilyScore(name string, liveId int64, score int64) {
	value, ok := self.FamilyData.Load(name)
	if ok {
		var dbData = value.(*models.FamilyInfoDB)
		dbData.SetFamilyScore(liveId, score)
		node, ok := self.MapFamilyRank.Load(name)
		if ok == true {
			node.(*models.FamilyTopNode).Score += score
		} else {
			newNode := self.NewTopNode(name)
			if newNode != nil {
				newNode.Score = score
			}
		}
	}
}

func (self *GameConfig) GetFamilyData() *utils.CountableSyncMap {
	self.RankSort()
	return self.FamilyData
}

func (self *GameConfig) NewTopNode(name string) *models.FamilyTopNode {
	//! 查找不到，则新增一个
	newNode := &models.FamilyTopNode{
		Name:  name, //! 平台Id
		Rank:  9999, //! 排名
		Score: 0,
	}

	self.FamilyRank = append(self.FamilyRank, newNode)

	self.MapFamilyRank.Store(name, newNode)

	return newNode
}

func (self *GameConfig) Save() {
	self.FamilyData.Range(func(key, value interface{}) bool {
		data := (value).(*models.FamilyInfoDB)
		data.OnSave(true)
		return true
	})
}

func (self *GameConfig) RankSort() {
	sort.Sort(TopFamily(self.FamilyRank))
	for i := 0; i < len(self.FamilyRank); i++ {
		self.FamilyRank[i].Rank = i + 1
	}
}

type TopFamily []*models.FamilyTopNode

func (s TopFamily) Len() int      { return len(s) }
func (s TopFamily) Swap(i, j int) { s[i], s[j] = s[j], s[i] }
func (s TopFamily) Less(i, j int) bool {
	if s[i].Score > s[j].Score {
		return true
	}
	return false
}
