package Response

import (
	"zone/game/Request"
	"zone/game/mods"
	"zone/lib/core"
	"zone/lib/network"
	"zone/pb/Message"
)

func MatchMutiGameResponse(session *network.Session, msg any) {
	_ = msg.(*Message.MatchMutiGameC2S) // 目前EnterMutiGameC2S为空结构，暂时不使用
	core.LogDebug("收到EnterMutiGameC2S消息:", "SessionID:", session.GetId(), "RoomID:", session.RoomID)

	// 检查session是否有效
	if session == nil || !session.IsConnected() {
		core.LogError("无效的session，无法处理EnterMutiGame请求", "SessionID:", session.GetId())
		return
	}

	// 获取多人游戏管理器
	multiGameMatchMgr := mods.GetMultiGameMatchMgr()
	if multiGameMatchMgr == nil {
		core.LogError("无法获取多人游戏匹配管理器")
		return
	}

	multiGameMatchMgr.AddSession(session)

	// 发送成功响应
	Request.MatchMutiGameRequest(session)
}
