package manager

import (
	"sync"
	"zone/game/configs"
	"zone/game/mods"
	"zone/lib/core"
	"zone/lib/db"
	"zone/lib/network"
	"zone/lib/payload"
)

type GiftRecordMgr struct {
	MaxId          int64
	GiftRecordData *sync.Map //! 全局数据
}

var s_GiftRecordMgr *GiftRecordMgr = nil

func GetGiftRecordMgr() *GiftRecordMgr {
	if s_GiftRecordMgr == nil {
		s_GiftRecordMgr = new(GiftRecordMgr)
		s_GiftRecordMgr.GiftRecordData = new(sync.Map)
		s_GiftRecordMgr.MaxId = 0

		// 注册到mods包的管理器注册表
		mods.RegisterGiftRecordManager(s_GiftRecordMgr)
	}

	return s_GiftRecordMgr
}

func (grm *GiftRecordMgr) CreateRecord(roomId int32, anchorId string, data *payload.PayloadData) {

	if value, ok := grm.GiftRecordData.Load(data.MsgId); ok {
		record := value.(*db.SQL_GiftLog)
		record.Time = core.TimeServer().Unix()
		record.State = 0
		return
	}
	record := &db.SQL_GiftLog{
		Id:         0,
		PlayerName: data.Nickname,
		PlayerId:   data.OpenId,
		RoomId:     int64(roomId),
		AnchorId:   anchorId,
		MsgId:      data.MsgId,
		GiftName:   configs.GetGiftConfig().GetGiftName(data.GiftId),
		GiftCount:  data.GiftNum,
		Time:       core.TimeServer().UnixMilli(),
		State:      0,
		Platform:   network.PlatformName,
	}
	db.GetLogMgr().SqlGiftLog(record)

	grm.GiftRecordData.Store(record.MsgId, record)
}

func (grm *GiftRecordMgr) ExRecord(msgId string) {
	if value, ok := grm.GiftRecordData.Load(msgId); ok {
		record := value.(*db.SQL_GiftLog)
		record.Time = core.TimeServer().UnixMilli()
		record.State = 1
		db.GetLogMgr().SqlGiftLog(record)
		return
	}
}

func (grm *GiftRecordMgr) DoRecord(msgId string) {
	if value, ok := grm.GiftRecordData.LoadAndDelete(msgId); ok {
		record := value.(*db.SQL_GiftLog)
		record.Time = core.TimeServer().UnixMilli()
		record.State = 2
		db.GetLogMgr().SqlGiftLog(record)
		return
	}
}
