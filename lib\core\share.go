package core

import "time"

// ! 全局接口变量
var (
	ZoneApp IZoneApp = nil
	GameApp IGameApp = nil
	GateApp IGateApp = nil
)

// ! 对外接口，需保证获取全局句柄
func GetZoneApp() IZoneApp {
	return ZoneApp
}

func GetGameApp() IGameApp {
	return GameApp
}

func GetGateApp() IGateApp {
	return GateApp
}

// ! 系统时间的偏移
var ServerTimeOffset int64 = 0

func TimeServer() time.Time {
	return time.Unix(time.Now().Unix()+ServerTimeOffset, 0)
}
