// JoysGames copyrights this specification. No part of this specification may be
// reproduced in any form or means, without the prior written consent of JoysGames.
//
// This specification is preliminary and is subject to change at any time without notice.
// JoysGames assumes no responsibility for any errors contained herein.
//
// The above copyright notice and this permission notice shall be included
// in all copies or substantial portions of the Software.
// @package JGServer
// @copyright joysgames.cn All rights reserved.
// @version v1.0

package core

import (
	"reflect"
	"runtime/debug"
	"sync"
	"time"
)

var (
	modMgr     *ModMgr   // 单例
	modMgrOnce sync.Once // 单次加锁
)

// 单例(多线程安全)
func GetModMgr() *ModMgr {
	modMgrOnce.Do(func() {
		modMgr = &ModMgr{}
	})
	return modMgr
}

// 模块管理器
type ModMgr struct {
	ModLoader
}

// 初始化(务必调用)
func (that *ModMgr) Init() {
	that.ModLoader.Init()
}

// 初始化管理器
func (that *ModMgr) GetName() string {
	return reflect.TypeOf(that).Elem().Name()
}

// 默认运行更新
func (that *ModMgr) RunUpdateLoop() {
	defer func() {
		err := recover()
		if err != nil {
			LogError(err, string(debug.Stack()))
		}
	}()

	// 逻辑循环
	that.SetGoroutineID()
	that.Load()
	that.LaterLoad()
	that.OnStart()
	logicStart := time.Now()
	ticker := time.NewTicker(time.Second)
	for {
		select {
		case task := <-that.TaskChan:
			that.DoTask(task)
		case dt := <-ticker.C:
			that.Update(dt.Sub(logicStart).Milliseconds())
			logicStart = dt
		}
	}
}
