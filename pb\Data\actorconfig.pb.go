// Generated from Excel file: ActorConfig.xlsx

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.5.1
// source: actorconfig.proto

package Data

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Actorconfig 配置数据
type Actorconfig struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 编号
	Id int32 `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty"`
	// 击杀积分
	KillScore int32 `protobuf:"varint,2,opt,name=KillScore,proto3" json:"KillScore,omitempty"`
	// 积分池积分
	PoolScore     int32 `protobuf:"varint,3,opt,name=PoolScore,proto3" json:"PoolScore,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Actorconfig) Reset() {
	*x = Actorconfig{}
	mi := &file_actorconfig_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Actorconfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Actorconfig) ProtoMessage() {}

func (x *Actorconfig) ProtoReflect() protoreflect.Message {
	mi := &file_actorconfig_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Actorconfig.ProtoReflect.Descriptor instead.
func (*Actorconfig) Descriptor() ([]byte, []int) {
	return file_actorconfig_proto_rawDescGZIP(), []int{0}
}

func (x *Actorconfig) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Actorconfig) GetKillScore() int32 {
	if x != nil {
		return x.KillScore
	}
	return 0
}

func (x *Actorconfig) GetPoolScore() int32 {
	if x != nil {
		return x.PoolScore
	}
	return 0
}

// ActorconfigList 配置数据列表
type ActorconfigList struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Items         []*Actorconfig         `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ActorconfigList) Reset() {
	*x = ActorconfigList{}
	mi := &file_actorconfig_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ActorconfigList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActorconfigList) ProtoMessage() {}

func (x *ActorconfigList) ProtoReflect() protoreflect.Message {
	mi := &file_actorconfig_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActorconfigList.ProtoReflect.Descriptor instead.
func (*ActorconfigList) Descriptor() ([]byte, []int) {
	return file_actorconfig_proto_rawDescGZIP(), []int{1}
}

func (x *ActorconfigList) GetItems() []*Actorconfig {
	if x != nil {
		return x.Items
	}
	return nil
}

var File_actorconfig_proto protoreflect.FileDescriptor

const file_actorconfig_proto_rawDesc = "" +
	"\n" +
	"\x11actorconfig.proto\x12\x04Data\"Y\n" +
	"\vActorconfig\x12\x0e\n" +
	"\x02Id\x18\x01 \x01(\x05R\x02Id\x12\x1c\n" +
	"\tKillScore\x18\x02 \x01(\x05R\tKillScore\x12\x1c\n" +
	"\tPoolScore\x18\x03 \x01(\x05R\tPoolScore\":\n" +
	"\x0fActorconfigList\x12'\n" +
	"\x05items\x18\x01 \x03(\v2\x11.Data.ActorconfigR\x05itemsB\x0eZ\fzone/pb/Datab\x06proto3"

var (
	file_actorconfig_proto_rawDescOnce sync.Once
	file_actorconfig_proto_rawDescData []byte
)

func file_actorconfig_proto_rawDescGZIP() []byte {
	file_actorconfig_proto_rawDescOnce.Do(func() {
		file_actorconfig_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_actorconfig_proto_rawDesc), len(file_actorconfig_proto_rawDesc)))
	})
	return file_actorconfig_proto_rawDescData
}

var file_actorconfig_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_actorconfig_proto_goTypes = []any{
	(*Actorconfig)(nil),     // 0: Data.Actorconfig
	(*ActorconfigList)(nil), // 1: Data.ActorconfigList
}
var file_actorconfig_proto_depIdxs = []int32{
	0, // 0: Data.ActorconfigList.items:type_name -> Data.Actorconfig
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_actorconfig_proto_init() }
func file_actorconfig_proto_init() {
	if File_actorconfig_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_actorconfig_proto_rawDesc), len(file_actorconfig_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_actorconfig_proto_goTypes,
		DependencyIndexes: file_actorconfig_proto_depIdxs,
		MessageInfos:      file_actorconfig_proto_msgTypes,
	}.Build()
	File_actorconfig_proto = out.File
	file_actorconfig_proto_goTypes = nil
	file_actorconfig_proto_depIdxs = nil
}
