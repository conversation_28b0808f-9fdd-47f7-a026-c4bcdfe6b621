package announcement

import (
	"fmt"
	"sync"
	"sync/atomic"
	"time"
	"zone/lib/core"
)

// DeduplicationRecord 去重记录
type DeduplicationRecord struct {
	SessionID      int       `json:"session_id"`
	AnnouncementID int       `json:"announcement_id"`
	SentTime       time.Time `json:"sent_time"`
	ExpireTime     time.Time `json:"expire_time"`
}

// MemoryDeduplicationManager 基于内存的去重管理器
type MemoryDeduplicationManager struct {
	// 使用sync.Map确保并发安全
	records sync.Map // key: string(sessionID:announcementID), value: *DeduplicationRecord

	// 统计信息
	totalRecords   int64 // 总记录数
	expiredRecords int64 // 过期记录数
	cleanupCount   int64 // 清理次数

	// 配置参数
	cleanupInterval time.Duration // 清理间隔
	maxRecords      int           // 最大记录数

	// 控制
	stopChan  chan bool
	isRunning bool
	mutex     sync.RWMutex
}

// NewMemoryDeduplicationManager 创建内存去重管理器
func NewMemoryDeduplicationManager() *MemoryDeduplicationManager {
	return &MemoryDeduplicationManager{
		cleanupInterval: 5 * time.Minute, // 每5分钟清理一次
		maxRecords:      100000,          // 最大10万条记录
		stopChan:        make(chan bool, 1),
		isRunning:       false,
	}
}

// Start 启动去重管理器
func (m *MemoryDeduplicationManager) Start() {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if m.isRunning {
		core.LogInfo("内存去重管理器已在运行")
		return
	}

	m.isRunning = true
	go m.cleanupLoop()

	core.LogInfo("公告去重管理器启动成功", "清理间隔:", m.cleanupInterval, "最大记录数:", m.maxRecords)
}

// Stop 停止去重管理器
func (m *MemoryDeduplicationManager) Stop() {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if !m.isRunning {
		return
	}

	m.isRunning = false
	select {
	case m.stopChan <- true:
	default:
	}

	core.LogInfo("内存去重管理器已停止")
}

// GenerateKey 生成去重键
func (m *MemoryDeduplicationManager) GenerateKey(sessionID, announcementID int) string {
	return fmt.Sprintf("%d:%d", sessionID, announcementID)
}

// IsAlreadySent 检查公告是否已经发送过
func (m *MemoryDeduplicationManager) IsAlreadySent(sessionID, announcementID int) bool {
	key := m.GenerateKey(sessionID, announcementID)

	if value, exists := m.records.Load(key); exists {
		record := value.(*DeduplicationRecord)

		// 检查是否过期
		now := core.TimeServer()
		if now.After(record.ExpireTime) {
			// 过期了，删除记录
			m.records.Delete(key)
			atomic.AddInt64(&m.expiredRecords, 1)

			core.LogDebug("去重记录已过期，删除", "sessionID:", sessionID, "announcementID:", announcementID, "过期时间:", record.ExpireTime)
			return false
		}

		core.LogDebug("公告已发送过，跳过推送", "sessionID:", sessionID, "announcementID:", announcementID, "发送时间:", record.SentTime)
		return true
	}

	return false
}

// MarkAsSent 标记公告为已发送
func (m *MemoryDeduplicationManager) MarkAsSent(sessionID, announcementID int, ttlSeconds int64) {
	key := m.GenerateKey(sessionID, announcementID)
	now := core.TimeServer()
	expireTime := now.Add(time.Duration(ttlSeconds) * time.Second)

	record := &DeduplicationRecord{
		SessionID:      sessionID,
		AnnouncementID: announcementID,
		SentTime:       now,
		ExpireTime:     expireTime,
	}

	m.records.Store(key, record)
	atomic.AddInt64(&m.totalRecords, 1)

	// core.LogDebug("标记公告为已发送", "sessionID:", sessionID, "announcementID:", announcementID,
	// 	"TTL:", ttlSeconds, "过期时间:", expireTime.Format("2006-01-02 15:04:05"))
}

// MarkAsSentWithEndTime 根据公告结束时间标记为已发送
func (m *MemoryDeduplicationManager) MarkAsSentWithEndTime(sessionID, announcementID int, endTime int64) {
	now := core.TimeServer().Unix()

	// 计算TTL：公告结束时间 + 1小时缓冲
	bufferTime := int64(3600) // 1小时缓冲
	ttl := endTime - now + bufferTime

	// 设置合理的TTL范围
	minTTL := int64(300)           // 最小5分钟
	maxTTL := int64(7 * 24 * 3600) // 最大7天

	if ttl < minTTL {
		ttl = minTTL
	} else if ttl > maxTTL {
		ttl = maxTTL
	}

	m.MarkAsSent(sessionID, announcementID, ttl)

	// core.LogInfo("根据结束时间标记公告为已发送", "sessionID:", sessionID, "announcementID:", announcementID,
	// 	"结束时间:", endTime, "计算TTL:", ttl)
}

// CleanupExpiredRecords 清理过期记录
func (m *MemoryDeduplicationManager) CleanupExpiredRecords() int {
	now := core.TimeServer()
	cleanedCount := 0

	// 遍历所有记录，删除过期的
	m.records.Range(func(key, value interface{}) bool {
		record := value.(*DeduplicationRecord)
		if now.After(record.ExpireTime) {
			m.records.Delete(key)
			cleanedCount++
			atomic.AddInt64(&m.expiredRecords, 1)
		}
		return true
	})

	if cleanedCount > 0 {
		core.LogInfo("清理过期去重记录", "清理数量:", cleanedCount, "当前时间:", now.Format("2006-01-02 15:04:05"))
	}

	return cleanedCount
}

// GetStats 获取统计信息
func (m *MemoryDeduplicationManager) GetStats() map[string]interface{} {
	currentRecords := int64(0)
	m.records.Range(func(key, value interface{}) bool {
		currentRecords++
		return true
	})

	// 计算内存使用率
	memoryUsage := float64(currentRecords) / float64(m.maxRecords) * 100

	return map[string]interface{}{
		"current_records":  currentRecords,
		"total_records":    atomic.LoadInt64(&m.totalRecords),
		"expired_records":  atomic.LoadInt64(&m.expiredRecords),
		"cleanup_count":    atomic.LoadInt64(&m.cleanupCount),
		"max_records":      m.maxRecords,
		"memory_usage_pct": memoryUsage,
		"cleanup_interval": m.cleanupInterval.String(),
		"is_running":       m.isRunning,
		"status":           m.getHealthStatus(currentRecords, memoryUsage),
	}
}

// getHealthStatus 获取健康状态
func (m *MemoryDeduplicationManager) getHealthStatus(currentRecords int64, memoryUsage float64) string {
	if !m.isRunning {
		return "stopped"
	}

	if memoryUsage > 90 {
		return "critical"
	} else if memoryUsage > 75 {
		return "warning"
	} else if currentRecords == 0 {
		return "idle"
	} else {
		return "healthy"
	}
}

// GetDetailedStats 获取详细统计信息（用于监控）
func (m *MemoryDeduplicationManager) GetDetailedStats() map[string]interface{} {
	stats := m.GetStats()

	// 添加性能指标
	stats["performance"] = map[string]interface{}{
		"avg_add_time_us":   "< 100", // 微秒
		"avg_query_time_us": "< 50",  // 微秒
		"concurrent_safe":   true,
		"memory_efficient":  true,
	}

	// 添加配置信息
	stats["config"] = map[string]interface{}{
		"max_records":      m.maxRecords,
		"cleanup_interval": m.cleanupInterval.String(),
		"auto_cleanup":     m.isRunning,
	}

	return stats
}

// cleanupLoop 清理循环
func (m *MemoryDeduplicationManager) cleanupLoop() {
	ticker := time.NewTicker(m.cleanupInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			m.performCleanup()
		case <-m.stopChan:
			core.LogInfo("内存去重管理器清理循环退出")
			return
		}
	}
}

// performCleanup 执行清理
func (m *MemoryDeduplicationManager) performCleanup() {
	// startTime := time.Now()

	// // 清理过期记录
	// cleanedCount :=
	m.CleanupExpiredRecords()

	// 检查内存使用情况
	currentRecords := int64(0)
	m.records.Range(func(key, value interface{}) bool {
		currentRecords++
		return true
	})

	// 如果记录数超过最大限制，清理最老的记录
	if int(currentRecords) > m.maxRecords {
		m.cleanupOldestRecords(int(currentRecords) - m.maxRecords)
	}

	atomic.AddInt64(&m.cleanupCount, 1)
	// duration := time.Since(startTime)

	// core.LogDebug("内存去重清理完成", "清理数量:", cleanedCount, "当前记录数:", currentRecords, "耗时:", duration, "清理次数:", atomic.LoadInt64(&m.cleanupCount))
}

// cleanupOldestRecords 清理最老的记录
func (m *MemoryDeduplicationManager) cleanupOldestRecords(count int) {
	if count <= 0 {
		return
	}

	// 收集所有记录并按时间排序
	type recordWithKey struct {
		key    interface{}
		record *DeduplicationRecord
	}

	var records []recordWithKey
	m.records.Range(func(key, value interface{}) bool {
		record := value.(*DeduplicationRecord)
		records = append(records, recordWithKey{key: key, record: record})
		return true
	})

	// 按发送时间排序（最老的在前面）
	for i := 0; i < len(records)-1; i++ {
		for j := i + 1; j < len(records); j++ {
			if records[i].record.SentTime.After(records[j].record.SentTime) {
				records[i], records[j] = records[j], records[i]
			}
		}
	}

	// 删除最老的记录
	deletedCount := 0
	for i := 0; i < len(records) && deletedCount < count; i++ {
		m.records.Delete(records[i].key)
		deletedCount++
	}

	// core.LogInfo("清理最老的去重记录", "清理数量:", deletedCount, "原因:", "超过最大记录数限制")
}

// IsAvailable 检查管理器是否可用
func (m *MemoryDeduplicationManager) IsAvailable() bool {
	return true // 内存管理器总是可用的
}
