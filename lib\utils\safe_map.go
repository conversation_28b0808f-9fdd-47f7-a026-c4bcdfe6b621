package utils

import (
	"encoding/json"
	"sync"
)

type SafeMapInt struct {
	locker *sync.RWMutex
	data   map[int]interface{}
}

func NewSafeMapInt() *SafeMapInt {
	return &SafeMapInt{
		locker: new(sync.RWMutex),
		data:   make(map[int]interface{}),
	}
}

func (self *SafeMapInt) Create() {
	self.locker = new(sync.RWMutex)
	self.data = make(map[int]interface{})
}

func (self *SafeMapInt) Load(key int) (interface{}, bool) {
	self.locker.RLock()
	defer self.locker.RUnlock()
	if v, ok := self.data[key]; ok {
		return v, ok
	}

	return nil, false
}

func (self *SafeMapInt) Store(key int, value interface{}) {
	self.locker.Lock()
	defer self.locker.Unlock()

	self.data[key] = value
}

func (self *SafeMapInt) Range(f func(key int, value interface{}) bool) {
	self.locker.RLock()
	defer self.locker.RUnlock()

	for k, v := range self.data {
		if !f(k, v) {
			break
		}
	}
}

func (self *SafeMapInt) Delete(key int) {
	self.locker.Lock()
	defer self.locker.Unlock()

	delete(self.data, key)
}

func (self *SafeMapInt) Clear() {
	self.locker.Lock()
	defer self.locker.Unlock()

	self.data = make(map[int]interface{})
}

func (self *SafeMapInt) Decode(data string) bool {
	self.locker.Lock()
	defer self.locker.Unlock()

	err := json.Unmarshal([]byte(data), &self.data)
	if err != nil {
		return false
	}
	return true
}

func (self *SafeMapInt) Encode() ([]byte, error) {
	self.locker.RLock()
	defer self.locker.RUnlock()

	return json.Marshal(self.data)
}

func (self *SafeMapInt) Len() int {
	self.locker.RLock()
	defer self.locker.RUnlock()
	return len(self.data)
}

type SafeMapInt32 struct {
	locker *sync.RWMutex
	data   map[int32]interface{}
}

func NewSafeMapInt32() *SafeMapInt32 {
	return &SafeMapInt32{
		locker: new(sync.RWMutex),
		data:   make(map[int32]interface{}),
	}
}

func (self *SafeMapInt32) Create() {
	self.locker = new(sync.RWMutex)
	self.data = make(map[int32]interface{})
}

func (self *SafeMapInt32) Load(key int32) (interface{}, bool) {
	self.locker.RLock()
	defer self.locker.RUnlock()
	if v, ok := self.data[key]; ok {
		return v, ok
	}

	return nil, false
}

func (self *SafeMapInt32) Store(key int32, value interface{}) {
	self.locker.Lock()
	defer self.locker.Unlock()

	self.data[key] = value
}

func (self *SafeMapInt32) Range(f func(key int32, value interface{}) bool) {
	self.locker.RLock()
	defer self.locker.RUnlock()

	for k, v := range self.data {
		if !f(k, v) {
			break
		}
	}
}

func (self *SafeMapInt32) Delete(key int32) {
	self.locker.Lock()
	defer self.locker.Unlock()

	delete(self.data, key)
}

func (self *SafeMapInt32) Clear() {
	self.locker.Lock()
	defer self.locker.Unlock()

	self.data = make(map[int32]interface{})
}

func (self *SafeMapInt32) Decode(data string) bool {
	self.locker.Lock()
	defer self.locker.Unlock()

	err := json.Unmarshal([]byte(data), &self.data)
	if err != nil {
		return false
	}
	return true
}

func (self *SafeMapInt32) Encode() ([]byte, error) {
	self.locker.RLock()
	defer self.locker.RUnlock()

	return json.Marshal(self.data)
}

func (self *SafeMapInt32) Len() int {
	self.locker.RLock()
	defer self.locker.RUnlock()
	return len(self.data)
}

type SafeMapInt64 struct {
	locker *sync.RWMutex
	data   map[int64]interface{}
}

func NewSafeMapInt64() *SafeMapInt64 {
	return &SafeMapInt64{
		locker: new(sync.RWMutex),
		data:   make(map[int64]interface{}),
	}
}

func (self *SafeMapInt64) Load(key int64) (interface{}, bool) {
	if v, ok := self.data[key]; ok {
		return v, ok
	}

	return nil, false
}

func (self *SafeMapInt64) Store(key int64, value interface{}) {
	self.locker.Lock()
	defer self.locker.Unlock()

	self.data[key] = value
}

func (self *SafeMapInt64) Range(f func(key, value interface{}) bool) {
	self.locker.RLock()
	defer self.locker.RUnlock()

	for k, v := range self.data {
		if !f(k, v) {
			break
		}
	}
}

func (self *SafeMapInt64) Len() int {
	self.locker.RLock()
	defer self.locker.RUnlock()
	return len(self.data)
}

func (self *SafeMapInt64) Decode(data string) bool {
	self.locker.Lock()
	defer self.locker.Unlock()

	err := json.Unmarshal([]byte(data), &self.data)
	if err != nil {
		return false
	}
	return true
}

func (self *SafeMapInt64) Encode() ([]byte, error) {
	self.locker.RLock()
	defer self.locker.RUnlock()

	return json.Marshal(self.data)
}

func (self *SafeMapInt64) Clear() {
	self.locker.Lock()
	defer self.locker.Unlock()

	self.data = make(map[int64]interface{})
}

func (self *SafeMapInt64) Delete(key int64) {
	self.locker.Lock()
	defer self.locker.Unlock()

	delete(self.data, key)
}

type SafeMapIntInt struct {
	locker *sync.RWMutex
	data   map[int]int
}

func NewSafeMapIntInt() *SafeMapIntInt {
	return &SafeMapIntInt{
		locker: new(sync.RWMutex),
		data:   make(map[int]int),
	}
}

func (self *SafeMapIntInt) Load(key int) (interface{}, bool) {
	if v, ok := self.data[key]; ok {
		return v, ok
	}

	return nil, false
}

func (self *SafeMapIntInt) Store(key int, value int) {
	self.locker.Lock()
	defer self.locker.Unlock()

	self.data[key] = value
}

func (self *SafeMapIntInt) Range(f func(key, value int) bool) {
	self.locker.RLock()
	defer self.locker.RUnlock()

	for k, v := range self.data {
		if !f(k, v) {
			break
		}
	}
}

func (self *SafeMapIntInt) Decode(data string) bool {
	self.locker.Lock()
	defer self.locker.Unlock()

	err := json.Unmarshal([]byte(data), &self.data)
	if err != nil {
		return false
	}
	return true
}

func (self *SafeMapIntInt) Encode() ([]byte, error) {
	self.locker.RLock()
	defer self.locker.RUnlock()

	return json.Marshal(self.data)
}

func (self *SafeMapIntInt) Delete(key int) {
	self.locker.Lock()
	defer self.locker.Unlock()

	delete(self.data, key)
}

func (self *SafeMapIntInt) Count() int {
	self.locker.RLock()
	defer self.locker.RUnlock()

	return len(self.data)
}

func (self *SafeMapIntInt) CalValue(key int, value int) {
	self.locker.RLock()
	defer self.locker.RUnlock()

	self.data[key] += value
}

func (self *SafeMapIntInt) Len() int {
	self.locker.RLock()
	defer self.locker.RUnlock()
	return len(self.data)
}

type SafeMapString struct {
	locker *sync.RWMutex
	data   map[string]interface{}
}

func NewSafeMapString() *SafeMapString {
	return &SafeMapString{
		locker: new(sync.RWMutex),
		data:   make(map[string]interface{}),
	}
}

func (self *SafeMapString) Create() {
	self.locker = new(sync.RWMutex)
	self.data = make(map[string]interface{})
}

func (self *SafeMapString) Load(key string) (interface{}, bool) {
	self.locker.RLock()
	defer self.locker.RUnlock()
	if v, ok := self.data[key]; ok {
		return v, ok
	}

	return nil, false
}

func (self *SafeMapString) Store(key string, value interface{}) {
	self.locker.Lock()
	defer self.locker.Unlock()

	self.data[key] = value
}

func (self *SafeMapString) Range(f func(key string, value interface{}) bool) {
	self.locker.RLock()
	defer self.locker.RUnlock()

	for k, v := range self.data {
		if !f(k, v) {
			break
		}
	}
}

func (self *SafeMapString) Delete(key string) {
	self.locker.Lock()
	defer self.locker.Unlock()

	delete(self.data, key)
}

func (self *SafeMapString) Clear() {
	self.locker.Lock()
	defer self.locker.Unlock()

	self.data = make(map[string]interface{})
}

func (self *SafeMapString) Len() int {
	self.locker.RLock()
	defer self.locker.RUnlock()
	return len(self.data)
}
