package bytedance

import (
	"zone/lib/network"
)

// API URL 常量定义
const (
	// 字节跳动API基础URL
	ByteDanceAPIBaseURL = "https://webcast.bytedance.com/api"
	TouTiaoAPIBaseURL   = "https://developer.toutiao.com/api"

	// 具体API端点
	URLGetToken   = TouTiaoAPIBaseURL + "/apps/v2/token"
	URLLiveInfo   = ByteDanceAPIBaseURL + "/webcastmate/info"
	URLReporting  = ByteDanceAPIBaseURL + "/live_data/ack"
	URLLiveStart  = ByteDanceAPIBaseURL + "/live_data/task/start"
	URLLiveStop   = ByteDanceAPIBaseURL + "/live_data/task/stop"
	URLLiveStatus = ByteDanceAPIBaseURL + "/live_data/task/get"
	URLFailData   = ByteDanceAPIBaseURL + "/live_data/task/fail_data/get"
	URLGameInfo   = ByteDanceAPIBaseURL + "/live_data/game/info"
)

type JsTokenReq struct {
	AppId     string `json:"appid"`      //! 房间Id
	Secret    string `json:"secret"`     //! 应用Id
	GrantType string `json:"grant_type"` //! 消息类型
}

type JsTokenResultData struct {
	AccessToken string `json:"access_token"`
	ExpiresIn   int64  `json:"expires_in"`
}

type JsTokenResult struct {
	ErrNo   int               `json:"err_no"`
	ErrTips string            `json:"err_tips"`
	Data    JsTokenResultData `json:"data"`
}

// JsLiveInfoReq ! 请求结构
type JsLiveInfoReq struct {
	Token string `json:"token"` //! 房间Id
}

// JsLiveInfoResData ! 房间信息
type JsLiveInfoResInfo struct {
	RoomId       int64  `json:"room_id"`        //! 房间Id
	AnchorOpenId string `json:"anchor_open_id"` //! 主播Id
	AvatarUrl    string `json:"avatar_url"`     //! 主播头像
	NickName     string `json:"nick_name"`      //! 任务Id
}

// JsLiveInfoResData ! 房间信息
type JsLiveInfoResData struct {
	Info JsLiveInfoResInfo `json:"info"`
}

// JsLiveInfoRes ! 获取直播信息
type JsLiveInfoRes struct {
	ErrNo  int               `json:"errcode"`
	ErrMsg string            `json:"errmsg"`
	Data   JsLiveInfoResData `json:"data"` //! 任务Id
}

// JsLiveStartReq ! 请求结构
type JsLiveStartReq struct {
	RoomId  string `json:"roomid"`   //! 房间Id
	AppId   string `json:"appid"`    //! 应用Id
	MsgType string `json:"msg_type"` //! 消息类型
}

// JsLiveStartResData ! 开始直播请求
type JsLiveStartResData struct {
	TaskId string `json:"task_id"` //! 任务Id
}

// JsLiveStartRes ! 开始直播范返回
type JsLiveStartRes struct {
	ErrNo  int                `json:"err_no"`
	ErrMsg string             `json:"err_msg"`
	LogId  string             `json:"logid"`
	Data   JsLiveStartResData `json:"data"`
}

type JsLiveTaskData struct {
	Status int `json:"status"` //! 任务Id
}

// JsLiveTaskRes ! 开始直播范返回
type JsLiveTaskRes struct {
	ErrNo  int            `json:"err_no"`
	ErrMsg string         `json:"err_msg"`
	LogId  string         `json:"logid"`
	Data   JsLiveTaskData `json:"data"`
}

type JsReportingData struct {
	MsgId      string `json:"msg_id"`      //! 唯一标识，平台推送payload数据里的msg_id
	MsgType    string `json:"msg_type"`    //! 消息类型，如礼物为live_gift，评论为live_comment
	ClientTime int64  `json:"client_time"` //! 毫秒级时间戳
}

type JsReporting struct {
	RoomId  string `json:"room_id"`  //! 房间Id
	AppId   string `json:"app_id"`   //! 应用Id
	AckType int    `json:"ack_type"` //! 消息类型 上报类型，1：收到推送；2：完成处理
	Data    string `json:"data"`     //! 数据
}

type JsReportingRes struct {
	ErrNo  int    `json:"err_no"`
	ErrMsg string `json:"err_msg"`
	LogId  string `json:"log_id"`
}

type JsLiveStatusReq struct {
	RoomId  string `json:"roomid"`   //! 房间Id
	AppId   string `json:"appid"`    //! 应用Id
	MsgType string `json:"msg_type"` //! 消息类型
}

type JsFailDataReq struct {
	RoomId   string `json:"roomid"`    //! 房间ID
	AppId    string `json:"appid"`     //! 应用Id
	MsgType  string `json:"msg_type"`  //! 消息类型
	PageNum  int    `json:"page_num"`  //! 页码
	PageSize int    `json:"page_size"` //! 每页数量
}

type JsFailData struct {
	RoomId  string `json:"roomid"`   //! 应用Id
	MsgType string `json:"msg_type"` //! 消息类型
	Payload string `json:"payload"`  //! 页码
}

type JsFailDataData struct {
	PageNum    int           `json:"page_num"`    //! 页码
	TotalCount int           `json:"total_count"` //! 页码
	DataList   []*JsFailData `json:"data_list"`   //! 页码
}

type JsFailDataRes struct {
	ErrNo  int            `json:"err_no"`
	ErrMsg string         `json:"err_msg"`
	LogId  string         `json:"logid"`
	Data   JsFailDataData `json:"data"`
}

type GameInfoRequest struct {
	AppID        string              `json:"app_id"`
	RoundID      string              `json:"round_id"`
	AnchorOpenID string              `json:"anchor_open_id"`
	RoomID       string              `json:"room_id"`
	StartTime    int64               `json:"start_time"`
	EndTime      int64               `json:"end_time"`
	EndStatus    int                 `json:"end_status"`
	RankLists    []*network.RankList `json:"rank_lists"`
}

type GameInfoResSuc struct {
	Code int64 `json:"code"`
}

type GameInfoResFail struct {
	ErrCode int    `json:"errcode"`
	ErrMsg  string `json:"errmsg"`
}

type Payload struct {
	MsgId              string `json:"msg_id"`
	OpenId             string `json:"sec_openid"`
	AvatarUrl          string `json:"avatar_url"`
	Nickname           string `json:"nickname"`
	Timestamp          int64  `json:"timestamp"`
	GiftId             string `json:"sec_gift_id"`
	GiftNum            int    `json:"gift_num"`
	GiftValue          int    `json:"gift_value"`
	Content            string `json:"content"`
	LikeNum            int    `json:"like_num"`
	FansclubReasonType int    `json:"fansclub_reason_type"`
	FansclubLevel      int    `json:"fansclub_level"`
}
