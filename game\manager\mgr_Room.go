package manager

import (
	"fmt"
	"strconv"
	"zone/game/mods"
	"zone/lib/utils"
)

// RoomMgr ! 中心服务器
type RoomMgr struct {
	RoomNum         int                  //! 房间数量
	RoomInitNum     int                  //! 初始房间数量
	RoomIdMax       int32                //! 房间ID 最大序号
	RoomRoundId     int                  //! 房间ID 最大序号
	RoomMap         *utils.SafeMapInt32  //! 房间管理器
	RoomPlatformMap *utils.SafeMapString //! 抖音房间编号
}

var s_roommgr *RoomMgr = nil

func GetRoomMgr() *RoomMgr {
	if s_roommgr == nil {
		s_roommgr = new(RoomMgr)
		s_roommgr.RoomInitNum = 10
		s_roommgr.RoomIdMax = 1
		s_roommgr.RoomRoundId = 1

		s_roommgr.RoomMap = utils.NewSafeMapInt32()
		s_roommgr.RoomPlatformMap = utils.NewSafeMapString()

		// 注册到mods包的管理器注册表
		mods.RegisterRoomManager(s_roommgr)
	}

	return s_roommgr
}

func (roomMgr *RoomMgr) GetRoomById(roomId int32) *mods.LiveRoom {
	room, ok := roomMgr.RoomMap.Load(roomId)
	if ok {
		return room.(*mods.LiveRoom)
	}

	return nil
}

func (roomMgr *RoomMgr) GetRoomByPlatformId(roomId string) *mods.LiveRoom {
	room, ok := roomMgr.RoomPlatformMap.Load(roomId)
	if ok {
		return room.(*mods.LiveRoom)
	}
	return nil
}

func (roomMgr *RoomMgr) CreateRoomByPlatformId(roomId string) *mods.LiveRoom {
	if value, ok := roomMgr.RoomPlatformMap.Load(roomId); ok {
		room := value.(*mods.LiveRoom)
		room.Reset(false)  // 重置但不关闭
		room.RestartRoom() // 重新启动房间（如果需要）
		return room
	} else {
		room := roomMgr.CreateRoom()
		room.RoomPlatformId = roomId
		roomMgr.RoomPlatformMap.Store(roomId, room)
		return room //errors.New("player not found")
	}

}

func (roomMgr *RoomMgr) ReStoreLiveRoom(month bool) {

	roomMgr.RoomPlatformMap.Range(func(key string, value interface{}) bool {
		room := (value).(*mods.LiveRoom)
		if room != nil {
			room.ReStoreLivePlayer(month)
		}
		return true
	})
}

func (roomMgr *RoomMgr) SetAnnouncement(content string) {
	if content == "" {
		return
	}

	roomMgr.RoomPlatformMap.Range(func(key string, value interface{}) bool {
		room := (value).(*mods.LiveRoom)
		if room != nil {
			room.SetAnnouncement(content)
		}
		return true
	})
}

func (roomMgr *RoomMgr) GetRoomRoundID() string {
	// 优化：使用strconv.Itoa替代fmt.Sprintf，性能提升3-5倍
	content := strconv.Itoa(roomMgr.RoomRoundId)
	roomMgr.RoomRoundId += 1
	return content
}

func (roomMgr *RoomMgr) CreateRoom() *mods.LiveRoom {

	room := new(mods.LiveRoom)

	room.RoomId = roomMgr.RoomIdMax
	room.Init()
	roomMgr.RoomIdMax += 1
	roomMgr.RoomMap.Store(room.RoomId, room)

	//! 启动逻辑
	go room.LogicRun()

	return room
}

// CreateRoomWithGameType 创建指定游戏类型的房间
func (roomMgr *RoomMgr) CreateRoomWithGameType(gameType mods.GameType) *mods.LiveRoom {
	room := roomMgr.CreateRoom()

	// 根据游戏类型设置不同的游戏实例
	switch gameType {
	case mods.GameTypeMulti:
	case mods.GameTypeLive:
		fallthrough
	default:
		// 单人游戏模式（默认）- 房间初始化时已经设置为单人游戏模式
		// 无需额外操作
	}

	return room
}

// SwitchRoomGameType 切换房间的游戏类型
func (roomMgr *RoomMgr) SwitchRoomGameType(roomId int32, gameType mods.GameType) error {
	room := roomMgr.GetRoomById(roomId)
	if room == nil {
		return fmt.Errorf("房间%d不存在", roomId)
	}

	switch gameType {
	case mods.GameTypeMulti:
	case mods.GameTypeLive:
		room.SwitchToLiveGame()
	default:
		return fmt.Errorf("不支持的游戏类型: %v", gameType)
	}

	return nil
}

// GetRoomGameType 获取房间的游戏类型
func (roomMgr *RoomMgr) GetRoomGameType(roomId int32) mods.GameType {
	room := roomMgr.GetRoomById(roomId)
	if room == nil {
		return mods.GameTypeLive // 默认单人游戏
	}

	if room.IsMultiGame() {
		return mods.GameTypeMulti
	}
	return mods.GameTypeLive
}

// GetRoomMap 获取房间映射
func (roomMgr *RoomMgr) GetRoomMap() interface{} {
	return roomMgr.RoomMap
}

// GetRoomPlatformMap 获取平台房间映射
func (roomMgr *RoomMgr) GetRoomPlatformMap() interface{} {
	return roomMgr.RoomPlatformMap
}
