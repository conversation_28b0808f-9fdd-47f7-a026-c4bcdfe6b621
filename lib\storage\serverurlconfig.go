package storage

import (
	"fmt"
	"sync"
	"zone/lib/core"
	"zone/lib/db"
)

// ServerUrlConfig ServerUrl配置结构体（简化版）
type ServerUrlConfig struct {
	ID           int    `json:"id" db:"id"`                       // 平台编号：0-抖音，1-快手，99-本地开发环境
	WebSocketURL string `json:"websocket_url" db:"websocket_url"` // WebSocket地址
	HttpURL      string `json:"http_url" db:"http_url"`           // HTTP地址
	UseEncrypt   bool   `json:"use_encrypt" db:"use_encrypt"`     // 是否启用加密
}

// ServerUrlConfigManager ServerUrl配置管理器（简化版）
type ServerUrlConfigManager struct {
	cache      sync.Map     // 内存缓存
	cacheMutex sync.RWMutex // 缓存读写锁
}

var (
	serverUrlConfigMgr     *ServerUrlConfigManager
	serverUrlConfigMgrOnce sync.Once
)

// GetServerUrlConfigManager 获取ServerUrl配置管理器单例
func GetServerUrlConfigManager() *ServerUrlConfigManager {
	serverUrlConfigMgrOnce.Do(func() {
		serverUrlConfigMgr = &ServerUrlConfigManager{}
	})
	return serverUrlConfigMgr
}

// GetConfigByPlatform 根据平台ID获取配置（带缓存）
func (m *ServerUrlConfigManager) GetConfigByPlatform(platformID int) (*ServerUrlConfig, error) {
	// 先尝试从缓存获取
	if value, ok := m.cache.Load(platformID); ok {
		if config, ok := value.(*ServerUrlConfig); ok {
			return config, nil
		}
	}

	// 缓存未命中，从数据库查询
	config, err := m.getConfigFromDB(platformID)
	if err != nil {
		return nil, err
	}

	// 更新缓存
	m.cache.Store(platformID, config)

	return config, nil
}

// getConfigFromDB 从数据库获取配置
func (m *ServerUrlConfigManager) getConfigFromDB(platformID int) (*ServerUrlConfig, error) {
	sql := `SELECT id, websocket_url, http_url, use_encrypt 
			FROM %s 
			WHERE id = %d`

	query := fmt.Sprintf(sql, TABLE_ServerUrl, platformID)

	rows, err := db.GetDBMgr().DBUser.QueryAny(query)
	if err != nil {
		core.LogError("查询ServerUrl配置失败:", err)
		return nil, err
	}
	defer rows.Close()

	if rows.Next() {
		config := &ServerUrlConfig{}
		var useEncryptInt int
		err := rows.Scan(&config.ID, &config.WebSocketURL, &config.HttpURL, &useEncryptInt)
		if err != nil {
			core.LogError("扫描ServerUrl配置数据失败:", err)
			return nil, err
		}
		config.UseEncrypt = useEncryptInt == 1
		return config, nil
	}

	return nil, fmt.Errorf("未找到平台ID为 %d 的配置", platformID)
}

// GetAllConfigs 获取所有配置
func (m *ServerUrlConfigManager) GetAllConfigs() ([]*ServerUrlConfig, error) {
	sql := `SELECT id, websocket_url, http_url, use_encrypt 
			FROM serverurl 
			ORDER BY id`

	rows, err := db.GetDBMgr().DBUser.QueryAny(sql)
	if err != nil {
		core.LogError("查询所有ServerUrl配置失败:", err)
		return nil, err
	}
	defer rows.Close()

	var configs []*ServerUrlConfig
	for rows.Next() {
		config := &ServerUrlConfig{}
		var useEncryptInt int
		err := rows.Scan(&config.ID, &config.WebSocketURL, &config.HttpURL, &useEncryptInt)
		if err != nil {
			core.LogError("扫描ServerUrl配置数据失败:", err)
			continue
		}
		config.UseEncrypt = useEncryptInt == 1
		configs = append(configs, config)
	}

	return configs, nil
}

// CreateConfig 创建新配置
func (m *ServerUrlConfigManager) CreateConfig(config *ServerUrlConfig) error {
	useEncryptInt := 0
	if config.UseEncrypt {
		useEncryptInt = 1
	}
	sql := "INSERT INTO %s (id, websocket_url, http_url, use_encrypt) VALUES (%d, '%s', '%s', %d)"

	_, _, success := db.GetDBMgr().DBUser.Exec(sql, TABLE_ServerUrl, config.ID, config.WebSocketURL, config.HttpURL, useEncryptInt)
	if !success {
		return fmt.Errorf("创建ServerUrl配置失败")
	}

	// 清除缓存
	m.cache.Delete(config.ID)

	core.LogInfo("创建ServerUrl配置成功: 平台ID=", config.ID)
	return nil
}

// UpdateConfig 更新配置
func (m *ServerUrlConfigManager) UpdateConfig(config *ServerUrlConfig) error {

	useEncryptInt := 0
	if config.UseEncrypt {
		useEncryptInt = 1
	}

	sql := "UPDATE %s SET websocket_url = '%s', http_url = '%s', use_encrypt = %d WHERE id = %d"

	_, _, success := db.GetDBMgr().DBUser.Exec(sql, TABLE_ServerUrl, config.WebSocketURL, config.HttpURL, useEncryptInt, config.ID)
	if !success {
		return fmt.Errorf("更新ServerUrl配置失败")
	}

	// 清除缓存
	m.cache.Delete(config.ID)

	core.LogInfo("更新ServerUrl配置成功: 平台ID", config.ID)
	return nil
}

// DeleteConfig 删除配置
func (m *ServerUrlConfigManager) DeleteConfig(platformID int) error {
	sql := "DELETE FROM %s WHERE id = %d"

	_, _, success := db.GetDBMgr().DBUser.Exec(sql, TABLE_ServerUrl, platformID)
	if !success {
		return fmt.Errorf("删除ServerUrl配置失败")
	}

	// 清除缓存
	m.cache.Delete(platformID)

	core.LogInfo("删除ServerUrl配置成功: 平台ID=", platformID)
	return nil
}

// LoadAllConfigsToCache 将所有配置加载到缓存
func (m *ServerUrlConfigManager) LoadAllConfigsToCache() error {
	configs, err := m.GetAllConfigs()
	if err != nil {
		return err
	}

	for _, config := range configs {
		m.cache.Store(config.ID, config)
	}

	// core.LogInfo(fmt.Sprintf("加载了 %d 个ServerUrl配置到缓存", len(configs)))
	return nil
}

// ClearAllCache 清除所有缓存
func (m *ServerUrlConfigManager) ClearAllCache() {
	m.cacheMutex.Lock()
	defer m.cacheMutex.Unlock()

	m.cache.Range(func(key, value interface{}) bool {
		m.cache.Delete(key)
		return true
	})
	core.LogInfo("清除所有ServerUrl配置缓存")
}

// GetCacheStats 获取缓存统计信息
func (m *ServerUrlConfigManager) GetCacheStats() map[string]interface{} {
	stats := make(map[string]interface{})

	count := 0
	m.cache.Range(func(key, value interface{}) bool {
		count++
		return true
	})

	stats["cache_count"] = count
	return stats
}
