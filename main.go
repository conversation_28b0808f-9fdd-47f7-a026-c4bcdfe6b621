package main

import (
	"log"
	_ "net/http/pprof"
	"os"
	"os/signal"
	"runtime"
	"syscall"
	"zone/app"
)

// registerSignalHandler 注册信号处理器
// signalType: 要监听的信号类型
// handlerFunc: 信号处理函数
func registerSignalHandler(signalType os.Signal, handlerFunc func(*chan os.Signal)) {
	signalChannel := make(chan os.Signal, 1)
	signal.Notify(signalChannel, signalType)
	go handlerFunc(&signalChannel)
}

// handlePipeSignal 处理管道破裂信号 (SIGPIPE)
func handlePipeSignal(signalChannel *chan os.Signal) {
	for {
		<-*signalChannel
		log.Println("收到 SIGPIPE 信号：管道破裂")
	}
}

// handleStopSignal 处理停止信号 (SIGTSTP - Ctrl+Z)
func handleStopSignal(signalChannel *chan os.Signal) {
	for {
		<-*signalChannel
		log.Println("收到 SIGTSTP 信号：进程暂停")
		// TODO: 可以在这里添加配置重载逻辑
		// csvUtilManager.Reload()
	}
}

// handleTrapSignal 处理调试陷阱信号 (SIGTRAP)
func handleTrapSignal(signalChannel *chan os.Signal) {
	for {
		<-*signalChannel
		log.Println("收到 SIGTRAP 信号：调试陷阱")
	}
}

// handleInterruptSignal 处理中断信号 (SIGINT - Ctrl+C)
// 执行优雅关闭流程
func handleInterruptSignal(signalChannel *chan os.Signal) {
	for {
		<-*signalChannel
		log.Println("收到 SIGINT 信号：开始优雅关闭服务器")
		app.GetZoneApp().Close()
	}
}

func main() {
	// 设置CPU核心数为物理核心数的2倍，提高并发性能
	runtime.GOMAXPROCS(runtime.NumCPU() * 2)

	// 注册系统信号处理器，实现优雅关闭
	setupSignalHandlers()

	// 获取区域应用实例
	zoneApplication := app.GetZoneApp()

	// 获取配置信息
	config := zoneApplication.GetConfig()

	// 初始化Gin HTTP路由器
	zoneApplication.InitGinRouter()
	// 输出服务器启动信息
	log.Printf("服务器绑定地址: %s", config.Host)
	log.Printf("服务器版本: %d", config.ServerVer)

	// 启动区域服务器（阻塞调用）
	log.Println("正在启动区域服务器...")
	zoneApplication.StartService()
}

// setupSignalHandlers 设置系统信号处理器
func setupSignalHandlers() {
	// 注册管道破裂信号处理器
	registerSignalHandler(syscall.SIGPIPE, handlePipeSignal)

	// 注册调试陷阱信号处理器
	registerSignalHandler(syscall.SIGTRAP, handleTrapSignal)

	// 注册中断信号处理器（Ctrl+C）
	registerSignalHandler(syscall.SIGINT, handleInterruptSignal)

	// Linux系统下的停止信号处理器（当前注释掉）
	// registerSignalHandler(syscall.SIGTSTP, handleStopSignal)

	log.Println("系统信号处理器注册完成")
}
