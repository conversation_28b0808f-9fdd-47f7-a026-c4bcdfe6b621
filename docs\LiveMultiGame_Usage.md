# LiveMultiGame系统使用指南

## 概述

LiveMultiGame系统是一个支持多个房间连接到同一个游戏实例的多人游戏框架。它在保持与现有LiveGame接口兼容性的基础上，提供了强大的多房间管理和数据同步功能。

## 核心特性

### 1. 多房间支持
- 一个LiveMultiGame实例可以管理多个LiveRoom
- 支持房间的动态加入和离开
- 自动选择和管理主房间

### 2. 数据同步
- 实时同步游戏状态到所有连接的房间
- 统一的玩家数据管理
- 跨房间的事件广播

### 3. 平滑切换
- 支持单人游戏和多人游戏之间的无缝切换
- 保持游戏状态的连续性

### 4. 接口兼容
- 完全实现GameInterface接口
- 与现有LiveGame保持API兼容

## 基本使用

### 1. 创建多人游戏

```go
// 获取多人游戏管理器
multiGameMgr := mods.GetMultiGameMgr()

// 创建新的多人游戏
multiGame, err := multiGameMgr.CreateMultiGame()
if err != nil {
    log.Fatal("创建多人游戏失败:", err)
}

fmt.Printf("创建多人游戏成功，游戏ID: %s\n", multiGame.GetGameID())
```

### 2. 添加房间到多人游戏

```go
// 创建房间
roomMgr := mods.GetRoomMgr()
room := roomMgr.CreateRoom()

// 添加房间到多人游戏
err := multiGame.AddRoom(room)
if err != nil {
    log.Fatal("添加房间失败:", err)
}

fmt.Printf("房间%d已添加到游戏%s\n", room.RoomId, multiGame.GetGameID())
```

### 3. 自动匹配多人游戏

```go
// 创建或加入可用的多人游戏
multiGame, err := multiGameMgr.CreateOrJoinMultiGame(room)
if err != nil {
    log.Fatal("加入多人游戏失败:", err)
}

fmt.Printf("房间%d已加入游戏%s，当前房间数: %d\n", 
    room.RoomId, multiGame.GetGameID(), multiGame.GetConnectedRoomCount())
```

### 4. 游戏类型切换

```go
// 直接使用房间的切换方法
// 切换到多人游戏
multiGameMgr := mods.GetMultiGameMgr()
multiGame, err := multiGameMgr.CreateOrJoinMultiGame(room)
if err != nil {
    log.Fatal("切换到多人游戏失败:", err)
}
room.SwitchToMultiGame(multiGame)

// 检查当前游戏类型
if room.IsMultiGame() {
    fmt.Println("当前是多人游戏模式")
}

// 切换回单人游戏
room.SwitchToLiveGame()
```

## 高级功能

### 1. 玩家管理

```go
// 创建玩家
player := &mods.LivePlayer{
    LiveId: 12345,
    Data: &mods.PlayerData{
        PlayerName: "TestPlayer",
        OpenId:     "test_openid",
    },
}
player.Init()

// 添加玩家到多人游戏
success := multiGame.AddPlayer(player)
if success {
    fmt.Printf("玩家%d已添加到游戏\n", player.LiveId)
}

// 检查玩家是否存在
if multiGame.HasPlayer(player.LiveId) {
    fmt.Printf("玩家%d存在于游戏中\n", player.LiveId)
}

// 获取玩家
retrievedPlayer := multiGame.GetRoomPlayer(player.LiveId)
if retrievedPlayer != nil {
    fmt.Printf("获取到玩家: %s\n", retrievedPlayer.Data.PlayerName)
}
```

### 2. 用户交互处理

```go
// 处理评论
comment := []byte("这是一条测试评论")
multiGame.ProcessComment(room.RoomId, comment)

// 处理点赞
like := []byte("点赞数据")
multiGame.ProcessLike(room.RoomId, like)

// 处理礼物
gifts := []*payload.PayloadData{
    {
        OpenId:    "test_openid",
        GiftId:    "gift_001",
        GiftValue: 100,
        Content:   "送出礼物",
    },
}
multiGame.ProcessGift(room.RoomId, gifts, "msg_001")
```

### 3. 游戏状态管理

```go
// 设置游戏状态
multiGame.SetPlayState(mods.PLAY_STATE_READY)

// 开始游戏
multiGame.GameStart()

// 设置积分池
multiGame.SetScorePool(1000)

// 广播游戏状态到所有房间
multiGame.BroadcastGameState()

// 结束游戏
multiGame.GameOver(nil)
```

### 4. 数据同步

```go
// 同步特定房间的状态
err := multiGame.SyncRoomState(room)
if err != nil {
    log.Printf("同步房间状态失败: %v", err)
}

// 验证房间同步状态
if multiGame.ValidateRoomSync(room) {
    fmt.Println("房间同步状态正常")
}

// 同步所有房间数据
multiGame.SyncAllRoomsData()
```

## 配置和管理

### 1. 设置最大房间数

```go
// 设置单个游戏的最大房间数
multiGame.SetMaxRoomCount(10)

// 设置全局最大游戏数量
multiGameMgr.SetMaxGamesCount(100)

// 设置每个游戏的最大房间数
multiGameMgr.SetMaxRoomsPerGame(8)
```

### 2. 获取游戏统计信息

```go
// 获取游戏统计信息
stats := multiGame.GetGameStatistics()
fmt.Printf("游戏统计: %+v\n", stats)

// 获取所有活跃游戏列表
gameList := multiGameMgr.GetGameList()
for _, gameInfo := range gameList {
    fmt.Printf("游戏: %+v\n", gameInfo)
}

// 获取活跃游戏数量
activeCount := multiGameMgr.GetActiveGameCount()
fmt.Printf("当前活跃游戏数: %d\n", activeCount)
```

### 3. 健康检查和清理

```go
// 检查游戏健康状态
if multiGame.CheckGameHealth() {
    fmt.Println("游戏状态健康")
} else {
    fmt.Println("游戏状态异常")
}

// 清理断开连接的房间
multiGame.CleanupDisconnectedRooms()

// 清理已结束的游戏
multiGameMgr.CleanupFinishedGames()
```

## 最佳实践

### 1. 错误处理
- 始终检查方法返回的错误
- 对关键操作进行重试机制
- 记录详细的错误日志

### 2. 性能优化
- 合理设置最大房间数和游戏数
- 定期清理无用的游戏和房间
- 避免频繁的状态同步

### 3. 并发安全
- LiveMultiGame内部已实现线程安全
- 外部调用时注意避免竞态条件
- 使用适当的锁机制保护共享资源

### 4. 监控和调试
- 定期检查游戏健康状态
- 监控内存使用情况
- 记录关键操作的性能指标

## 故障排除

### 常见问题

1. **房间添加失败**
   - 检查游戏是否已达到最大房间数
   - 确认房间未被其他游戏占用
   - 验证游戏状态是否允许添加新房间

2. **同步失败**
   - 检查网络连接状态
   - 验证房间会话是否有效
   - 确认同步数据格式正确

3. **性能问题**
   - 检查房间和玩家数量是否过多
   - 优化广播频率
   - 考虑分片或负载均衡

### 调试技巧

```go
// 启用详细日志
core.SetLogLevel(core.LogLevelDebug)

// 获取详细的游戏状态
stats := multiGame.GetGameStatistics()
fmt.Printf("详细状态: %+v\n", stats)

// 检查特定房间的数据
roomData := multiGame.GetRoomSpecificData(roomId)
fmt.Printf("房间数据: %+v\n", roomData)
```

## 总结

LiveMultiGame系统提供了强大而灵活的多人游戏支持，通过合理使用其提供的API，可以轻松构建支持多房间的实时游戏应用。系统的设计保证了与现有代码的兼容性，同时提供了丰富的扩展能力。
