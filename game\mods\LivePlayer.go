package mods

import (
	"sort"
	"sync"
	"zone/game/Request"
	"zone/game/configs"
	"zone/game/models"
	"zone/lib/core"
	"zone/lib/db"
	"zone/lib/network"
	"zone/lib/payload"
	"zone/lib/utils"
	"zone/pb/Message"
)

// LiveScore ! 直播积分
type LiveScore struct {
	LiveId      int64 //! 直播Id
	RoomId      int   //! 直播房间
	GameScore   int   //! 积分
	KillScore   int   //! 击杀积分
	GiftScore   int   //! 礼物积分
	CurrentRank int   //! 当前排行榜
	WinStreak   int   //! 连胜
	Call        int
}

func (self *LiveScore) Reset() {
	self.GameScore = 0
	self.KillScore = 0
	self.GiftScore = 0
	self.CurrentRank = 0
	// self.WinStreak = 0
}

func (self *LiveScore) IsExpired(starttime int64) bool {
	// if self.StartTime != starttime {
	// 	return true
	// }

	return false
}

func (self *LiveScore) GetTotalPoint() int {
	return self.GameScore
}

// PlayerLiveInfo ! 直播状态
type PlayerLiveInfo struct {
	AnchorId   string
	FamilyName string
	//BlockCount   int    //! 本局当前小地块计数
	IsDead bool //! 是否已经死亡
}

// LivePlayer ! 房间角色
type LivePlayer struct {
	LiveId    int64     //! 直播角色Id，由服务器生成，跟角色Id对应
	OpenId    string    //! 平台ID，相当于OpenId
	ScoreData *sync.Map //! 积分信息 key 为room id 值为 *LiveScore
	InfoData  *sync.Map //! 直播状态 key 为room id 值为 *PlayerLiveInfo

	Data *models.LivePlayerDB //! 全局数据
}

// Reset ! 重新开局，重置数据
func (self *LivePlayer) Reset(roomid int32) {
	self.ScoreData.Delete(roomid)
	self.InfoData.Delete(roomid)
}

func (self *LivePlayer) GetLiveScore(roomid int32) *LiveScore {
	if score, ok := self.ScoreData.Load(roomid); ok {
		return score.(*LiveScore)
	}

	return nil
}

func (self *LivePlayer) GetLiveGameScore(roomid int32) int {
	if score, ok := self.ScoreData.Load(roomid); ok {
		liveScore := score.(*LiveScore)
		if liveScore != nil {
			return liveScore.GameScore
		}
		return 0
	}

	return 0
}

func (self *LivePlayer) AddLiveScore(roomid int32, liveScore *LiveScore) {
	self.ScoreData.Store(roomid, liveScore)

	self.Data.JoinGameTimes += 1
}

func (self *LivePlayer) GetPlayerLiveInfo(roomid int32) *PlayerLiveInfo {
	if info, ok := self.InfoData.Load(roomid); ok {
		return info.(*PlayerLiveInfo)
	}

	return nil
}
func (self *LivePlayer) AddPlayerLiveInfo(roomid int32, info *PlayerLiveInfo) {
	self.InfoData.Store(roomid, info)
	self.SetFamilyScore(info, 0)
}

func (self *LivePlayer) SetFamilyScore(info *PlayerLiveInfo, score int64) {
	self.Data.SetFamilyScore(info.FamilyName, score)
}

func (self *LivePlayer) AddGiftRecord(name string, count int) {
	self.Data.AddGiftRecord(name, count)
}

func (self *LivePlayer) AddGiftById(roomid int32, giftId int, giftNum int) {
	// core.LogDebug("LivePlayer : ", self.LiveId, " Add Gift ", giftNum)
	// self.Gift += giftNum
	// self.Data.Gift += giftNum

	giftConfig := configs.GetGiftConfig().GetGiftConfigById(giftId)
	if giftConfig == nil {
		core.LogDebug("GetGiftConfig Error : ", giftId)
		return
	}

	room := GetRoomMgr().GetRoomById(roomid)
	if room == nil {
		return
	}

	room.SetScorePool(room.GetScorePool() + int64(int(giftConfig.GiftPoolScore)*giftNum))

	// GetLiveTopMgr().AddCall(self.LiveId, int(giftConfig.Count)*giftNum)
	// var scoreData = self.GetLiveScore(roomid)
	// if scoreData != nil {
	// 	scoreData.Call += int(giftConfig.Count) * giftNum
	// }
	// self.Data.TotalCall += int64(int(giftConfig.Count) * giftNum)
	self.AddScore(roomid, int(giftConfig.Score), "")

	if !utils.IsNil(room) && !utils.IsNil(room.Session) {
		var PlayerOperate = new(Message.PlayerOperateDTO)
		PlayerOperate.PlayerId = int32(self.LiveId)
		PlayerOperate.Pamram = int32(configs.GetGiftConfig().GetGiftParameter(giftId, giftNum))
		PlayerOperate.Type = 1
		PlayerOperate.MsgId = ""
		Request.PlayerOperateRequest(room.Session, PlayerOperate)
	}
}

func (self *LivePlayer) AddBlindBox(roomid int32, giftId int, giftNum int) {

	giftConfig := configs.GetGiftConfig().GetGiftConfigById(giftId)
	if giftConfig == nil {
		core.LogDebug("GetGiftConfig Error : ", giftId)
		return
	}

	room := GetRoomMgr().GetRoomById(roomid)
	if room == nil {
		return
	}
	// self.AddScore(roomid, giftConfig.Score)

	if !utils.IsNil(room) && !utils.IsNil(room.Session) {
		var PlayerOperate = new(Message.PlayerOperateDTO)
		PlayerOperate.PlayerId = int32(self.LiveId)
		PlayerOperate.Pamram = int32(configs.GetGiftConfig().GetGiftParameter(giftId, giftNum))
		PlayerOperate.Type = 1
		PlayerOperate.MsgId = ""
		Request.PlayerOperateRequest(room.Session, PlayerOperate)
	}
}

func (self *LivePlayer) ChangeName(roomid int32, name string) {

	room := GetRoomMgr().GetRoomById(roomid)
	if room == nil {
		return
	}

	config := configs.GetGameConfig().GetBlockNameConfigByName(name)
	if config == nil {
		return
	}

	info := self.GetPlayerLiveInfo(roomid)
	if info == nil {
		return
	}
	if info.FamilyName == name {
		return
	}

	info.FamilyName = name

	if !utils.IsNil(room) && !utils.IsNil(room.Session) {
		var PlayerOperate = new(Message.PlayerOperateDTO)
		PlayerOperate.PlayerId = int32(self.LiveId)
		PlayerOperate.Name = name
		PlayerOperate.Type = 4
		PlayerOperate.MsgId = ""
		Request.PlayerOperateRequest(room.Session, PlayerOperate)
	}

}

func (self *LivePlayer) AddGiftValue(giftValue int64) {
	self.Data.GiftValue += giftValue
}

// AddGift ! 刷新礼物
func (self *LivePlayer) AddGift(roomid int32, payload *payload.PayloadData) bool {
	// core.LogDebug("LivePlayer : ", self.LiveId, " Add Gift ", giftNum)
	// self.Gift += giftNum
	// self.Data.Gift += giftNum

	giftId := payload.GiftId
	giftNum := payload.GiftNum
	giftValue := payload.GiftValue
	giftConfig := configs.GetGiftConfig().GetGiftConfig(giftId)
	if giftConfig == nil {
		core.LogDebug("GetGiftConfig Error : ", giftId)
		return false
	}
	room := GetRoomMgr().GetRoomById(roomid)
	if room != nil {
		room.SetScorePool(room.GetScorePool() + int64(int(giftConfig.GiftPoolScore)*giftNum))
	}
	self.AddGiftRecord(giftConfig.Name[network.Platform], giftNum)
	giftValues := int64(giftValue * giftNum)
	self.AddGiftValue(giftValues)
	room.AnchorData.SetPlayerValue(self.LiveId, giftValues)
	self.AddScore(roomid, int(giftConfig.Score)*giftNum, models.AddGiftLog)

	// GetLiveTopMgr().AddCall(self.LiveId, int(giftConfig.Count)*giftNum)
	// var scoreData = self.GetLiveScore(roomid)
	// if scoreData != nil {
	// 	scoreData.Call += int(giftConfig.Count) * giftNum
	// }
	// self.Data.TotalCall += int64(int(giftConfig.Count) * giftNum)

	if !utils.IsNil(room) && !utils.IsNil(room.Session) {
		GetGiftRecordMgr().ExRecord(payload.MsgId)
		var PlayerOperate = new(Message.PlayerOperateDTO)
		PlayerOperate.PlayerId = int32(self.LiveId)
		PlayerOperate.Pamram = int32(configs.GetGiftConfig().GetGiftParameterByString(giftId, giftNum))
		PlayerOperate.Type = 1
		PlayerOperate.MsgId = payload.UMsgId
		Request.PlayerOperateRequest(room.Session, PlayerOperate)
	}

	if network.Platform == network.Platform_KuaiShou {
		if giftConfig.BlindBox != 0 {
			blindBoxMap := configs.GetGiftConfig().GetBlindBox(giftConfig.BlindBox, giftNum)
			keys := make([]int, 0, len(blindBoxMap))
			for k := range blindBoxMap {
				keys = append(keys, k)
			}
			sort.Ints(keys)
			for _, k := range keys {
				// fmt.Printf("%d: %d\n", k, blindBoxMap[k])
				self.AddBlindBox(roomid, k, blindBoxMap[k])
			}
		}
	}

	return true
}

// AddScore ! 增加积分，汇总增加
func (self *LivePlayer) AddScore(roomid int32, score int, desc string) {
	// core.LogDebug("LivePlayer : ", self.LiveId, " Add Point ", score)
	if score == 0 {
		return
	}
	var scoreData = self.GetLiveScore(roomid)
	if scoreData != nil {
		scoreData.GameScore += score
		if desc == models.AddGiftLog {
			scoreData.GiftScore += score
		}
		GetLiveTopMgr().AddScore(self.LiveId, int64(score))
		room := GetRoomMgr().GetRoomById(roomid)
		if room != nil {
			if !utils.IsNil(room) && !utils.IsNil(room.Session) {
				playerData := new(Message.PlayerSyncDTO)
				playerData.GameScore = int32(scoreData.GameScore)
				playerData.GameKillScore = int32(scoreData.KillScore)
				playerData.GiftScore = int32(scoreData.GiftScore)
				Request.SyncPlayerDataRequest(room.Session, int32(self.LiveId), playerData, room.GetScorePool())
			}
		}
	}
	self.Data.WeekScore += int64(score)
	self.Data.MonthScore += int64(score)

	db.GetLogMgr().SqlPlayerLog(&db.SQL_PlayerLog{
		Id:         0,
		PlayerName: self.Data.PlayerName,
		PlayerId:   self.LiveId,
		RoomId:     int64(roomid),
		Time:       core.TimeServer().Unix(),
		Week:       self.Data.WeekScore,
		Month:      self.Data.MonthScore,
		Win:        self.Data.WinStreak,
		Desc:       desc,
		Platform:   network.PlatformName,
	})
	info := self.GetPlayerLiveInfo(roomid)
	if info != nil {
		self.SetFamilyScore(info, int64(score))
		configs.GetGameConfig().SetFamilyScore(info.FamilyName, self.LiveId, int64(score))
	}
}

// AddKill ! 增加击杀，汇总
func (self *LivePlayer) AddKill(roomId int32, kills []*Message.KillTypeDTO) {
	if kills == nil {
		return
	}
	room := GetRoomMgr().GetRoomById(roomId)
	if room == nil {
		return
	}
	info := self.GetPlayerLiveInfo(roomId)

	var scoreData = self.GetLiveScore(roomId)
	if scoreData != nil {
		len := len(kills)
		totalScore := 0
		totalPoolScore := int64(0)
		for i := 0; i < len; i++ {
			dto := kills[i]
			config := configs.GetActorScoreConfig().GetActorScoreConfig(int(dto.SoldierId))
			if config != nil {
				score := int(config.KillScore) * int(dto.KillCount)
				totalScore += score
				totalPoolScore += int64(int(config.PoolScore) * int(dto.KillCount))
			}
		}

		scoreData.GameScore += totalScore
		scoreData.KillScore += totalScore
		GetLiveTopMgr().AddScore(self.LiveId, int64(totalScore))
		self.Data.WeekScore += int64(totalScore)
		self.Data.MonthScore += int64(totalScore)
		room.SetScorePool(room.GetScorePool() + totalPoolScore)
		if info != nil {
			self.SetFamilyScore(info, int64(totalScore))
			configs.GetGameConfig().SetFamilyScore(info.FamilyName, self.LiveId, int64(totalScore))
		}

		// var playerData protocol.SyncPlayerDataS2C
		// playerData.PlayerId = int(self.LiveId)
		// playerData.Sync.GameScore = scoreData.GameScore
		// playerData.Sync.GameKillScore = scoreData.KillScore
		// playerData.Sync.GiftScore = scoreData.GiftScore
		// playerData.ScorePool = room.ScorePool
		if !utils.IsNil(room) && !utils.IsNil(room.Session) {
			playerData := new(Message.PlayerSyncDTO)
			playerData.GameScore = int32(scoreData.GameScore)
			playerData.GameKillScore = int32(scoreData.KillScore)
			playerData.GiftScore = int32(scoreData.GiftScore)
			Request.SyncPlayerDataRequest(room.Session, int32(self.LiveId), playerData, room.GetScorePool())
			// room.Session.SendNetMsg(protocol.Protocol_SyncPlayerData, utils.MsgToNetMsgBytes(&playerData, protocol.Protocol_SyncPlayerData, roomId))
		}
	}
}
func (self *LivePlayer) CreatePlayerDTO(liveScore *LiveScore, info *PlayerLiveInfo) *Message.PlayerDTO {
	data := self.Data
	return &Message.PlayerDTO{
		PlayerId:   utils.ClampInt64ToInt32(self.LiveId),
		HeaderUrl:  data.Icon,
		AnchorId:   0, // 默认为0，如果需要可以传入参数
		PlayerName: data.PlayerName,
		Sync: &Message.PlayerSyncDTO{
			GameScore:     0, // 游戏开始时为0
			GiftScore:     utils.ClampIntToInt32(liveScore.GiftScore),
			GameKillScore: 0, // 游戏开始时为0
			CurrentRank:   utils.ClampIntToInt32(data.MonthRank),
		},
		Rank: &Message.PlayerRankDTO{
			WinStreak:     utils.ClampIntToInt32(liveScore.WinStreak),
			CurrentRank:   utils.ClampIntToInt32(data.MonthRank),
			WeekRank:      utils.ClampIntToInt32(data.WeekRank),
			WeekScore:     data.WeekScore,
			MonthRank:     utils.ClampIntToInt32(data.MonthRank),
			MonthScore:    data.MonthScore,
			FamilyDetails: 0, // 默认为0
			GameScore:     utils.ClampIntToInt32(liveScore.GameScore),
			Title:         data.GetTitle(),
		},
		State: &Message.PlayerStateDTO{
			Name:   info.FamilyName,
			Family: "", // 如果需要可以添加
		},
	}
}
