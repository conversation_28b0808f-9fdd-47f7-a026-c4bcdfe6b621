package api

import (
	"net/http"
	"zone/lib/core"
	"zone/lib/storage"

	"github.com/gin-gonic/gin"
)

// ServerUrlHandler WebSocket API处理器
type ServerUrlHandler struct {
	Config        *core.Config
	ConfigService *storage.ServerUrlService
}

func NewServerUrlHandler(config *core.Config) *ServerUrlHandler {
	return &ServerUrlHandler{
		Config:        config,
		ConfigService: storage.GetServerUrlService(),
	}
}

type ServerUrlRequest struct {
	Platform int  `json:"platform"`
	Local    bool `json:"local"`
}
type ServerUrlResponse struct {
	WebSocket  string `json:"WebSocket"`
	Http       string `json:"Http"`
	UseEncrypt bool   `json:"useEncrypt"`
}

func (w *ServerUrlHandler) GetServerUrl(c *gin.Context) {
	// 解析请求参数
	var req ServerUrlRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		core.LogError("WebSocket URL请求参数错误:", err)
		c.J<PERSON>(http.StatusBadRequest, APIResponse{
			Code:    400,
			Message: "请求参数错误: " + err.Error(),
		})
		return
	}

	core.LogDebug("获取WebSocket URL请求: Platform=", req.Platform, "Local=", req.Local)

	// 检查配置服务是否已初始化
	if !w.ConfigService.IsInitialized() {
		core.LogError("ServerUrl配置服务未初始化，使用备用配置")
		var response ServerUrlResponse
		w.setFallbackUrls(&response, req.Platform, req.Local)
		c.JSON(http.StatusOK, response)
		return
	}

	var response ServerUrlResponse

	// 从配置服务获取URL
	config := w.ConfigService.GetConfig(req.Platform, req.Local)
	if config == nil {
		core.LogError("从配置服务获取ServerUrl配置失败")
		w.setFallbackUrls(&response, req.Platform, req.Local)
	} else {
		response.WebSocket = config.WebSocketURL
		response.Http = config.HttpURL
		response.UseEncrypt = config.UseEncrypt
	}

	// 如果从配置服务获取失败，使用备用逻辑
	if response.WebSocket == "" || response.Http == "" {
		core.LogError("从配置服务获取URL失败，使用备用配置")
		w.setFallbackUrls(&response, req.Platform, req.Local)
	}

	c.JSON(http.StatusOK, response)
}

// setFallbackUrls 设置备用URL配置
func (w *ServerUrlHandler) setFallbackUrls(response *ServerUrlResponse, platform int, isLocal bool) {
	// 使用原有的硬编码逻辑作为备用方案
	response.UseEncrypt = w.Config.UseEncrypt

	if isLocal {
		response.WebSocket = "ws://127.0.0.1:10027"
		response.Http = "http://127.0.0.1:10027"
	} else if platform == 0 {
		response.WebSocket = "wss://ysyq.ziyewangluo.com"
		response.Http = "https://ysyq.ziyewangluo.com"
	} else {
		response.WebSocket = "wss://kstest.ziyewangluo.com"
		response.Http = "https://ysyq.ziyewangluo.com"
	}
}
