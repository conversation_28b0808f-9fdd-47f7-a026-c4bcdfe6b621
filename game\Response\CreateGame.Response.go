// Code generated by pb_exporter.go. DO NOT EDIT.
// Generated at: 2025-06-12 16:35:42

package Response

import (
	"zone/game/Request"
	"zone/game/mods"

	// "zone/lib/core"
	"zone/lib/network"
	"zone/pb/Message"
)

func CreateGameResponse(session *network.Session, msg any) {
	// 将 msg 转换为对应的 C2S 消息结构指针
	createGame := msg.(*Message.CreateGameC2S)
	// core.LogDebug("CreateGameC2S:", createGame)
	var room = mods.GetSessionRoomMgr().GetRoom(session)
	if room == nil {
		Request.ErrorCodeRequest(session, 0, 0, "查找房间失败，请重启直播间")
		return
	}
	room.Create(createGame)
}
