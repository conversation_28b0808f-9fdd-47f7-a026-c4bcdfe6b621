// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.5.1
// source: gameMsg.proto

package Message

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CreateRoomC2S struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Token         string                 `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"` //  直播token
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateRoomC2S) Reset() {
	*x = CreateRoomC2S{}
	mi := &file_gameMsg_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateRoomC2S) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateRoomC2S) ProtoMessage() {}

func (x *CreateRoomC2S) ProtoReflect() protoreflect.Message {
	mi := &file_gameMsg_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateRoomC2S.ProtoReflect.Descriptor instead.
func (*CreateRoomC2S) Descriptor() ([]byte, []int) {
	return file_gameMsg_proto_rawDescGZIP(), []int{0}
}

func (x *CreateRoomC2S) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

type CreateRoomS2C struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RoomId        int32                  `protobuf:"varint,1,opt,name=roomId,proto3" json:"roomId,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateRoomS2C) Reset() {
	*x = CreateRoomS2C{}
	mi := &file_gameMsg_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateRoomS2C) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateRoomS2C) ProtoMessage() {}

func (x *CreateRoomS2C) ProtoReflect() protoreflect.Message {
	mi := &file_gameMsg_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateRoomS2C.ProtoReflect.Descriptor instead.
func (*CreateRoomS2C) Descriptor() ([]byte, []int) {
	return file_gameMsg_proto_rawDescGZIP(), []int{1}
}

func (x *CreateRoomS2C) GetRoomId() int32 {
	if x != nil {
		return x.RoomId
	}
	return 0
}

// class=SystemMsg
// cmd=3001 主播创建游戏
type CreateGameC2S struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MapId         int32                  `protobuf:"varint,1,opt,name=mapId,proto3" json:"mapId,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateGameC2S) Reset() {
	*x = CreateGameC2S{}
	mi := &file_gameMsg_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateGameC2S) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateGameC2S) ProtoMessage() {}

func (x *CreateGameC2S) ProtoReflect() protoreflect.Message {
	mi := &file_gameMsg_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateGameC2S.ProtoReflect.Descriptor instead.
func (*CreateGameC2S) Descriptor() ([]byte, []int) {
	return file_gameMsg_proto_rawDescGZIP(), []int{2}
}

func (x *CreateGameC2S) GetMapId() int32 {
	if x != nil {
		return x.MapId
	}
	return 0
}

type CreateGameS2C struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RoomId        int32                  `protobuf:"varint,1,opt,name=roomId,proto3" json:"roomId,omitempty"`       // 这里本来应该表示为RoomID，但由于弹幕游戏本身roomCode的存在,避免歧义
	ScorePool     int64                  `protobuf:"varint,2,opt,name=scorePool,proto3" json:"scorePool,omitempty"` // 积分池
	Seed          uint64                 `protobuf:"varint,3,opt,name=seed,proto3" json:"seed,omitempty"`
	WeekLastTime  uint64                 `protobuf:"varint,4,opt,name=weekLastTime,proto3" json:"weekLastTime,omitempty"`   // 周剩余时间
	MonthLastTime uint64                 `protobuf:"varint,5,opt,name=monthLastTime,proto3" json:"monthLastTime,omitempty"` // 月剩余时间
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateGameS2C) Reset() {
	*x = CreateGameS2C{}
	mi := &file_gameMsg_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateGameS2C) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateGameS2C) ProtoMessage() {}

func (x *CreateGameS2C) ProtoReflect() protoreflect.Message {
	mi := &file_gameMsg_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateGameS2C.ProtoReflect.Descriptor instead.
func (*CreateGameS2C) Descriptor() ([]byte, []int) {
	return file_gameMsg_proto_rawDescGZIP(), []int{3}
}

func (x *CreateGameS2C) GetRoomId() int32 {
	if x != nil {
		return x.RoomId
	}
	return 0
}

func (x *CreateGameS2C) GetScorePool() int64 {
	if x != nil {
		return x.ScorePool
	}
	return 0
}

func (x *CreateGameS2C) GetSeed() uint64 {
	if x != nil {
		return x.Seed
	}
	return 0
}

func (x *CreateGameS2C) GetWeekLastTime() uint64 {
	if x != nil {
		return x.WeekLastTime
	}
	return 0
}

func (x *CreateGameS2C) GetMonthLastTime() uint64 {
	if x != nil {
		return x.MonthLastTime
	}
	return 0
}

// class=SystemMsg
// cmd=3001 主播创建游戏
type InitGameOverC2S struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InitGameOverC2S) Reset() {
	*x = InitGameOverC2S{}
	mi := &file_gameMsg_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InitGameOverC2S) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitGameOverC2S) ProtoMessage() {}

func (x *InitGameOverC2S) ProtoReflect() protoreflect.Message {
	mi := &file_gameMsg_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitGameOverC2S.ProtoReflect.Descriptor instead.
func (*InitGameOverC2S) Descriptor() ([]byte, []int) {
	return file_gameMsg_proto_rawDescGZIP(), []int{4}
}

type InitGameOverS2C struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InitGameOverS2C) Reset() {
	*x = InitGameOverS2C{}
	mi := &file_gameMsg_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InitGameOverS2C) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitGameOverS2C) ProtoMessage() {}

func (x *InitGameOverS2C) ProtoReflect() protoreflect.Message {
	mi := &file_gameMsg_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitGameOverS2C.ProtoReflect.Descriptor instead.
func (*InitGameOverS2C) Descriptor() ([]byte, []int) {
	return file_gameMsg_proto_rawDescGZIP(), []int{5}
}

type ReloginGameC2S struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RoomID        int32                  `protobuf:"varint,1,opt,name=roomID,proto3" json:"roomID,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReloginGameC2S) Reset() {
	*x = ReloginGameC2S{}
	mi := &file_gameMsg_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReloginGameC2S) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReloginGameC2S) ProtoMessage() {}

func (x *ReloginGameC2S) ProtoReflect() protoreflect.Message {
	mi := &file_gameMsg_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReloginGameC2S.ProtoReflect.Descriptor instead.
func (*ReloginGameC2S) Descriptor() ([]byte, []int) {
	return file_gameMsg_proto_rawDescGZIP(), []int{6}
}

func (x *ReloginGameC2S) GetRoomID() int32 {
	if x != nil {
		return x.RoomID
	}
	return 0
}

type ReloginGameS2C struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReloginGameS2C) Reset() {
	*x = ReloginGameS2C{}
	mi := &file_gameMsg_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReloginGameS2C) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReloginGameS2C) ProtoMessage() {}

func (x *ReloginGameS2C) ProtoReflect() protoreflect.Message {
	mi := &file_gameMsg_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReloginGameS2C.ProtoReflect.Descriptor instead.
func (*ReloginGameS2C) Descriptor() ([]byte, []int) {
	return file_gameMsg_proto_rawDescGZIP(), []int{7}
}

// class=SystemMsg
// cmd=3002 进入PK池
type MatchMutiGameC2S struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MatchMutiGameC2S) Reset() {
	*x = MatchMutiGameC2S{}
	mi := &file_gameMsg_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MatchMutiGameC2S) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MatchMutiGameC2S) ProtoMessage() {}

func (x *MatchMutiGameC2S) ProtoReflect() protoreflect.Message {
	mi := &file_gameMsg_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MatchMutiGameC2S.ProtoReflect.Descriptor instead.
func (*MatchMutiGameC2S) Descriptor() ([]byte, []int) {
	return file_gameMsg_proto_rawDescGZIP(), []int{8}
}

type MatchMutiGameS2C struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MatchMutiGameS2C) Reset() {
	*x = MatchMutiGameS2C{}
	mi := &file_gameMsg_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MatchMutiGameS2C) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MatchMutiGameS2C) ProtoMessage() {}

func (x *MatchMutiGameS2C) ProtoReflect() protoreflect.Message {
	mi := &file_gameMsg_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MatchMutiGameS2C.ProtoReflect.Descriptor instead.
func (*MatchMutiGameS2C) Descriptor() ([]byte, []int) {
	return file_gameMsg_proto_rawDescGZIP(), []int{9}
}

type EnterMutiGameC2S struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EnterMutiGameC2S) Reset() {
	*x = EnterMutiGameC2S{}
	mi := &file_gameMsg_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EnterMutiGameC2S) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnterMutiGameC2S) ProtoMessage() {}

func (x *EnterMutiGameC2S) ProtoReflect() protoreflect.Message {
	mi := &file_gameMsg_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnterMutiGameC2S.ProtoReflect.Descriptor instead.
func (*EnterMutiGameC2S) Descriptor() ([]byte, []int) {
	return file_gameMsg_proto_rawDescGZIP(), []int{10}
}

type EnterMutiGameS2C struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MutiGameID    int32                  `protobuf:"varint,1,opt,name=MutiGameID,proto3" json:"MutiGameID,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EnterMutiGameS2C) Reset() {
	*x = EnterMutiGameS2C{}
	mi := &file_gameMsg_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EnterMutiGameS2C) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnterMutiGameS2C) ProtoMessage() {}

func (x *EnterMutiGameS2C) ProtoReflect() protoreflect.Message {
	mi := &file_gameMsg_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnterMutiGameS2C.ProtoReflect.Descriptor instead.
func (*EnterMutiGameS2C) Descriptor() ([]byte, []int) {
	return file_gameMsg_proto_rawDescGZIP(), []int{11}
}

func (x *EnterMutiGameS2C) GetMutiGameID() int32 {
	if x != nil {
		return x.MutiGameID
	}
	return 0
}

// cmd=3005 同步帧数
type SyncFrameC2S struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SyncFrameC2S) Reset() {
	*x = SyncFrameC2S{}
	mi := &file_gameMsg_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SyncFrameC2S) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncFrameC2S) ProtoMessage() {}

func (x *SyncFrameC2S) ProtoReflect() protoreflect.Message {
	mi := &file_gameMsg_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncFrameC2S.ProtoReflect.Descriptor instead.
func (*SyncFrameC2S) Descriptor() ([]byte, []int) {
	return file_gameMsg_proto_rawDescGZIP(), []int{12}
}

type SyncFrameS2C struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CurFrame      int32                  `protobuf:"varint,1,opt,name=curFrame,proto3" json:"curFrame,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SyncFrameS2C) Reset() {
	*x = SyncFrameS2C{}
	mi := &file_gameMsg_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SyncFrameS2C) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncFrameS2C) ProtoMessage() {}

func (x *SyncFrameS2C) ProtoReflect() protoreflect.Message {
	mi := &file_gameMsg_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncFrameS2C.ProtoReflect.Descriptor instead.
func (*SyncFrameS2C) Descriptor() ([]byte, []int) {
	return file_gameMsg_proto_rawDescGZIP(), []int{13}
}

func (x *SyncFrameS2C) GetCurFrame() int32 {
	if x != nil {
		return x.CurFrame
	}
	return 0
}

// cmd=3006 同步玩家击杀
type PlayerKillCountC2S struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PlayerKills   []*PlayerKillCountDTO  `protobuf:"bytes,1,rep,name=playerKills,proto3" json:"playerKills,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PlayerKillCountC2S) Reset() {
	*x = PlayerKillCountC2S{}
	mi := &file_gameMsg_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PlayerKillCountC2S) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlayerKillCountC2S) ProtoMessage() {}

func (x *PlayerKillCountC2S) ProtoReflect() protoreflect.Message {
	mi := &file_gameMsg_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlayerKillCountC2S.ProtoReflect.Descriptor instead.
func (*PlayerKillCountC2S) Descriptor() ([]byte, []int) {
	return file_gameMsg_proto_rawDescGZIP(), []int{14}
}

func (x *PlayerKillCountC2S) GetPlayerKills() []*PlayerKillCountDTO {
	if x != nil {
		return x.PlayerKills
	}
	return nil
}

type PlayerKillCountS2C struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Time          int32                  `protobuf:"varint,1,opt,name=time,proto3" json:"time,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PlayerKillCountS2C) Reset() {
	*x = PlayerKillCountS2C{}
	mi := &file_gameMsg_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PlayerKillCountS2C) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlayerKillCountS2C) ProtoMessage() {}

func (x *PlayerKillCountS2C) ProtoReflect() protoreflect.Message {
	mi := &file_gameMsg_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlayerKillCountS2C.ProtoReflect.Descriptor instead.
func (*PlayerKillCountS2C) Descriptor() ([]byte, []int) {
	return file_gameMsg_proto_rawDescGZIP(), []int{15}
}

func (x *PlayerKillCountS2C) GetTime() int32 {
	if x != nil {
		return x.Time
	}
	return 0
}

// cmd=3007 游戏开始
type GameStartC2S struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GameStartC2S) Reset() {
	*x = GameStartC2S{}
	mi := &file_gameMsg_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GameStartC2S) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GameStartC2S) ProtoMessage() {}

func (x *GameStartC2S) ProtoReflect() protoreflect.Message {
	mi := &file_gameMsg_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GameStartC2S.ProtoReflect.Descriptor instead.
func (*GameStartC2S) Descriptor() ([]byte, []int) {
	return file_gameMsg_proto_rawDescGZIP(), []int{16}
}

type GameStartS2C struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GameStartS2C) Reset() {
	*x = GameStartS2C{}
	mi := &file_gameMsg_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GameStartS2C) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GameStartS2C) ProtoMessage() {}

func (x *GameStartS2C) ProtoReflect() protoreflect.Message {
	mi := &file_gameMsg_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GameStartS2C.ProtoReflect.Descriptor instead.
func (*GameStartS2C) Descriptor() ([]byte, []int) {
	return file_gameMsg_proto_rawDescGZIP(), []int{17}
}

// cmd=3008 游戏结束
type GameOverC2S struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	WinPlayers    []int32                `protobuf:"varint,1,rep,packed,name=winPlayers,proto3" json:"winPlayers,omitempty"` // 获胜玩家id
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GameOverC2S) Reset() {
	*x = GameOverC2S{}
	mi := &file_gameMsg_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GameOverC2S) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GameOverC2S) ProtoMessage() {}

func (x *GameOverC2S) ProtoReflect() protoreflect.Message {
	mi := &file_gameMsg_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GameOverC2S.ProtoReflect.Descriptor instead.
func (*GameOverC2S) Descriptor() ([]byte, []int) {
	return file_gameMsg_proto_rawDescGZIP(), []int{18}
}

func (x *GameOverC2S) GetWinPlayers() []int32 {
	if x != nil {
		return x.WinPlayers
	}
	return nil
}

type GameOverS2C struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Settlement    []*PlayerSettlementDTO `protobuf:"bytes,1,rep,name=settlement,proto3" json:"settlement,omitempty"` // 结算信息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GameOverS2C) Reset() {
	*x = GameOverS2C{}
	mi := &file_gameMsg_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GameOverS2C) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GameOverS2C) ProtoMessage() {}

func (x *GameOverS2C) ProtoReflect() protoreflect.Message {
	mi := &file_gameMsg_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GameOverS2C.ProtoReflect.Descriptor instead.
func (*GameOverS2C) Descriptor() ([]byte, []int) {
	return file_gameMsg_proto_rawDescGZIP(), []int{19}
}

func (x *GameOverS2C) GetSettlement() []*PlayerSettlementDTO {
	if x != nil {
		return x.Settlement
	}
	return nil
}

// cmd = 3011
type GetScoreRankC2S struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Type          int32                  `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"` // 2:周 3:月 4:连胜 5:招兵
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetScoreRankC2S) Reset() {
	*x = GetScoreRankC2S{}
	mi := &file_gameMsg_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetScoreRankC2S) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetScoreRankC2S) ProtoMessage() {}

func (x *GetScoreRankC2S) ProtoReflect() protoreflect.Message {
	mi := &file_gameMsg_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetScoreRankC2S.ProtoReflect.Descriptor instead.
func (*GetScoreRankC2S) Descriptor() ([]byte, []int) {
	return file_gameMsg_proto_rawDescGZIP(), []int{20}
}

func (x *GetScoreRankC2S) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

type GetScoreRankS2C struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Rank          []*ScoreRankDTO        `protobuf:"bytes,1,rep,name=rank,proto3" json:"rank,omitempty"`
	Type          int32                  `protobuf:"varint,2,opt,name=type,proto3" json:"type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetScoreRankS2C) Reset() {
	*x = GetScoreRankS2C{}
	mi := &file_gameMsg_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetScoreRankS2C) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetScoreRankS2C) ProtoMessage() {}

func (x *GetScoreRankS2C) ProtoReflect() protoreflect.Message {
	mi := &file_gameMsg_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetScoreRankS2C.ProtoReflect.Descriptor instead.
func (*GetScoreRankS2C) Descriptor() ([]byte, []int) {
	return file_gameMsg_proto_rawDescGZIP(), []int{21}
}

func (x *GetScoreRankS2C) GetRank() []*ScoreRankDTO {
	if x != nil {
		return x.Rank
	}
	return nil
}

func (x *GetScoreRankS2C) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

// cmd = 3012
type FamilyRankC2S struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FamilyRankC2S) Reset() {
	*x = FamilyRankC2S{}
	mi := &file_gameMsg_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FamilyRankC2S) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FamilyRankC2S) ProtoMessage() {}

func (x *FamilyRankC2S) ProtoReflect() protoreflect.Message {
	mi := &file_gameMsg_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FamilyRankC2S.ProtoReflect.Descriptor instead.
func (*FamilyRankC2S) Descriptor() ([]byte, []int) {
	return file_gameMsg_proto_rawDescGZIP(), []int{22}
}

type FamilyRankS2C struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	FamilyRank    []*FamilyRankDTO       `protobuf:"bytes,1,rep,name=familyRank,proto3" json:"familyRank,omitempty"` // 家族排行榜
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FamilyRankS2C) Reset() {
	*x = FamilyRankS2C{}
	mi := &file_gameMsg_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FamilyRankS2C) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FamilyRankS2C) ProtoMessage() {}

func (x *FamilyRankS2C) ProtoReflect() protoreflect.Message {
	mi := &file_gameMsg_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FamilyRankS2C.ProtoReflect.Descriptor instead.
func (*FamilyRankS2C) Descriptor() ([]byte, []int) {
	return file_gameMsg_proto_rawDescGZIP(), []int{23}
}

func (x *FamilyRankS2C) GetFamilyRank() []*FamilyRankDTO {
	if x != nil {
		return x.FamilyRank
	}
	return nil
}

type SyncWinStreakPoolC2S struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SyncWinStreakPoolC2S) Reset() {
	*x = SyncWinStreakPoolC2S{}
	mi := &file_gameMsg_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SyncWinStreakPoolC2S) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncWinStreakPoolC2S) ProtoMessage() {}

func (x *SyncWinStreakPoolC2S) ProtoReflect() protoreflect.Message {
	mi := &file_gameMsg_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncWinStreakPoolC2S.ProtoReflect.Descriptor instead.
func (*SyncWinStreakPoolC2S) Descriptor() ([]byte, []int) {
	return file_gameMsg_proto_rawDescGZIP(), []int{24}
}

type SyncWinStreakPoolS2C struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	WinStreakPool int32                  `protobuf:"varint,1,opt,name=winStreakPool,proto3" json:"winStreakPool,omitempty"` // 连胜池
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SyncWinStreakPoolS2C) Reset() {
	*x = SyncWinStreakPoolS2C{}
	mi := &file_gameMsg_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SyncWinStreakPoolS2C) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncWinStreakPoolS2C) ProtoMessage() {}

func (x *SyncWinStreakPoolS2C) ProtoReflect() protoreflect.Message {
	mi := &file_gameMsg_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncWinStreakPoolS2C.ProtoReflect.Descriptor instead.
func (*SyncWinStreakPoolS2C) Descriptor() ([]byte, []int) {
	return file_gameMsg_proto_rawDescGZIP(), []int{25}
}

func (x *SyncWinStreakPoolS2C) GetWinStreakPool() int32 {
	if x != nil {
		return x.WinStreakPool
	}
	return 0
}

var File_gameMsg_proto protoreflect.FileDescriptor

const file_gameMsg_proto_rawDesc = "" +
	"\n" +
	"\rgameMsg.proto\x12\n" +
	"PB.Message\x1a\fcommon.proto\"%\n" +
	"\rCreateRoomC2S\x12\x14\n" +
	"\x05token\x18\x01 \x01(\tR\x05token\"'\n" +
	"\rCreateRoomS2C\x12\x16\n" +
	"\x06roomId\x18\x01 \x01(\x05R\x06roomId\"%\n" +
	"\rCreateGameC2S\x12\x14\n" +
	"\x05mapId\x18\x01 \x01(\x05R\x05mapId\"\xa3\x01\n" +
	"\rCreateGameS2C\x12\x16\n" +
	"\x06roomId\x18\x01 \x01(\x05R\x06roomId\x12\x1c\n" +
	"\tscorePool\x18\x02 \x01(\x03R\tscorePool\x12\x12\n" +
	"\x04seed\x18\x03 \x01(\x04R\x04seed\x12\"\n" +
	"\fweekLastTime\x18\x04 \x01(\x04R\fweekLastTime\x12$\n" +
	"\rmonthLastTime\x18\x05 \x01(\x04R\rmonthLastTime\"\x11\n" +
	"\x0fInitGameOverC2S\"\x11\n" +
	"\x0fInitGameOverS2C\"(\n" +
	"\x0eReloginGameC2S\x12\x16\n" +
	"\x06roomID\x18\x01 \x01(\x05R\x06roomID\"\x10\n" +
	"\x0eReloginGameS2C\"\x12\n" +
	"\x10MatchMutiGameC2S\"\x12\n" +
	"\x10MatchMutiGameS2C\"\x12\n" +
	"\x10EnterMutiGameC2S\"2\n" +
	"\x10EnterMutiGameS2C\x12\x1e\n" +
	"\n" +
	"MutiGameID\x18\x01 \x01(\x05R\n" +
	"MutiGameID\"\x0e\n" +
	"\fSyncFrameC2S\"*\n" +
	"\fSyncFrameS2C\x12\x1a\n" +
	"\bcurFrame\x18\x01 \x01(\x05R\bcurFrame\"V\n" +
	"\x12PlayerKillCountC2S\x12@\n" +
	"\vplayerKills\x18\x01 \x03(\v2\x1e.PB.Message.PlayerKillCountDTOR\vplayerKills\"(\n" +
	"\x12PlayerKillCountS2C\x12\x12\n" +
	"\x04time\x18\x01 \x01(\x05R\x04time\"\x0e\n" +
	"\fGameStartC2S\"\x0e\n" +
	"\fGameStartS2C\"-\n" +
	"\vGameOverC2S\x12\x1e\n" +
	"\n" +
	"winPlayers\x18\x01 \x03(\x05R\n" +
	"winPlayers\"N\n" +
	"\vGameOverS2C\x12?\n" +
	"\n" +
	"settlement\x18\x01 \x03(\v2\x1f.PB.Message.PlayerSettlementDTOR\n" +
	"settlement\"%\n" +
	"\x0fGetScoreRankC2S\x12\x12\n" +
	"\x04type\x18\x01 \x01(\x05R\x04type\"S\n" +
	"\x0fGetScoreRankS2C\x12,\n" +
	"\x04rank\x18\x01 \x03(\v2\x18.PB.Message.ScoreRankDTOR\x04rank\x12\x12\n" +
	"\x04type\x18\x02 \x01(\x05R\x04type\"\x0f\n" +
	"\rFamilyRankC2S\"J\n" +
	"\rFamilyRankS2C\x129\n" +
	"\n" +
	"familyRank\x18\x01 \x03(\v2\x19.PB.Message.FamilyRankDTOR\n" +
	"familyRank\"\x16\n" +
	"\x14SyncWinStreakPoolC2S\"<\n" +
	"\x14SyncWinStreakPoolS2C\x12$\n" +
	"\rwinStreakPool\x18\x01 \x01(\x05R\rwinStreakPoolb\x06proto3"

var (
	file_gameMsg_proto_rawDescOnce sync.Once
	file_gameMsg_proto_rawDescData []byte
)

func file_gameMsg_proto_rawDescGZIP() []byte {
	file_gameMsg_proto_rawDescOnce.Do(func() {
		file_gameMsg_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_gameMsg_proto_rawDesc), len(file_gameMsg_proto_rawDesc)))
	})
	return file_gameMsg_proto_rawDescData
}

var file_gameMsg_proto_msgTypes = make([]protoimpl.MessageInfo, 26)
var file_gameMsg_proto_goTypes = []any{
	(*CreateRoomC2S)(nil),        // 0: PB.Message.CreateRoomC2S
	(*CreateRoomS2C)(nil),        // 1: PB.Message.CreateRoomS2C
	(*CreateGameC2S)(nil),        // 2: PB.Message.CreateGameC2S
	(*CreateGameS2C)(nil),        // 3: PB.Message.CreateGameS2C
	(*InitGameOverC2S)(nil),      // 4: PB.Message.InitGameOverC2S
	(*InitGameOverS2C)(nil),      // 5: PB.Message.InitGameOverS2C
	(*ReloginGameC2S)(nil),       // 6: PB.Message.ReloginGameC2S
	(*ReloginGameS2C)(nil),       // 7: PB.Message.ReloginGameS2C
	(*MatchMutiGameC2S)(nil),     // 8: PB.Message.MatchMutiGameC2S
	(*MatchMutiGameS2C)(nil),     // 9: PB.Message.MatchMutiGameS2C
	(*EnterMutiGameC2S)(nil),     // 10: PB.Message.EnterMutiGameC2S
	(*EnterMutiGameS2C)(nil),     // 11: PB.Message.EnterMutiGameS2C
	(*SyncFrameC2S)(nil),         // 12: PB.Message.SyncFrameC2S
	(*SyncFrameS2C)(nil),         // 13: PB.Message.SyncFrameS2C
	(*PlayerKillCountC2S)(nil),   // 14: PB.Message.PlayerKillCountC2S
	(*PlayerKillCountS2C)(nil),   // 15: PB.Message.PlayerKillCountS2C
	(*GameStartC2S)(nil),         // 16: PB.Message.GameStartC2S
	(*GameStartS2C)(nil),         // 17: PB.Message.GameStartS2C
	(*GameOverC2S)(nil),          // 18: PB.Message.GameOverC2S
	(*GameOverS2C)(nil),          // 19: PB.Message.GameOverS2C
	(*GetScoreRankC2S)(nil),      // 20: PB.Message.GetScoreRankC2S
	(*GetScoreRankS2C)(nil),      // 21: PB.Message.GetScoreRankS2C
	(*FamilyRankC2S)(nil),        // 22: PB.Message.FamilyRankC2S
	(*FamilyRankS2C)(nil),        // 23: PB.Message.FamilyRankS2C
	(*SyncWinStreakPoolC2S)(nil), // 24: PB.Message.SyncWinStreakPoolC2S
	(*SyncWinStreakPoolS2C)(nil), // 25: PB.Message.SyncWinStreakPoolS2C
	(*PlayerKillCountDTO)(nil),   // 26: PB.Message.PlayerKillCountDTO
	(*PlayerSettlementDTO)(nil),  // 27: PB.Message.PlayerSettlementDTO
	(*ScoreRankDTO)(nil),         // 28: PB.Message.ScoreRankDTO
	(*FamilyRankDTO)(nil),        // 29: PB.Message.FamilyRankDTO
}
var file_gameMsg_proto_depIdxs = []int32{
	26, // 0: PB.Message.PlayerKillCountC2S.playerKills:type_name -> PB.Message.PlayerKillCountDTO
	27, // 1: PB.Message.GameOverS2C.settlement:type_name -> PB.Message.PlayerSettlementDTO
	28, // 2: PB.Message.GetScoreRankS2C.rank:type_name -> PB.Message.ScoreRankDTO
	29, // 3: PB.Message.FamilyRankS2C.familyRank:type_name -> PB.Message.FamilyRankDTO
	4,  // [4:4] is the sub-list for method output_type
	4,  // [4:4] is the sub-list for method input_type
	4,  // [4:4] is the sub-list for extension type_name
	4,  // [4:4] is the sub-list for extension extendee
	0,  // [0:4] is the sub-list for field type_name
}

func init() { file_gameMsg_proto_init() }
func file_gameMsg_proto_init() {
	if File_gameMsg_proto != nil {
		return
	}
	file_common_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_gameMsg_proto_rawDesc), len(file_gameMsg_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   26,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_gameMsg_proto_goTypes,
		DependencyIndexes: file_gameMsg_proto_depIdxs,
		MessageInfos:      file_gameMsg_proto_msgTypes,
	}.Build()
	File_gameMsg_proto = out.File
	file_gameMsg_proto_goTypes = nil
	file_gameMsg_proto_depIdxs = nil
}
