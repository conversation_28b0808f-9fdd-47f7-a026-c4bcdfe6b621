package mods

import (
	"zone/game/models"
	"zone/game/platform/bytedance"
	"zone/game/platform/kuaishou"
	"zone/lib/network"
	"zone/lib/payload"
	"zone/pb/Message"
)

// ManagerRegistry 管理器注册表，用于避免循环导入
// 这个文件提供了访问各种管理器的全局函数

// 管理器接口定义
type RoomManagerInterface interface {
	GetRoomById(roomId int32) *LiveRoom
	GetRoomByPlatformId(roomId string) *LiveRoom
	CreateRoomByPlatformId(roomId string) *LiveRoom
	CreateRoom() *LiveRoom
	CreateRoomWithGameType(gameType GameType) *LiveRoom
	ReStoreLiveRoom(month bool)
	SetAnnouncement(content string)
	GetRoomRoundID() string
	// 添加缺失的字段访问方法
	GetRoomMap() interface{}
	GetRoomPlatformMap() interface{}
	// 多人游戏支持方法
	SwitchRoomGameType(roomId int32, gameType GameType) error
	GetRoomGameType(roomId int32) GameType
}

type LiveManagerInterface interface {
	GetData()
	Save()
	ResetDataWeekly()
	ResetDataCall()
	ResetDataMonthly()
	GetLivePlayer(liveId int64) *LivePlayer
	GetLivePlayerByOpenId(openId string) *LivePlayer
	GetLiveDataByOpenId(openId string) *models.LivePlayerDB
	GetLiveData(liveId int64) *models.LivePlayerDB
	NewLiveData(platformId, uname string) *models.LivePlayerDB
	NewLivePlayer(platformId, uname string) *LivePlayer
	LoadLivePlayerDB(liveId int64) *models.LivePlayerDB
	AddComment(roomDYId string, comment []byte) bool
	AddGift(roomDYId string, comment []*payload.PayloadData, msgId string) bool
	AddFans(roomDYId string, comment []*payload.PayloadData, msgId string) bool
	AddLike(roomDYId string, comment []byte) bool
}

type LiveTopManagerInterface interface {
	AddScore(liveId int64, score int64)
	AddCall(liveId int64, call int)
	SetWin(liveId int64, win int)
	NewTopNode(liveId int64) interface{}
	RankSort()
	GetTop_Week(liveId int64) int
	GetTop_Month(liveId int64) int
	GetTop_Win(liveId int64) int
	GetWeekRank() *Message.GetScoreRankS2C
	GetMonthRank() *Message.GetScoreRankS2C
	GetWinRank() *Message.GetScoreRankS2C
	GetCallRank() *Message.GetScoreRankS2C
	ClearWeekData()
	ClearMonthData()
	ClearCallData()
}

type SeasonManagerInterface interface {
	GetSeason()
	CheckPlayer(playerId int, score int64)
	SetPlayer(playerId int, score int64)
	AddSeason()
}

type AnchorManagerInterface interface {
	GetData()
	Save()
	AnchorOnLine(AnchorOpenId, AvatarUrl, NickName string) *models.AnchorDB
}

type LiveInfoManagerInterface interface {
	AddDYLiveReq(roomId string, callBack func(any), failcallBack func(any), msgType string, liveType int)
	AddKSLiveReq(roomId string, callBack func(any), failcallBack func(any), reqType int, actionType string, data interface{})
	Reporting(roomId string, status int, msgId string, msgType string)
	CheckKSFailData(roomId string, callBack func([]*kuaishou.GiftQueryItem))
	CheckDYFailData(roomId, msgType string, page, size int, callBack func(bool, []*bytedance.JsFailData))
}

type GiftRecordManagerInterface interface {
	CreateRecord(roomId int32, anchorId string, data *payload.PayloadData)
	DoRecord(msgId string)
	ExRecord(msgId string)
}

type SessionRoomManagerInterface interface {
	GetRoom(session *network.Session) *LiveRoom
	OnCloseSession(session *network.Session)
	Add(session *network.Session, room *LiveRoom)
	Delete(session *network.Session)
}

type MultiGameMatchManagerInterface interface {
	AddSession(session *network.Session)
}

type MultiGameManagerInterface interface {
	GetMultiGameById(gamerId int32) *LiveMultiGame
}

// 全局管理器实例（由manager包设置）
var (
	roomManager           RoomManagerInterface
	liveManager           LiveManagerInterface
	liveTopManager        LiveTopManagerInterface
	seasonManager         SeasonManagerInterface
	anchorManager         AnchorManagerInterface
	liveInfoManager       LiveInfoManagerInterface
	giftRecordManager     GiftRecordManagerInterface
	sessionRoomManager    SessionRoomManagerInterface
	multiGameMatchManager MultiGameMatchManagerInterface
	multiGameManager      MultiGameManagerInterface
)

// 注册函数（由manager包调用）
func RegisterRoomManager(mgr RoomManagerInterface) {
	roomManager = mgr
}

func RegisterMultiGameManager(mgr MultiGameManagerInterface) {
	multiGameManager = mgr
}

func RegisterLiveManager(mgr LiveManagerInterface) {
	liveManager = mgr
}

func RegisterLiveTopManager(mgr LiveTopManagerInterface) {
	liveTopManager = mgr
}

func RegisterSeasonManager(mgr SeasonManagerInterface) {
	seasonManager = mgr
}

func RegisterAnchorManager(mgr AnchorManagerInterface) {
	anchorManager = mgr
}

func RegisterLiveInfoManager(mgr LiveInfoManagerInterface) {
	liveInfoManager = mgr
}

func RegisterGiftRecordManager(mgr GiftRecordManagerInterface) {
	giftRecordManager = mgr
}

func RegisterSessionRoomManager(mgr SessionRoomManagerInterface) {
	sessionRoomManager = mgr
}

func RegisterMultiGameMatchManager(mgr MultiGameMatchManagerInterface) {
	multiGameMatchManager = mgr
}

// 全局访问函数（供mods包内部使用）
func GetRoomMgr() RoomManagerInterface {
	if roomManager == nil {
		panic("RoomManager not registered! Please ensure manager.GetRoomMgr() is called during application initialization.")
	}
	return roomManager
}

func GetLiveMgr() LiveManagerInterface {
	if liveManager == nil {
		panic("LiveManager not registered! Please ensure manager.GetLiveMgr() is called during application initialization.")
	}
	return liveManager
}

func GetLiveTopMgr() LiveTopManagerInterface {
	if liveTopManager == nil {
		panic("LiveTopManager not registered! Please ensure manager.GetLiveTopMgr() is called during application initialization.")
	}
	return liveTopManager
}

func GetSeasonMgr() SeasonManagerInterface {
	return seasonManager
}

func GetAnchorMgr() AnchorManagerInterface {
	return anchorManager
}

func GetLiveInfoMgr() LiveInfoManagerInterface {
	if liveInfoManager == nil {
		// 如果管理器未注册，尝试初始化
		// 这是一个安全措施，但正常情况下应该在应用启动时注册
		panic("LiveInfoManager not registered! Please ensure manager.GetLiveInfoMgr() is called during application initialization.")
	}
	return liveInfoManager
}

func GetGiftRecordMgr() GiftRecordManagerInterface {
	return giftRecordManager
}

func GetSessionRoomMgr() SessionRoomManagerInterface {
	if sessionRoomManager == nil {
		panic("SessionRoomManager not registered! Please ensure manager.GetSessionRoomMgr() is called during application initialization.")
	}
	return sessionRoomManager
}

func GetMultiGameMatchMgr() MultiGameMatchManagerInterface {
	if multiGameMatchManager == nil {
		panic("MultiGameManager not registered! Please ensure manager.GetMultiGameMatchMgr() is called during application initialization.")
	}
	return multiGameMatchManager
}

func GetMultiGameMgr() MultiGameManagerInterface {
	if multiGameManager == nil {
		panic("MultiGameManager not registered! Please ensure manager.GetMultiGameMgr() is called during application initialization.")
	}
	return multiGameManager
}
