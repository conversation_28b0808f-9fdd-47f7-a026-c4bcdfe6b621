package mods

import (
	"zone/game/platform/bytedance"
	"zone/lib/core"
	"zone/lib/db"
	"zone/lib/network"
	"zone/lib/payload"
	"zone/pb/Message"
)

// DoComment 处理评论
func (game *LiveGame) DoComment(payload *payload.PayloadData) {
	db.GetLogMgr().SqlCommentLog(&db.SQL_CommentLog{
		Id:         0,
		PlayerName: payload.Nickname,
		RoomId:     int64(game.GetRoomId()),
		PlayerId:   payload.OpenId,
		AnchorId:   game.GetAnchorId(),
		Comment:    payload.Content,
		MsgId:      payload.MsgId,
		Time:       payload.Timestamp,
		Platform:   network.PlatformName,
	})
	core.LogInfo("DoComment:", payload.MsgId, "Comment:", payload.Content)

	// 始终执行任务去重检查和报告（移除测试模式限制）
	if !game.Room.TaskManager.CheckAndMarkCommentTask(game.GetRoomId(), payload.MsgId) {
		core.LogError("DoComment重复消息，跳过处理:", payload.MsgId, "Comment:", payload.Content)
		return // 重复消息，跳过处理
	}

	core.LogInfo("DoComment处理:", payload.MsgId, "Comment:", payload.Content)
	GetLiveInfoMgr().Reporting(game.GetRoomPlatformId(), 1, payload.MsgId, bytedance.LIVE_MSG_TYPE_COMMENT)
}

// DoGift 处理礼物
func (game *LiveGame) DoGift(payload *payload.PayloadData) {
	// 始终执行任务去重检查和报告（移除测试模式限制）
	if !game.Room.TaskManager.CheckAndMarkGiftTask(game.GetRoomId(), payload.MsgId) {
		return // 重复消息，跳过处理
	}
	GetLiveInfoMgr().Reporting(game.GetRoomPlatformId(), 1, payload.MsgId, bytedance.LIVE_MSG_TYPE_GIFT)
	livePlayer := GetLiveMgr().GetLivePlayerByOpenId(payload.OpenId)
	if livePlayer == nil {
		//! 还没有加入游戏，加入人少的一方，一样，则加入到
		liveData := GetLiveMgr().GetLiveDataByOpenId(payload.OpenId)
		if liveData == nil {
			liveData = GetLiveMgr().NewLiveData(payload.OpenId, payload.Nickname)
			liveData.Icon = payload.AvatarUrl
		} else {
			liveData.Icon = payload.AvatarUrl
			liveData.PlayerName = payload.Nickname
		}

		livePlayer = GetLiveMgr().GetLivePlayer(liveData.PlayerId)
		if livePlayer == nil {
			livePlayer = GetLiveMgr().NewLivePlayer(liveData.OpenId, liveData.PlayerName)
		}
	}

	livePlayer.AddGift(game.GetRoomId(), payload)
	GetLiveInfoMgr().Reporting(game.GetRoomPlatformId(), 2, payload.MsgId, bytedance.LIVE_MSG_TYPE_GIFT)
}

// DoFans 处理粉丝
func (game *LiveGame) DoFans(payload *payload.PayloadData) {
	// 始终执行任务去重检查和报告（移除测试模式限制）
	if !game.Room.TaskManager.CheckAndMarkFansTask(game.GetRoomId(), payload.MsgId) {
		return // 重复消息，跳过处理
	}
	GetLiveInfoMgr().Reporting(game.GetRoomPlatformId(), 1, payload.MsgId, bytedance.LIVE_MSG_TYPE_FANS)
	livePlayer := GetLiveMgr().GetLivePlayerByOpenId(payload.OpenId)
	if livePlayer == nil {
		liveData := GetLiveMgr().GetLiveDataByOpenId(payload.OpenId)
		if liveData == nil {
			liveData = GetLiveMgr().NewLiveData(payload.OpenId, payload.Nickname)
			liveData.Icon = payload.AvatarUrl
		} else {
			liveData.Icon = payload.AvatarUrl
			liveData.PlayerName = payload.Nickname
		}

		livePlayer = GetLiveMgr().GetLivePlayer(liveData.PlayerId)
		if livePlayer == nil {
			livePlayer = GetLiveMgr().NewLivePlayer(liveData.OpenId, liveData.PlayerName)
		}
	}

	livePlayer.AddGiftById(game.GetRoomId(), 6, 1)
	GetLiveInfoMgr().Reporting(game.GetRoomPlatformId(), 2, payload.MsgId, bytedance.LIVE_MSG_TYPE_FANS)
}

// DoLike 处理点赞
func (game *LiveGame) DoLike(payload *payload.PayloadData) {
	// 始终执行任务去重检查和报告（移除测试模式限制）
	if !game.Room.TaskManager.CheckAndMarkLikeTask(game.GetRoomId(), payload.MsgId) {
		core.LogError("重复消息，跳过处理:", payload.MsgId)
		return // 重复消息，跳过处理
	}
	GetLiveInfoMgr().Reporting(game.GetRoomPlatformId(), 1, payload.MsgId, bytedance.LIVE_MSG_TYPE_LIKE)

	livePlayer := GetLiveMgr().GetLivePlayerByOpenId(payload.OpenId)
	if livePlayer == nil {
		//! 还没有加入游戏，加入人少的一方，一样，则加入到
		liveData := GetLiveMgr().GetLiveDataByOpenId(payload.OpenId)
		if liveData == nil {
			liveData = GetLiveMgr().NewLiveData(payload.OpenId, payload.Nickname)
			liveData.Icon = payload.AvatarUrl
		} else {
			liveData.Icon = payload.AvatarUrl
			liveData.PlayerName = payload.Nickname
		}

		livePlayer = GetLiveMgr().GetLivePlayer(liveData.PlayerId)
		if livePlayer == nil {
			livePlayer = GetLiveMgr().NewLivePlayer(liveData.OpenId, liveData.PlayerName)
		}
	}

	livePlayer.AddGiftById(game.GetRoomId(), 12, payload.LikeNum)
	GetLiveInfoMgr().Reporting(game.GetRoomPlatformId(), 2, payload.MsgId, bytedance.LIVE_MSG_TYPE_LIKE)
}

// DoneOp 完成操作
func (game *LiveGame) DoneOp(op *Message.PlayerOperateC2S) {
	// 始终执行操作报告（移除测试模式限制）
	if op.IsSuccess == 1 && op.Type == 1 && op.MsgId != "" {
		GetLiveInfoMgr().Reporting(game.GetRoomPlatformId(), 2, op.MsgId, bytedance.LIVE_MSG_TYPE_GIFT)
	}
	if op.Type == 1 && op.MsgId != "" {
		GetGiftRecordMgr().DoRecord(op.MsgId)
	}
}
