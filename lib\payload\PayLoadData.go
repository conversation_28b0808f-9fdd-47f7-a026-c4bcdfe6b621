package payload

import (
	"zone/game/platform/bytedance"
	"zone/game/platform/kuaishou"
)

type PayloadData struct {
	UMsgId        string
	MsgId         string
	OpenId        string
	AvatarUrl     string
	Nickname      string
	GiftId        string
	GiftNum       int
	GiftValue     int
	LikeNum       int
	Content       string
	Timestamp     int64
	FansClub      int // 粉丝团消息类型：1-升级，2-加团
	FansClubLevel int
}

func ConvertDYPayloadData(comment []*bytedance.Payload) []*PayloadData {
	var ret []*PayloadData

	for _, v := range comment {
		ret = append(ret, &PayloadData{
			MsgId:         v.MsgId,
			OpenId:        v.OpenId,
			AvatarUrl:     v.AvatarUrl,
			Nickname:      v.Nickname,
			GiftId:        v.GiftId,
			GiftNum:       v.GiftNum,
			GiftValue:     v.GiftValue,
			LikeNum:       v.LikeNum,
			Content:       v.Content,
			Timestamp:     v.Timestamp,
			FansClub:      v.FansclubReasonType,
			FansClubLevel: v.FansclubLevel,
		})

	}
	return ret
}

func ConvertKSPayloadData(comment []*kuaishou.Payload) []*PayloadData {
	var ret []*PayloadData

	for _, v := range comment {
		ret = append(ret, &PayloadData{
			MsgId:     v.UniqueNo,
			OpenId:    v.UserInfo.UserID,
			AvatarUrl: v.UserInfo.AvatarUrl,
			Nickname:  v.UserInfo.UserName,
			GiftId:    v.GiftID,
			GiftNum:   v.GiftCount,
			GiftValue: v.GiftUnitPrice,
			LikeNum:   v.Count,
			Content:   v.Content,
			Timestamp: v.Timestamp,
		})

	}
	return ret
}
