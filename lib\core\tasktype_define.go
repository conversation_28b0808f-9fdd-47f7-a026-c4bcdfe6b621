package core

// 任务相关
const (
	TASK_TYPE_PLAYER_LEVEL            = 1   // 玩家等级11
	TASK_TYPE_CRYSTAL_FIGHT           = 2   // 法阵战力
	TASK_TYPE_GET_HERO                = 11  // 获得X星英雄11
	TASK_TYPE_BIGGEST_LEVEL           = 12  // 英雄到达指定顶级
	TASK_TYPE_LEVEL_UP_COUNT          = 13  // 英雄升级次数
	TASK_TYPE_STAR_UP_COUNT           = 14  // 英雄大圣堂升阶
	TASK_TYPE_HAVE_HERO               = 15  // 拥有指定条件英雄
	TASK_TYPE_GET_HOOK_AWARD          = 21  // 领取挂机宝箱次数11
	TASK_TYPE_GET_HOOK_FAST_AWARD     = 22  // 领取挂机快速奖励次数11
	TASK_TYPE_GET_HOOK_GET_ITEM       = 23  // 领取挂机物品数量
	TASK_TYPE_EQUIP_LEVEL_UP          = 31  // 装备强化
	TASK_TYPE_HAVE_EQUIP              = 32  // 拥有装备
	TASK_TYPE_FINISH_PASS             = 41  // 完成普通关卡
	TASK_TYPE_FINISH_MAIN_PASS        = 42  // 完成主线关卡11
	TASK_TYPE_FINISH_CHAPTER          = 43  // 完成章节
	TASK_TYPE_MONTH_STATE             = 50  // 月卡任务
	TASK_TYPE_ARENA_COUNT             = 51  // 取得挑战竞技场次数（普通）11
	TASK_TYPE_ARENA_POINT             = 52  // 竞技场积分11
	TASK_TYPE_SPECIAL_ARENA_CLASS     = 53  // 高阶竞技场段位
	TASK_TYPE_PIT_PASS                = 61  // 通过迷宫
	TASK_TYPE_PIT_KILL                = 62  // 迷宫击杀
	TASK_TYPE_PIT_KILL_PLAYER         = 63  // 迷宫击杀玩家
	TASK_TYPE_RESONANCE_CRYSTAL_SET   = 70  // 共鸣水晶提升的等级
	TASK_TYPE_RESONANCE_CRYSTAL_COUNT = 71  // 共鸣水晶开启槽位
	TASK_TYPE_RESONANCE_CRYSTAL_LEVEL = 72  // 共鸣水晶提升的等级
	RechargeOnceTask                  = 73  // 单笔充值
	TASK_TYPE_WOTER_LEVEL             = 81  // 王座塔层级
	TASK_TYPE_WOTER_COUNT             = 82  // 王座塔次数
	TASK_TYPE_CAMP_WOTER_LEVEL        = 83  // 种族塔层数
	TASK_TYPE_ONE_CAMP_TOWER_LEVEL    = 84  // 某一种族塔层数
	TASK_TYPE_UNION_HUNT_COUNT        = 91  // 公会狩猎参与次数
	TASK_TYPE_REWARD_TASK_SET         = 101 // 悬赏接受11
	TASK_TYPE_REWARD_TASK_GET         = 102 // 悬赏完成
	TASK_TYPE_REWARD_LEVLE            = 103 // 悬赏栏等级
	TASK_TYPE_FRIEND_POINT_COUNT      = 111 // 友情点次数
	TASK_TYPE_FRIEND_POINT            = 112 // 友情点数数量
	TASK_TYPE_SUMMON_HEROS            = 121 // 召唤英雄11
	TASK_TYPE_SHOP_BUY_COUNT          = 122 // 商店购买次数
	TASK_TYPE_ADD_LIVENESS            = 123 // 增加活跃度
	TASK_TYPE_JOIN_UNION              = 124 // 加入公会
	TASK_TYPE_LOGIN_TOTAL_COUNT       = 125 // 登录次数
	TASK_TYPE_REG_TOTAL_COUNT         = 126 // 注册天数
	TASK_TYPE_DECOMPOSE_HEROS         = 127 // 分解英雄11
	TASK_TYPE_HERO_STAR_POINT         = 128 // 英雄星级积分
	TASK_TYPE_LOGIN_DAY               = 129 // 累计登录
	TASK_TYPE_IS_LOGIN                = 130 // 是否成功登录 11
	TASK_TYPE_ASTROLOGY_COUNT         = 131 // 占星次数
	TASK_TYPE_OPEN_SERVER_DAY         = 132 //服务器开启天数
	TASK_TYPE_ACTIVITY_LOGIN          = 133 // 活动表登录
	TASK_TYPE_SUMMON_ELITE_HEROS      = 141 // 召唤精英英雄
	//TASK_TYPE_ITEM_EXCHANGE           = 147 // 道具兑换（限时打折）
	TASK_TYPE_RECHARGE_ONCE        = 201 // 指定充值
	TASK_TYPE_RECHARGE_SINGLE      = 202 // 单笔充值
	TASK_TYPE_RECHARGE_MONEY_DAILY = 203 // 每日累积充值
	TASK_TYPE_RECHARGE_COST        = 204 // 累积消耗
	//TASK_TYPE_RECHARGE_EQUAL_SINGLE     = 205 // 单笔充值
	TASK_TYPE_RECHARGE_ALL           = 206 // 累积充值
	TASK_TYPE_JJC_SCORE              = 212 // 竞技场积分
	TASK_TYPE_VIP_BUY                = 213 // VIP等级
	TASK_TYPE_RECHARGE_GOLD_WEEK     = 214 // 黄金周卡
	TASK_TYPE_STAR_UP_COUNT_EQUAL    = 216 // 14的强版本
	TASK_TYPE_GET_HERO_WIDE          = 217 // 11的弱版本
	TASK_TYPE_REWARD_TASK_GET_EQUAL  = 218 // 102的强版本
	TASK_TYPE_INSTANCE_PROCESS       = 221 // 铸时星域副本
	TASK_TYPE_ACTIFACT_GET           = 222 // 获得神器
	TASK_TYPE_LIFETREE_MAIN_LEVEL    = 231 // 创世神木等级
	TASK_TYPE_LIFETREE_OTHER_LEVEL   = 232 // 创世神木分支职业
	TASK_TYPE_ITEM_GET_COUNT         = 241 // 获得指定道具
	TASK_TYPE_ACTIVITY_EXCHANGE      = 242 // 获得指定道具
	TASK_TYPE_INTERSTELLAR_NEBULA    = 251 // 星云完成
	TASK_TYPE_INTERSTELLAR_NEBULAWAR = 252 // 星耀完成
	TASK_TYPE_NOBILITY_LEVEL         = 271 // 达到爵位
	//TASK_TYPE_ACTIVITY_BOSS_HURT_SINGLE = 281 // 暗域入侵单次入侵
	//TASK_TYPE_ACTIVITY_BOSS_HURT_ALL    = 282 // 暗域入侵累计伤害
	//TASK_TYPE_ACTIVITY_BOSS_COUNT       = 283 // 暗域入侵挑战次数
	TASK_TYPE_RECHARGE_EQUAL_MORE           = 387 // 七日累充。单笔充值 金额判断
	LoginAllDayEx                           = 388 // 新做的累积登录，逻辑更加严谨
	TASK_TYPE_EXPEDITION_LOGIN_DAY          = 389 //神域登录
	TASK_TYPE_RECHARGE_DAY_COUNT            = 420 // 充值天数
	TASK_TYPE_RECHARGE_DAY_COUNT_CONTINUOUS = 421 // 连续充值30天数
	TASK_TYPE_ONLINE_ACCUMUL_ATION          = 501 // 在线累计
	TASK_TYPE_ALCHEMY                       = 502 //炼金11
	TASK_TYPE_RESOURCE_NUM                  = 503 //试炼之地完成次数11
	TASK_TYPE_TEMPLE_NUM                    = 504 //参与神眠之庙
	TASK_TYPE_TEMPLE_All_NUM                = 505 //神眠之庙总层数之和
	TASK_TYPE_RICH_USE_DICE                 = 506 //骰子任务
	TASK_TYPE_MEMORY_PASS_NUM               = 507 //记忆回廊通关
	TASK_TYPE_CROSS_SERVER_ARENA_FIGHT      = 508 //苍穹战场
	TASK_TYPE_CROSS_SERVER_ARENA_SCORE      = 509 //苍穹战场积分
	TASK_TYPE_CROSS_SERVER_CHAT             = 512 //跨服聊天一次
	TASK_TYPE_ACT_NEWBOSS_FIGHT             = 513 //紧急悬赏
	TASK_TYPE_ACIENT                        = 514 //追逐草帽大冒险
	TASK_TYPE_RANK_HERO_FIGHT               = 613 //全区最高战力
	TASK_TYPE_ACTIVITY_DAY_BOSS_HURT_ALL    = 702 // 伤害挑战（简单）单次伤害
	TASK_TYPE_ACTIVITY_DAY_BOSS_HURT_SINGLE = 703 // 每日历练
	TASK_TYPE_LIVENESS_ITEM                 = 711 //
	TASK_TYPE_FIGHT_NUM                     = 712 // 全民豪礼
	TASK_TYPE_CHAOS_NUM                     = 715 //混沌招募次数
	TASK_TYPE_ACTIVITY_BOSS_HURT_SINGLE     = 731
	TASK_TYPE_ACTIVITY_BOSS_HURT_ALL        = 732
	TASK_TYPE_ACTIVITY_BOSS_SURVIVAL_NUM    = 733
	TASK_TYPE_ACTIVITY_BOSS_TEAM_NUM        = 734
	TASK_TYPE_FIND_HEART                    = 741  //心愿招募
	TASK_TYPE_ACTIVITY_STAR                 = 742  //升星计划
	TASK_TYPE_SERVER_FIND                   = 1000 // 征召任务 全服累计高级招募
	TASK_TYPE_SERVER_TRIAL                  = 1001 // 征召任务 全服参与船团试炼
	TASK_TYPE_SERVER_HERO                   = 1002 // 征召任务 全服累计培养
	TASK_TYPE_SERVER_TOWER                  = 1003 // 征召任务 全服累计通关海底大监狱
	TASK_TYPE_SERVER_REWARD                 = 1004 // 征召任务 全服累计完成紫色及以上情报任务
	TASK_TYPE_SERVER_GIVE                   = 1005 // 征召任务 全服累计捐献美味口粮
	TASK_TYPE_SHOP_REFRESH                  = 1010 //市场刷新次数

)

// 任务相关废弃
const (
	PlayerLvTask    = 1  // 玩家等级
	HeroOwnTask     = 6  // 拥有的英雄
	HeroStarLvTask  = 7  // 英雄升星
	PlayerFightTask = 14 // 玩家战力
	BuyMoneyTask    = 24 // 招财
	FindTask        = 26 // 招募任务
	TitleTask       = 27 // 称号任务
	BuyEnergyTask   = 39 // 购买体力
	ShopTask        = 40 // 商店任务
	FinishTask      = 41 // 领取任务奖励,可指定任务类型
	CostGemTask     = 42 // 元宝消耗
	CostEnergeTask  = 49 // 消耗道具
	MonthCard       = 50 // 月卡
	GetPowerTime    = 56 // 按时间体力领取
	SummonHerosTask = 59 // 召唤英雄
	RechargeGemTask = 60 // 充值元宝
	LoginTotalTask  = 61 // 累计登录

	FriendCount          = 86  // 好友数量
	LoginActTask         = 126 // 活动累计登录
	LoginTask            = 129 // 登录
	LevelRankTask        = 133 // 等级排行
	FightRankTask        = 134 // 战力排行
	UnionCopyTask        = 137 // 军团副本
	BeautyAdvance        = 140 // 宝物进阶
	TreasureaAdvance     = 141 // 宝物进阶数量累加
	BeautySearch         = 143 // 圣物搜索
	LegendLevelTask      = 144 // 传说关卡通过
	ItemExchangeTask     = 147 // 道具兑换（限时打折）
	TrialTask            = 155 // 试炼
	TaskMilitary         = 156 // 军演
	SummonHorseTask      = 157 // 购买水晶
	DiscernHorse         = 158 // 召唤魔宠
	HaveHorseCount       = 159 // 拥有魔宠数量
	HaveRunesCount       = 160 // 拥有符文数量
	ChaseTitle           = 165 // 王权争夺
	BuySoldierTask       = 166 // 购买兵符
	HeroRankTask         = 167 // 英雄星级排行
	PassRankTask         = 168 // 关卡星级排行
	ArtifactRankTask     = 169 // 上阵神器星级排行
	BeatyRankTask        = 170 // 美人战力排行
	ActShopTask          = 172 // 活动商店刷新
	ActEnergyTask        = 173 // 活动体力购买
	GemCostTask          = 174 // 钻石消耗
	CityNumTask          = 175 // 国家城池数量
	SmeltTask            = 176 // 冶炼
	TigerUpgradeTask     = 177 // 虎符精炼
	TigerOwnTask         = 178 // 拥有虎符
	DiscountTask         = 800 // 限时打折
	HeroUpStarNumTask    = 179 // 武将升星, 数量, 星级, done
	HeroUpStarTimesTask  = 180 // 武将升星, 升星次数, done
	EquipUpNumTask       = 181 // 装备强化, 装备数量, 达标等级 done
	EquipUpTimesTask     = 182 // 装备强化, 强化次数 done
	OwnGemNumTask        = 183 // 拥有宝石数量, 宝石等级, 宝石类型[0任意类型] done
	EquipStarNumTask     = 184 // 装备附魔,装备数量,达标等级 done
	EquipStarTimesTask   = 185 // 装备附魔,附魔次数 done
	CommonLevelTask      = 186 // 普通副本, 通关次数, 关卡Id[0代表任意关卡] done
	EliteLevelTask       = 187 // 精英副本, 通关次数, 关卡Id[0代表任意关卡] done
	TeamLevelTask        = 188 // 组队副本, 通关次数, 关卡Id[0代表任意关卡] done
	GemLevelTask         = 189 // 宝石副本, 通关次数, 关卡Id[0代表任意关卡] done
	ExpetionTask         = 190 // 远征副本, 通关次数, 关卡Id[0代表任意关卡] done
	TechDoneTask         = 191 // 科技 完成次数 done
	LivenessTask         = 192 // 活跃度 完成次数 达标数值 done
	PvpTimesTask         = 193 // 竞技场 完成次数 达标排名 0代表任意排名 done
	PvpScoreTask         = 194 // 竞技场 累计积分 done
	TowerResetTask       = 195 // 爬塔 重置次数 done
	TowerWinTask         = 196 // 爬塔 通关次数 关卡 0代表任意关卡 done
	TowerWin2Task        = 197 // 爬塔 通关次数 精英关卡 0代表任意关卡 done
	HaveTechCount        = 200 // 符合需求的科技数量 done
	TowerRank            = 201 // 爬塔排行榜 done
	PvpRankNum           = 202 // 斗技场排行榜
	TalentTask           = 203 // 天赋任务  done
	EquipColorTask       = 205 // 装备品级  done
	SoldierColorTask     = 207 // 佣兵品级  done
	SoldierLvTask        = 208 // 佣兵等级  done
	KingTaskFinish       = 209 // 王国任务_小环 done
	KingTaskDone         = 210 // 王国任务整个任务 done
	HaveArmyCount        = 211 // 符合条件佣兵数量 佣兵等级 佣兵品质 done
	HaveFlagCount        = 212 // 符合条件军旗数量 军旗等级 军旗品质 done
	EmbattleTalent       = 213 // 上阵武将总天赋排行 done
	UnionDonation        = 215 // 军团捐献 捐献类型 done
	FriendGiftCount      = 216 // 好友赠送 done
	ArmyFightCount       = 217 // 佣兵挑战 挑战类型 done
	UnionBossCount       = 218 // 军团boss done
	CityAttackCount      = 219 // 城池入侵 类型 done
	BuySmeltCount        = 220 // 购买神殿祝福物品 done
	PvpRankNow           = 222 // 斗技场排行 done
	TenTimesSearch       = 223 // 十连抽次数 召唤类型 done
	HaveBossCount        = 224 // 拥有巨兽 品质 done
	EquipGemRankTask     = 225 // 宝石等级活动排行
	HorseFightRankTask   = 226 // 魔宠战力活动排行
	TigerFightRankTask   = 227 // 虎符战力活动排行
	NobilityTask         = 228 // 爵位等级
	GetOnHookAwardTask   = 229 // 领取挂机奖励
	HaveDinivityTask     = 230 // 拥有神格
	AllDinivityTask      = 231 // 英雄神格
	DreamLandLootTask    = 232 // 神格幻境探索
	DreamLandLootTenTask = 233 // 神格幻境探索十连抽
	DungeonSweep         = 234 // 地下城扫荡, 通关次数, 章节Id[0代表任意章节] done
	GemLevelSweep        = 235 // 魔龙洞窟扫荡
	FightInspire         = 236 // 鼓舞
	MineFightReasult     = 237 // 矿点争夺
	GVGFightReasult      = 238 // 孤山夺宝
	OnHookLevelTask      = 239 // 天空城
	EquipGemTotalLevel   = 240 // 宝石阵容总等级        等级
	HorseTotalFight      = 241 // 魔宠阵容总战力        战力
	TigerTotalFight      = 242 // 纹章阵容总战力        战力
	//TASK_TYPE_ACTIVITY_BOSS_HURT_SINGLE = 281 // 暗域入侵单次入侵
	//TASK_TYPE_ACTIVITY_BOSS_HURT_ALL    = 282 // 暗域入侵累计伤害
	TASK_TYPE_ACTIVITY_BOSS_COUNT   = 283 // 暗域入侵挑战次数
	HeroUpClassTask                 = 301 // 英雄升阶
	HeroFateTask                    = 302 // 英雄羁绊
	HeroLvTask                      = 303 // 英雄等级
	HeroHandBookTask                = 304 // 英雄图鉴
	BuildTaxTask                    = 306 // 税收
	JoinUnionTask                   = 351 // 加入公会
	UnionDonationPoint              = 352 // 军团捐赠点数
	CityDefenseTask                 = 353 // 城防
	JoinNationalWarTask             = 354 // 参与国战    //待定
	InfluenceOccupyTask             = 355 // 势力占领
	UseSoldierTask                  = 356 // 使用兵种类型   //待定
	KingFightTask                   = 357 // 权谋之争占领(王座之战)
	NationalAllianceTask            = 358 // 国家同盟
	EquipFight                      = 359 // 装备战力
	BaldricFight                    = 360 // 配饰战力
	BeautyFight                     = 361 // 神器战力
	HeroHandBookCountTask           = 362 // 英雄图鉴数量
	TechFight                       = 363 // 科技战力
	BaldricCountTask                = 364 // 配饰任务
	ChapterTask                     = 365 // 章节任务
	OnHookListTask                  = 366 // 挂机总览列表强制征收
	EquipFightRankTask              = 367 // 装备战力活动排行
	HorseStarRankTask               = 368 // 魔宠战力活动排行
	TASK_TYPE_RECHARGE_EQUAL_SINGLE = 369 // 单笔充值 金额判断
	OpenSeverDay                    = 370 // 开服天数

	// 触发礼包专用的特殊任务 其他地方不要随便用
	SpecialPassTask         = 371 // 关卡id 必须完全相同
	SpecialLevelTask        = 372 // 玩家等级 必须完全相同
	SpecialHeroLevelTask    = 373 // 英雄等级 大于等于
	SpecialHeroStarTask     = 374 // 英雄星级 必须完全相同
	SpecialHeroClassTask    = 375 // 英雄阶级 必须完全相同
	SpecialEquipUpGradeTask = 376 // 装备强化 大于等于
	SpecialEquipStarTask    = 377 // 装备附魔 必须完全相同
	SpecialEquipRuneTask    = 378 // 装备铭文 必须完全相同
	SpecialBeautyTask       = 379 // 神器阶级 必须完全相同
	SpecialHorseStarTask    = 380 // 坐骑星级 必须完全相同
	SpecialHorseTalentTask  = 381 // 坐骑天赋升级 必须完全相同
	SpecialHorseAwakeTask   = 382 // 坐骑觉醒 必须完全相同
	SpecialNewPitTask       = 383 // 华容道次数 大于等于
	SpecialPVPTask          = 384 // 竞技场 小于等于
	// 结束
	FindCountRankTask  = 385 // 抽奖次数排行
	UnionLevelRankTask = 386 // 军团等级
	//TASK_TYPE_RECHARGE_EQUAL_MORE = 387 // 单笔充值 金额判断
	EquipAccessories   = 389 //装备配饰	数量 品质
	EmbattleHero       = 390 //上阵武将  数量 品质 阵营
	AdventureChallenge = 391 //奇遇挑战	次数
	DailyTaskLiveness  = 392 //每日任务活跃度达到100 	次数
	EquipReborn        = 393 //装备重生	次数 品质
	HeroReborn         = 394 //武将重生 	次数 品质
	CompleteEquipment  = 395 //装备整套装备 数量 品质
	BuyDailyTrialNums  = 396 //每日试炼购买次数 次数
	ShareBattleArray   = 397 //世界分享阵容 次数
	BeKing             = 398 //登上王座 	次数 爵位  zhangyang
	EquipRefine        = 399 //配饰精炼 次数 精炼品质
	EquipEngrave       = 400 //装备铭刻次数 品质
	ModiChariot        = 401 //战车改造 次数
	HaveHorse          = 402 //拥有战马	数量 品质
	HorseSoul          = 403 //战马天赋	数量 等级 品质
	BaseTech           = 404 //拥有基础科技 	拥有数量 科技等级
	SoldierTech        = 405 //拥有兵种科技	拥有数量 科技等级
	NationTech         = 406 //拥有国家科技	拥有数量 科技等级
	EquipTemper        = 407 //淬炼装备	数量 星级
	AllipTemper        = 408 //淬炼整套装备 数量 星级
	UnionDonationNew   = 409 //同盟捐献 次数 档次  0任意1铜钱2小元宝2大元宝
	HorseFind          = 410 //战马召唤 次数 类型  0任意1铜钱2元宝

)
