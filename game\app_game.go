package game

import (
	"fmt"
	"sync"
	"time"
	"zone/game/announcement"
	"zone/game/configs"
	"zone/game/manager"
	"zone/game/models"
	"zone/game/mods"
	"zone/game/platform/bytedance"
	"zone/game/platform/kuaishou"
	"zone/lib/core"
	"zone/lib/crypto"
	"zone/lib/db"
	"zone/lib/network"
	"zone/lib/storage"
)

const (
	// TimerInterval 主逻辑循环定时器间隔（秒）
	TimerInterval = 1
	// SaveInterval 数据保存间隔（分钟）
	SaveInterval = 5
	// AnnouncementCheckInterval 公告检查间隔（分钟）
	AnnouncementCheckInterval = 3
	// DailyResetHour 每日重置时间（小时）
	DailyResetHour = 0
	// NoonResetHour 中午重置时间（小时）
	NoonResetHour = 12
	// WeeklyResetDay 周重置日（周六）
	WeeklyResetDay = 6
	// MonthEndNotificationHour 月末通知时间（小时）
	MonthEndNotificationHour = 23
)

// GameApp 游戏应用程序，负责游戏核心逻辑和定时任务
type GameApp struct {
	config              core.Config     // 游戏配置
	isShutdown          bool            // 关闭标志
	waitGroup           *sync.WaitGroup // 同步等待组
	worldLevel          int             // 世界等级
	isInitialized       bool            // 初始化状态
	announcementMap     *sync.Map       // 公告映射表
	currentSeason       int             // 当前赛季
	lastDailyUpdateTime int64           // 上次每日更新时间戳
}

// 游戏应用单例实例
var gameAppInstance *GameApp

// GetGameApp 获取游戏应用单例实例
func GetGameApp() *GameApp {
	if gameAppInstance == nil {
		gameAppInstance = &GameApp{
			isShutdown:      false,
			waitGroup:       &sync.WaitGroup{},
			announcementMap: &sync.Map{},
		}
		// 注册全局接口
		core.GameApp = gameAppInstance
	}
	return gameAppInstance
}

// StartService 启动游戏服务
// isTestServer: 是否为测试服务器
// platform: 平台标识
func (gameApp *GameApp) StartService(config *core.Config) {
	core.LogInfo("正在启动游戏应用服务...")

	// 增加等待组计数
	gameApp.waitGroup.Add(1)

	// 设置网络配置
	network.IsTestServer = config.IsTestServer
	network.Platform = config.Platform

	crypto.EnableEncrypt = config.UseEncrypt
	if network.Platform == network.Platform_DouYin {
		bytedance.GetAccessToken()
		network.PlatformName = "抖音"
	} else {
		kuaishou.GetAccessToken()
		network.PlatformName = "快手"
	}

	// 初始化各种管理器
	gameApp.initializeManagers()

	// 启动后台服务
	core.LogInfo("启动后台逻辑处理...")
	gameApp.startBackgroundServices()
}

// initializeManagers 初始化各种管理器
func (gameApp *GameApp) initializeManagers() {

	// 初始化游戏相关管理器
	configs.GetGiftConfig().Init()
	configs.GetMapConfig().Init()
	configs.GetGameConfig().Init()
	configs.GetActorScoreConfig().Init()

	manager.GetSeasonMgr().GetSeason()
	manager.GetLiveMgr().GetData()
	manager.GetAnchorMgr().GetData()

	// 初始化LiveInfoMgr以确保注册到mods包
	manager.GetLiveInfoMgr()

	// 初始化其他管理器以确保注册
	manager.GetLiveTopMgr()
	manager.GetRoomMgr()
	manager.GetGiftRecordMgr()
	manager.GetSessionRoomMgr()
	manager.GetMultiGameMatchMgr()
	manager.GetMultiGameMgr()

	// 启动公告管理器
	announcement.GetManager().Start()

	core.LogInfo("游戏管理器初始化完成")
}

// startBackgroundServices 启动后台服务
func (gameApp *GameApp) startBackgroundServices() {
	// 启动主逻辑定时器
	go gameApp.OnTimer()

	// 启动日志检查管理器
	go core.GetLoggerCheckMgr().Run()

	// 启动会话管理器
	// go network.GetSessionMgr().Run()
}

// Shutdown 检查游戏应用是否正在关闭
func (gameApp *GameApp) Shutdown() bool {
	return gameApp.isShutdown
}

// StopService 停止游戏服务
func (gameApp *GameApp) StopService() {
	core.LogInfo("正在停止游戏应用服务...")

	// 设置关闭标志
	gameApp.isShutdown = true

	// 停止公告管理器
	announcement.GetManager().Stop()

	// 保存所有数据
	gameApp.Save()

	core.LogDebug("游戏应用服务已停止")
	gameApp.waitGroup.Done()
}

// RegisterService 注册服务（预留方法）
func (gameApp *GameApp) RegisterService() {
	// TODO: 实现服务注册逻辑
}

// OnTimer 游戏主逻辑循环
// 每秒执行一次定时任务，包括数据保存、公告更新等
func (gameApp *GameApp) OnTimer() {
	ticker := time.NewTicker(time.Second * TimerInterval)
	defer ticker.Stop()

	core.LogInfo("游戏主逻辑定时器已启动")

	for {
		// 检查是否需要关闭
		if gameApp.isShutdown {
			core.LogInfo("检测到关闭信号，退出主逻辑循环")
			break
		}

		select {
		case <-ticker.C:
			gameApp.executeTimerTasks()
		}
	}

	core.LogInfo("游戏主逻辑定时器已停止")
}

// executeTimerTasks 执行定时任务
func (gameApp *GameApp) executeTimerTasks() {
	// 检查关闭状态
	if gameApp.isShutdown {
		return
	}

	currentTime := core.TimeServer()

	// 执行各种定时任务
	gameApp.handlePeriodicSave(currentTime)
	gameApp.handleDailyReset(currentTime)
	gameApp.handleNoonReset(currentTime)
	gameApp.handleAnnouncementUpdate(currentTime)
	gameApp.handleMonthEndNotifications(currentTime)

	// 清理已移除的会话
	network.GetSessionMgr().ClearRemoveSession()
}

// handlePeriodicSave 处理定期数据保存
func (gameApp *GameApp) handlePeriodicSave(currentTime time.Time) {
	// 每5分钟的第3分钟进行数据保存
	if currentTime.Minute()%SaveInterval == 3 && currentTime.Second() == 0 {
		core.LogInfo("执行定期数据保存，当前时间:", currentTime.Format("15:04:05"))
		gameApp.Save()
	}
}

// handleNoonReset 处理中午重置任务
func (gameApp *GameApp) handleNoonReset(currentTime time.Time) {
	// 每天12点重置，防止重复执行（30分钟内只执行一次）
	if currentTime.Hour() == NoonResetHour &&
		currentTime.Minute() < 5 &&
		currentTime.Second() > 0 &&
		currentTime.Unix()-gameApp.lastDailyUpdateTime > 1800 {

		// core.LogInfo("执行中午重置任务")
		gameApp.lastDailyUpdateTime = currentTime.Unix()

		// 周六处理世界榜单清零
		if currentTime.Weekday() == WeeklyResetDay {
			core.LogInfo("执行周重置：清零世界榜单")
			manager.GetLiveMgr().ResetDataWeekly()
		}
	}
}

// handleDailyReset 处理每日重置任务
func (gameApp *GameApp) handleDailyReset(currentTime time.Time) {
	// 每天0点重置，防止重复执行（30分钟内只执行一次）
	if currentTime.Hour() == DailyResetHour &&
		currentTime.Minute() < 5 &&
		currentTime.Unix()-gameApp.lastDailyUpdateTime > 1800 {

		core.LogInfo("执行每日重置任务")
		gameApp.lastDailyUpdateTime = currentTime.Unix()
		manager.GetLiveMgr().ResetDataCall()

		// 如果是月末最后一天，执行月重置
		if gameApp.isLastDayOfMonth(currentTime) {
			core.LogInfo("执行月重置任务")
			manager.GetLiveMgr().ResetDataMonthly()
		}
	}
}

// handleAnnouncementUpdate 处理公告更新
func (gameApp *GameApp) handleAnnouncementUpdate(currentTime time.Time) {
	// 每3分钟的第1分钟更新公告
	if currentTime.Minute()%AnnouncementCheckInterval == 1 && currentTime.Second() == 0 {
		gameApp.loadAnnouncements()
		if announcement, exists := gameApp.announcementMap.Load(2); exists {
			announcementData := announcement.(*models.AnnouncementDB)
			mods.GetRoomMgr().SetAnnouncement(announcementData.Content)
		}
	}
}

// handleMonthEndNotifications 处理月末通知
func (gameApp *GameApp) handleMonthEndNotifications(currentTime time.Time) {
	if !gameApp.isLastDayOfMonth(currentTime) {
		return
	}

	if currentTime.Hour() == MonthEndNotificationHour {
		// 在40分钟时刷新公告
		if currentTime.Minute() == 40 && currentTime.Second() == 0 {
			gameApp.loadAnnouncements()
		}

		// 在45、50、55分钟时发送月末通知
		if (currentTime.Minute() == 45 || currentTime.Minute() == 50 || currentTime.Minute() == 55) &&
			currentTime.Second() == 0 {
			if announcement, exists := gameApp.announcementMap.Load(1); exists {
				announcementData := announcement.(*models.AnnouncementDB)
				mods.GetRoomMgr().SetAnnouncement(announcementData.Content)
			}
		}
	}
}

// loadAnnouncements 加载公告数据
func (gameApp *GameApp) loadAnnouncements() {
	// 重新初始化公告映射
	gameApp.announcementMap = &sync.Map{}

	var announcementTemplate models.AnnouncementDB
	sqlQuery := fmt.Sprintf("SELECT * FROM `%s`", storage.TABLE_Announcement)

	// 从数据库获取所有公告数据
	results := db.GetDBMgr().DBUser.GetAllData(sqlQuery, &announcementTemplate)

	// 处理每条公告记录
	for _, result := range results {
		announcementData := result.(*models.AnnouncementDB)
		announcementData.Init(storage.TABLE_Anchor, announcementData, true)
		gameApp.announcementMap.Store(announcementData.Id, announcementData)
	}
}

// GetAnnouncement 获取公告（兼容性方法）
func (gameApp *GameApp) GetAnnouncement() {
	gameApp.loadAnnouncements()
}

// isLastDayOfMonth 判断是否为当月最后一天
func (gameApp *GameApp) isLastDayOfMonth(currentTime time.Time) bool {
	// 获取下个月的第一天，然后减去一天得到当月最后一天
	nextMonth := time.Date(currentTime.Year(), currentTime.Month()+1, 1, 0, 0, 0, 0, currentTime.Location())
	lastDayOfMonth := nextMonth.AddDate(0, 0, -1)

	return currentTime.Day() == lastDayOfMonth.Day()
}

// IsLastDay 判断是否为当月最后一天（兼容性方法）
func (gameApp *GameApp) IsLastDay(targetTime time.Time) bool {
	return gameApp.isLastDayOfMonth(targetTime)
}

// HandleRPC 处理RPC调用（预留接口）
// dataType: RPC调用类型
// data: 可变参数数据
// 返回值: RPC调用结果
func (gameApp *GameApp) HandleRPC(dataType int, data ...interface{}) interface{} {
	// TODO: 实现具体的RPC处理逻辑
	// switch dataType {
	// case core.RPC_CROSS_ACTIVITY_CONSUMETOP_UPDATE:
	//     return mod.GetMasterMgr().MatchRPC.MatchConsumerTopUpdate(data[0].(*data.JS_ConsumerTopUser))
	// case core.RPC_CROSS_ACTIVITY_CONSUMETOP_GET_RANK:
	//     return mod.GetMasterMgr().MatchRPC.MatchConsumerTopGetAllRank(data[0].(int), data[1].(int))
	// default:
	//     core.LogError("未知RPC类型:", dataType)
	// }

	core.LogDebug("RPC调用暂未实现，类型:", dataType)
	return ""
}

// Save 保存所有游戏数据
// 执行各个管理器的数据保存操作
func (gameApp *GameApp) Save() {
	// 保存直播游戏数据
	manager.GetLiveMgr().Save()

	// 保存游戏配置数据
	configs.GetGameConfig().Save()

	// 保存主播数据
	mods.GetAnchorMgr().Save()
}
