package utils

import (
	"testing"
)

// TestSafeMapIntLen 测试SafeMapInt的Len方法
func TestSafeMapIntLen(t *testing.T) {
	safeMap := NewSafeMapInt()
	
	// 初始长度应该为0
	if safeMap.Len() != 0 {
		t.<PERSON>rrorf("期望初始长度为0，实际为%d", safeMap.Len())
	}
	
	// 添加元素
	safeMap.Store(1, "value1")
	safeMap.Store(2, "value2")
	safeMap.Store(3, "value3")
	
	// 检查长度
	if safeMap.Len() != 3 {
		t.Errorf("期望长度为3，实际为%d", safeMap.Len())
	}
	
	// 删除一个元素
	safeMap.Delete(2)
	
	// 检查长度
	if safeMap.Len() != 2 {
		t.Errorf("删除后期望长度为2，实际为%d", safeMap.Len())
	}
	
	// 清空
	safeMap.Clear()
	
	// 检查长度
	if safeMap.Len() != 0 {
		t.Errorf("清空后期望长度为0，实际为%d", safeMap.Len())
	}
}

// TestSafeMapInt32Len 测试SafeMapInt32的Len方法
func TestSafeMapInt32Len(t *testing.T) {
	safeMap := NewSafeMapInt32()
	
	// 初始长度应该为0
	if safeMap.Len() != 0 {
		t.Errorf("期望初始长度为0，实际为%d", safeMap.Len())
	}
	
	// 添加元素
	safeMap.Store(int32(1), "value1")
	safeMap.Store(int32(2), "value2")
	
	// 检查长度
	if safeMap.Len() != 2 {
		t.Errorf("期望长度为2，实际为%d", safeMap.Len())
	}
}

// TestSafeMapInt64Len 测试SafeMapInt64的Len方法
func TestSafeMapInt64Len(t *testing.T) {
	safeMap := NewSafeMapInt64()
	
	// 初始长度应该为0
	if safeMap.Len() != 0 {
		t.Errorf("期望初始长度为0，实际为%d", safeMap.Len())
	}
	
	// 添加元素
	safeMap.Store(int64(1), "value1")
	safeMap.Store(int64(2), "value2")
	safeMap.Store(int64(3), "value3")
	safeMap.Store(int64(4), "value4")
	
	// 检查长度
	if safeMap.Len() != 4 {
		t.Errorf("期望长度为4，实际为%d", safeMap.Len())
	}
}

// TestSafeMapIntIntLen 测试SafeMapIntInt的Len方法
func TestSafeMapIntIntLen(t *testing.T) {
	safeMap := NewSafeMapIntInt()
	
	// 初始长度应该为0
	if safeMap.Len() != 0 {
		t.Errorf("期望初始长度为0，实际为%d", safeMap.Len())
	}
	
	// 添加元素
	safeMap.Store(1, 100)
	safeMap.Store(2, 200)
	safeMap.Store(3, 300)
	
	// 检查长度
	if safeMap.Len() != 3 {
		t.Errorf("期望长度为3，实际为%d", safeMap.Len())
	}
	
	// 验证Count方法和Len方法返回相同结果
	if safeMap.Count() != safeMap.Len() {
		t.Errorf("Count()和Len()应该返回相同结果，Count: %d, Len: %d", safeMap.Count(), safeMap.Len())
	}
}

// TestSafeMapStringLen 测试SafeMapString的Len方法
func TestSafeMapStringLen(t *testing.T) {
	safeMap := NewSafeMapString()
	
	// 初始长度应该为0
	if safeMap.Len() != 0 {
		t.Errorf("期望初始长度为0，实际为%d", safeMap.Len())
	}
	
	// 添加元素
	safeMap.Store("key1", "value1")
	safeMap.Store("key2", "value2")
	safeMap.Store("key3", "value3")
	safeMap.Store("key4", "value4")
	safeMap.Store("key5", "value5")
	
	// 检查长度
	if safeMap.Len() != 5 {
		t.Errorf("期望长度为5，实际为%d", safeMap.Len())
	}
	
	// 删除元素
	safeMap.Delete("key3")
	
	// 检查长度
	if safeMap.Len() != 4 {
		t.Errorf("删除后期望长度为4，实际为%d", safeMap.Len())
	}
}

// TestAllSafeMapLenConcurrency 测试所有SafeMap的Len方法在并发环境下的安全性
func TestAllSafeMapLenConcurrency(t *testing.T) {
	// 测试SafeMapInt
	safeMapInt := NewSafeMapInt()
	go func() {
		for i := 0; i < 100; i++ {
			safeMapInt.Store(i, i)
		}
	}()
	go func() {
		for i := 0; i < 100; i++ {
			_ = safeMapInt.Len()
		}
	}()
	
	// 测试SafeMapString
	safeMapString := NewSafeMapString()
	go func() {
		for i := 0; i < 100; i++ {
			safeMapString.Store(string(rune(i)), i)
		}
	}()
	go func() {
		for i := 0; i < 100; i++ {
			_ = safeMapString.Len()
		}
	}()
	
	// 测试SafeMapInt64
	safeMapInt64 := NewSafeMapInt64()
	go func() {
		for i := 0; i < 100; i++ {
			safeMapInt64.Store(int64(i), i)
		}
	}()
	go func() {
		for i := 0; i < 100; i++ {
			_ = safeMapInt64.Len()
		}
	}()
	
	// 简单等待，确保goroutine执行完成
	// 在实际测试中，应该使用sync.WaitGroup
	for i := 0; i < 1000; i++ {
		// 空循环等待
	}
	
	t.Log("并发测试完成，没有发生panic")
}

// BenchmarkSafeMapLen 基准测试所有SafeMap的Len方法性能
func BenchmarkSafeMapIntLen(b *testing.B) {
	safeMap := NewSafeMapInt()
	
	// 预填充一些数据
	for i := 0; i < 1000; i++ {
		safeMap.Store(i, i)
	}
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = safeMap.Len()
	}
}

func BenchmarkSafeMapStringLen(b *testing.B) {
	safeMap := NewSafeMapString()
	
	// 预填充一些数据
	for i := 0; i < 1000; i++ {
		safeMap.Store(string(rune(i)), i)
	}
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = safeMap.Len()
	}
}

func BenchmarkSafeMapInt64Len(b *testing.B) {
	safeMap := NewSafeMapInt64()
	
	// 预填充一些数据
	for i := 0; i < 1000; i++ {
		safeMap.Store(int64(i), i)
	}
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = safeMap.Len()
	}
}
