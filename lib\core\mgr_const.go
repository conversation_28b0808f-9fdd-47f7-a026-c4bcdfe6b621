package core

const (
	ATTACK_WIN  = 1
	DEFENCE_WIN = 2
)

const (
	POS_ATTACK       = 0
	POS_DEFENCE      = 1
	POS_MAX          = 2
	TEAM_POS_SET_MAX = 9 //战场站位
)

type TurnBattleResult struct {
	ResultInfo string                           `json:"resultinfo"` // 详细过程
	FightId    int64                            `json:"fightid"`
	Random     int                              `json:"random"`
	Result     int                              `json:"result"`
	HP         [POS_MAX][TEAM_POS_SET_MAX]int64 `json:"hp"`
	TakeDamage [POS_MAX][TEAM_POS_SET_MAX]int64 `json:"takedamage"`
	HPPercent  [POS_MAX][TEAM_POS_SET_MAX]int   `json:"hppercent"`
	Energy     [POS_MAX][TEAM_POS_SET_MAX]int   `json:"energy"`
}

//! 基本游戏参数
const (
	MAX_PLAYER_CHECK_CODE = 8 //! 角色登陆-检查Code
)

const (
	CHAT_WORLD       = 1  // 世界
	CHAT_PARTY       = 2  // 公会
	CHAT_CAMP        = 3  // 阵营
	CHAT_PRIVATE     = 4  // 私聊
	CHAT_SYSTEM      = 5  // 公告
	CHAT_CAMP_SYSTEM = 6  // 阵营系统聊天
	CHAT_TEAM        = 7  // 组队系统聊天
	CHAT_MINE        = 8  // 矿点战玩家
	CHAT_MINE_SYSTEM = 9  // 矿点战系统
	CHAT_GVE         = 10 // 孤山夺宝
	CHAT_PRIVATE_SVR = 11 //! 跨服聊天

	MAX_BARRAGE_INTERVAL    = 10 // 弹幕间隔
	MAX_WORLD_CHAT_INTERVAL = 10 // 世界聊天间隔
)

const (
	LOG_COLOR_NORMAL = 0
	LOG_COLOR_RED    = 1
	LOG_COLOR_YELLOW = 2
	LOG_COLOR_BULE   = 3
	LOG_COLOR_GREEN  = 3
)

const (
	LOGIC_FALSE = 0 //未开启自动分解
	LOGIC_TRUE  = 1
	LOGIC_ALL   = 2
)

const (
	BATTLE_TYPE_PVP  = 1 //
	BATTLE_TYPE_PVE  = 2 //
	BATTLE_TYPE_BOSS = 3 //
)

const (
	TIME_RESET_TYPE_CREATE_ROLE = 1 //根据创建角色天数
	TIME_RESET_TYPE_WEEK        = 2
	TIME_RESET_TYPE_TIME        = 3 //根据固定时间计算
	TIME_RESET_TYPE_TIME_BASE   = 4 //基准时间
	TIME_RESET_TYPE_TIME_SEVER  = 5
)

const (
	TIME_RESET_SYSTEM_NEWPIT        = 26 //
	TIME_RESET_SYSTEM_UNIONACTIVITY = 74 //
	TIME_RESET_SYSTEM_CHAMPION      = 80 //
)

//tariff:type
const (
	TARIFF_TYPE_HERO_BACK      = 6  //英雄回退
	TARIFF_TYPE_ONHOOK_FAST    = 7  //快速挂机
	TARIFF_TYPE_FIND_OPEN_CAMP = 17 //阵营池开启

	TARIFF_RESONANCE_CRYSTAL_RESONANCE_GEM = 13
	TARIFF_RESONANCE_CRYSTAL_RESONANCE     = 14
	TARIFF_RESONANCE_CRYSTAL_CLEAN_CD      = 18
	TARIFF_ASTROLOGY_GEN                   = 26
	TARIFF_ASTROLOGY_ITEM                  = 27
	TARIFF_RICH_BUY_TIMES                  = 55

	TARIFF_TYPE_ARENA_NORMAL     = 1 //普通竞技场
	TARIFF_TYPE_ARENA_SPECIAL    = 2 //高阶竞技场
	TARIFF_TYPE_ARENA_NORMALBUY  = 3 //普通竞技场
	TARIFF_TYPE_ARENA_SPECIALBUY = 4 //高阶竞技场

	TARIFF_TYPE_VOID_HERO_CANCEL = 23 //虚空英雄取消

	TARIFF_TYPE_LIFE_TREE_RESET         = 25 //生命树重置
	TARIFF_TYPE_ACTIVITY_BOSS_RESET     = 34 //世界BOSS次数重置
	TARIFF_TYPE_CROSS_ARENA_TIMES       = 35 //跨服竞技场刷新次数
	TARIFF_TYPE_LOTTERY_DRAW_COST       = 36 //抽奖
	TARIFF_TYPE_REWARD_REFRESH          = 37 //刷新情报任务消耗
	TARIFF_TYPE_REWARD_PROP             = 38 //刷新情报任务消耗道具
	TARIFF_TYPE_HERO_STAR_4             = 41 //4星英雄置换消耗
	TARIFF_TYPE_HERO_STAR_5             = 42 //5星英雄置换消耗
	TARIFF_TYPE_TREASURE_ONE_CARD_1     = 43 //普通探宝单抽
	TARIFF_TYPE_TREASURE_ONE_CARD_2     = 44 //普通探宝10抽
	TARIFF_TYPE_TREASURE_TEN_CARD_1     = 45 //高级探宝单抽
	TARIFF_TYPE_TREASURE_TEN_CARD_2     = 46 //高级探宝10抽
	TARIFF_TYPE_TREASURE_REFRESH_COST_1 = 47 //探宝刷新普通消耗
	TARIFF_TYPE_TREASURE_REFRESH_COST_2 = 48 //高级探宝刷新消耗
	TARIFF_TYPE_SMELTER_ONE_CARD_1      = 49
	TARIFF_TYPE_SMELTER_FIVE_CARD_2     = 50
	TARIFF_TYPE_UNION_RECRUIT           = 53
	TARIFF_TYPE_SMELTER_ALERT_NAME      = 54
	TARIFF_TYPE_CROSS_SERVER_ARENA_CARD = 58
)

const (
	SIMPLE_NUM_SELFFIND_OFFSET = 11
	CROSSARENA_FREETIMES       = 12
	ASTROLOGY_INIT_SCORE       = 13
	LOTTERY_DRAW_ADD           = 14
	LOTTERY_DRAW_INIT          = 15
	FIND_8_COST                = 17
	FIND_8_COST_TEN            = 18
)

const (
	CANTFINISH = 0
	CANTAKE    = 1
	TAKEN      = 2
)

//新魔龙 英雄战力更新的原因
const (
	ReasonPlayerLogin = 1 //玩加登录
	//英雄本身相关
	ReasonHeroNew              = 101 //英雄创建
	ReasonHeroCheck            = 102 //英雄检查
	ReasonHeroReborn           = 103 //英雄重生
	ReasonHeroBack             = 104 //英雄回退
	ReasonHeroLvUp             = 105 //英雄升级
	ReasonHeroStarUp           = 106 //英雄升阶
	ReasonHeroFate             = 107 //英雄羁绊
	ReasonHeroLifeTree         = 108 //英雄生命树
	ReasonHeroVoidSetResonance = 109 //虚空英雄
	ReasonUnionHeroLifeTree    = 110 //军团生命树

	//装备
	ReasonEquipWear   = 201 //穿装备
	ReasonEquipOff    = 202 //脱装备
	ReasonEquipLvUp   = 203 //装备强化
	ReasonEquipAction = 204 //装备锻造
	//神器
	ReasonArtifactWear = 211 //穿神器
	ReasonArtifactOff  = 212 //脱神器
	//专属
	ReasonExclusiveUnlock = 221 //解锁专属
	ReasonExclusiveLvUp   = 222 //专属升级

	ReasonStageTalentSetSkill = 231 //设置技能

	ReasonTalismanWear = 241 //穿护符
	ReasonTalismanOff  = 242 //脱护符
	ReasonRuneWear     = 243
	ReasonTakeOffRune  = 244

	ReasonEmbedHorseSoul  = 251 // 镶嵌魔魂
	ReasonUpHorseSoul     = 252 // 升级魔魂
	ReasonMountHorse      = 253 // 上魔宠
	ReasonUnMountHorse    = 254 // 下魔宠
	ReasonAwakenHorse     = 255 // 觉醒魔宠
	ReasonSaveWashHorse   = 256 // 魔宠洗练保存
	ReasonSaveSwicthHorse = 257 // 魔宠转换保存
	ReasonRemoveHorseSoul = 258 // 魔宠删除马魂
	ReasonSetResonance    = 261 // 设置共鸣
	ReasonRemoveResonance = 262 // 取消共鸣

	ReasonAddRastar = 271 //勋章

)
const (
	SPECIAL_STOP = 1
	INIT_LV      = 0
)

const (
	ITEM_SOUL_HERO_STONE       = 13000002 //紫色魂石
	ITEM_ARENA_TICK            = 40000001 //竞技场挑战卷
	ITEM_NEW_PIT_REBORN        = 40000011 //女神之泪
	ITEM_FIND_GEM_ITEM         = 42010001 //钻石召唤物品
	ITEM_FIND_CAMP_ITEM        = 42010002 //阵营召唤卷
	ITEM_FIND_ASTROLOGY_ITEM   = 42010003 //占星卷
	ITEM_EQUIP_LVUP_ITEM_LOW   = 81000001 //黑铁铸币
	ITEM_TICKET                = 91000000 //! 玉璧
	ITEM_GOLD                  = 91000001 //金币
	ITEM_GEM                   = 91000002 //金果
	ITEM_PLAYER_EXP            = 91000005 //领主经验
	ITEM_PLAYER_VIP_EXP        = 91000022 //VIP经验
	ITEM_UNION                 = 91000025 //贡献币（公会币）
	ITEM_SCIENCE               = 91000033
	ITEM_HERO_EXP              = 91000041 //经验药水
	ITEM_POWDER                = 91000044 //魔粉
	ITEM_BACK_COIN             = 91000045 //英灵币（遣散币）
	ITEM_FRIEND_POINT          = 91000048 //友情点
	ITEM_ARENA_COIN            = 91000050 //竞技场硬币
	ITEM_NEW_PIT_COIN          = 91000051 //地牢币
	ITEM_CAMP_HERO_RAND        = 91000052 //种族英雄选择卡
	ITEM_ACCESS_ITEM           = 91000058 //收藏家点数
	ITEM_MONTH_SCORE           = 91000059 //月卡积分
	ITEM_REWARD_VALUE          = 91000061 //情报值
	ITEM_RICH_COIN             = 91000063 //遗迹币
	ITEM_RICH_ITEM             = 91000094 //骰子
	ITEM_RICH_ITEM_SELF_CHOOSE = 91000095 //道具：遥控骰子

	ITEM_ACT_OPTIONAL        = 91000128 //宝库钥匙  自选宝库
	ITEM_EXPEDITION_WARORDER = 91000086
	ITEM_SEEKFATE_135        = 91000135
	ITEM_SEEKFATE_136        = 91000136
	ITEM_ACT_PREARATORY      = 91000134 //美味口粮
	ITEM_ACT_EXPEDITION      = 42010003 //顶上之力
	ITEM_GOLD_PRIVILEGE      = 31000005 //金币特权
	ITEM_HERO_EXP_PRIVILEGE  = 31000006
)

const (
	ATTR_TYPE_HP      = 1
	ATTR_TYPE_ATTACK  = 2
	ATTR_TYPE_DEFENSE = 3
	ATTR_TYPE_FIGHT   = 99
	PER_BIT           = 10000 //万分比
)

// 游戏日志
const (
	LOG_FIGHT_FINALEX      = 26  // 决战
	LOG_USER_PASS_OK       = 100 // 关卡通过
	LOG_USER_TITLE_ACTIVE  = 101 // 威名-称号激活
	LOG_USER_RECHARGE      = 102 // 充值
	LOG_USER_VIP_UP        = 103 // VIP升级
	LOG_USER_LEVEL_UP      = 104 // 玩家升级
	LOG_BEAUTY_UP_TREASURE = 208 // 圣物-宝物-升级
	LOG_BEAUTY_UP_LEVEL    = 209 // 圣物-升级(激活，升阶）
	//LOG_HERO_ACTIVE        = 211 // 激活武将

	LOG_FIGHT_SOLO   = 301 // 国战-单挑
	LOG_FIGHT_FINAL  = 302 // 决战
	LOG_FIGHT_SERIES = 303 // 国战单人玩法

	LOG_PVP_ARMSARENA_BU = 401 // 天下会武-补-挑战
	LOG_PVP_KING_FIGHT   = 404 // 王权战斗
	LOG_PVP_OFFICE_FIGHT = 405 // 官职战斗
	LOG_PVP_EXPEDITION   = 406 // 远征

	LOG_EVENT_VISIT_CELEBRITY = 600 // 事件-名士寻访
	LOG_EVENT_PEOPLE_FEELING  = 601 // 民生民情
	LOG_EVENT_PASS_HERO       = 602 // 过关斩将
	LOG_EVENT_TREASURE_MAP    = 603 // 藏宝图

	LOG_EVENT_SummonHorse_Normal = 701 // 普通招募
	LOG_EVENT_SummonHorse_Senior = 702 // 高级招募
	LOG_EVENT_Discernhorse       = 703 // 相马
	LOG_EVENT_Combinehorse       = 704 // 合成马
	LOG_EVENT_Uphorse_4          = 705 // 马升4星
	LOG_EVENT_Uphorse_5          = 706 // 马升5星
	LOG_EVENT_Awakehorse         = 707 // 马觉醒
	LOG_EVENT_Decomposehorse     = 708 // 分解战马
	LOG_EVENT_Decomposesoul      = 709 // 分解马魂
	LOG_EVENT_Exchangesoul       = 710 // 马魂兑换
	LOG_EVENT_Uphorsesoul        = 711 // 马魂强化
	LOG_EVENT_GET_HORSE          = 712 // 获得战马

	LOG_EVENT_FlushmiliTask  = 801 // 刷新
	LOG_EVENT_ExectemiliTask = 802 // 派遣执行
	LOG_EVENT_AwardmiliTask  = 803 // 派遣领取

	LOG_GENERAL_HERO      = 1001 //  限时神将日志
	LOG_ACT_DIAL          = 1003 //  活动转盘日志
	LOG_ACT_DRAW          = 1004 // 活动翻牌
	LOG_ACT_LUCKEGG       = 1005 // 金蛋活动
	LOG_ACT_LUCKSTART     = 1006 // 开工福利
	LOG_ACT_DAILYRECHARGE = 1007 // 连续充值

	LOG_TREASURE = 2001 // 宝物
)

const (
	LOG_FIGHT                  = 1
	LOG_SWEEP                  = 2
	LOG_SUCCESS                = 1
	LOG_FAIL                   = 2
	LOG_PASS_TYPE_FIGHT        = 1
	LOG_PASS_TYPE_PASS_WIN     = 2
	LOG_PASS_TYPE_SEARCH       = 3
	LOG_PASS_TYPE_SWEEP        = 4
	LOG_MONEY_TASK_ITEM        = 1
	LOG_MONEY_TASK_GEM         = 2
	LOG_MONEY_TASK_REPLACEITEM = 3
	LOG_GVE_BUILD              = 1
	LOG_GVE_DRAGON             = 2
	LOG_SMELT_FREE             = 1
	LOG_SMELT_GEM              = 2
	LOG_SMELT_GEM_TEN          = 3
)

// 新日志
const (
	LOG_USER_LOGIN = 100 //! 角色登录
	LOG_ADD_GUIDE  = 101 //! 引导交互

	LOG_ADD_PLAYER_LEVEL       = 201 //! 用户升级
	LOG_PLAYER_CHANGE_NAME     = 202 //用户改名
	LOG_PLAYER_CHANGE_HEAD     = 203 //用户改头像
	LOG_PLAYER_CHANGE_PORTRAIT = 204 //用户改边框
	LOG_PLAYER_CHANGE_TITLE    = 205 //用户改称号

	LOG_LIVE_START_GAME   = 301 //! 开始直播
	LOG_LIVE_STOP_GAME    = 302 //! 结束直播
	LOG_LIVE_DO_JOIN_GAME = 303 //! 加入游戏
	LOG_LIVE_DO_LIKE      = 304 //! 用户点赞
	LOG_LIVE_DO_GIFT      = 305 //! 刷新礼物

	LOG_HERO_UP_LV    = 501 //伙伴升级
	LOG_HERO_UP_LV_TO = 502 //伙伴一键升级
	LOG_HERO_UP_STAR  = 503 //伙伴升星
	LOG_HERO_LOCK     = 504 //锁定伙伴
	LOG_HERO_UNLOCK   = 505 //解锁伙伴
	LOG_HERO_STAR     = 506 //升星殿堂操作记录506

	LOG_HERO_REBORN = 1101 //伙伴重生
	LOG_HERO_SHIFT  = 1102 //伙伴转换
	LOG_HERO_FIRE   = 1103 //伙伴遣散

	LOG_HERO_BOOK          = 601 //激活伙伴图鉴=601
	LOG_HERO_GET_HAND_BOOK = 602 //领取图鉴奖励
	LOG_HERO_DEMO          = 603 //进入试玩= 603

	LOG_GET_ENTANGLEMENT_AWARD         = 701 //解锁羁绊
	LOG_GET_ENTANGLEMENT_UPGRADE_SKILL = 702 //领取羁绊奖励

	LOG_MSG_UP_MAIN_LEVEL          = 801 //船灵升级
	LOG_MSG_GEMSTONE_NUM           = 802 //船灵宝石镶嵌
	LOG_ADD_RESONANCE_COUNT        = 803 //共鸣解锁
	LOG_SET_RESONANCE_HEROS        = 804 //上阵共鸣英雄
	LOG_CANCEL_RESONANCE_HEROS_MSG = 805 //下阵共鸣英雄
	LOG_MSG_UP_TYPE_LEVEL          = 806 //伙伴阵营等级修炼
	LOG_SYS_RASTAR                 = 807 //激活勋章
	LOG_RASTAR_UPDATE_STAR1        = 808 //勋章升星
	LOG_RASTAR_UPDATE_STAR2        = 809 //勋章觉醒

	LOG_PASS_BEGIN        = 301 //挑战主线关卡
	LOG_PASS_RESULT_ONREG = 302 //主线关卡通关

	LOG_AWARD_ONHOOK = 401 //领取挂机奖励
	LOG_ONHOOK_FAST  = 402 //使用快速挂机

	LOG_FINISH_TARGET_TASK = 4101 //领取通关奖励

	LOG_FINISH_ACTIVITY_ONREG = 4201 //领取在线奖励

	LOG_PASS_RESULT_EVENT = 4301 //海上事件

	LOG_REWARD_SET     = 4401 //接取情报
	LOG_REWARD_REFRESH = 4402 //刷新情报
	LOG_REWARD_GET     = 4403 //领取情报奖励

	LOG_TREASURERE_CARD  = 901 //探宝抽取
	LOG_TREASURE_REFRESH = 902 //刷新探宝

	LOG_WEAR_ALL_EQUIP       = 1001 //一键穿戴装备
	LOG_TAKE_OFF_ALL_EQUIP   = 1002 //一键卸下装备
	LOG_ONFORGING_ACTION     = 1003 //装备锻造
	LOG_ONFORGING_ACTION_ALL = 1004 //一键锻造

	LOG_SYNTHESIS_TALISMAN = 4501 //护符合成
	LOG_TALISMAN_RECAST    = 4502 //护符重铸
	LOG_REPLACE_SKILL      = 4503 //护符重铸保存
	LOG_OPTIONAL_SKILL     = 4504 //护符技能自选

	LOG_GET_MAIL_ITEM = 1201 //领取邮件附件

	LOG_GET_RANK_AWARD  = 1401 //领取排行榜奖励
	LOG_LOOK_RANK_AWARD = 1402 //排行榜查看用户信息

	LOG_SHOP_BUY            = 1501 //商店购买道具
	LOG_SHOP_RANDEM_REFRESH = 1502 //刷新商店商品

	LOG_RECHARGE_TICKET_START = 1701 //发起充值
	LOG_UP_VIP_LEVEL          = 1702 //贵族等级
	LOG_GET_VIP_RECHARGE      = 1703 //购买贵族礼包
	LOG_RECHARGE_TICKET_ONREG = 1704 //充值成功

	LOG_USE_ITEM_HANDLE = 1801 //使用道具
	LOG_HERO_COMPOUND   = 1802 //合成道具

	LOG_CREATE_UNION         = 2001 //创建大船团
	LOG_DISSOLVE_UNION       = 2002 //解散大船团
	LOG_ALERT_UNION_NAME     = 2003 //大船团改名
	LOG_ALERT_UNION_ICON     = 2004 //大船团改旗帜
	LOG_UNION_RECRUIT        = 2005 //发送招募信息
	LOG_JOIN_UNION           = 2006 //加入大船团
	LOG_OUT_UNION            = 2007 //退出大船团
	LOG_CHALLENGE_TRIAL_END  = 2008 //船团试炼
	LOG_TALENT_UP_TYPE_LEVEL = 2009 //船团天赋

	LOG_ARENA_FIGHT = 2101 //竞技场发起挑战
	LOG_SAVE_TEAM   = 2102 //保存防守阵容

	LOG_TOWER_FIGHT_RESULT = 2201 //挑战海底大监狱

	LOG_ABYSS_ALONE_END = 2301 //挑战海上列车

	LOG_RESOURCE_END   = 2401 //挑战七水之都
	LOG_RESOURCE_SWEEP = 2402 //扫荡七水之都

	LOG_MEMORY_BEGIN = 2501 //记忆回廊发起挑战
	LOG_MEMORY_END   = 2502 //记忆回廊通关

	LOG_NEWBOSS_END         = 2601 //通关紧急悬赏
	LOG_NEWBOSS_LOWER_LEVEL = 2602 //紧急悬赏等级调整

	LOG_FIGHT_BEGIN                   = 2801 //追逐草帽大冒险发起挑战
	LOG_ACTIVITY_DAY_BOSS_RESET_TIMES = 2802 //追逐草帽大冒险通关

	LOG_ACT_SEVEN = 2901 //新服悬赏跳转

	LOG_ACTIVITY_DAY_BOSS_RESULT_EX = 3001 //宿命交锋挑战

	LOG_CHALLENGE_START = 3301 //发起伙伴挑战
	LOG_CHALLENGE_END   = 3302 //通关伙伴挑战

	LOG_CHECKIN = 3401 //签到次数

	LOG_SPECIAL_PURCHASE_ACTIVATE = 3501 //触发限时礼包
	LOG_GET_AWARD                 = 3502 //购买限时礼包

	LOG_COLLECTION = 3601 //购买金币次数

	LOG_ECONOMICS_CARD = 3901 //蘑菇大冒险

)

//道具流水
const (
	ITEMLOG_CREATE_ROLE = "创建角色赠送奖励"

	ITEMLOG_ADD_EXP             = "玩家升级"
	ITEMLOG_COST_ALERT_NAME     = "玩家改名"
	ITEMLOG_GET_ACTIVATION_CODE = "领取激活码奖励"

	ITEMLOG_COST_HERO_UP_LV    = "英雄升级"
	ITEMLOG_COST_HERO_UP_LV_TO = "英雄一键升级"
	ITEMLOG_COST_HERO_UP_STAR  = "英雄升星消耗"

	ITEMLOG_COST_HERO_REBORN = "伙伴重生消耗"
	ITEMLOG_ADD_HERO_REBORN  = "伙伴重生获得"
	ITEMLOG_COST_HERO_SHIFT  = "伙伴转换消耗"
	ITEMLOG_ADD_HERO_FIRE    = "伙伴遣散消耗"
	ITEMLOG_COST_HERO_FIRE   = "伙伴遣散获得"

	ITEMLOG_ADD_HERO_GET_HAND_BOOK = "领取图鉴奖励"

	ITEMLOG_COST_ENTANGLEMENT_UPGRADE_SKILL = "技能解锁"
	ITEMLOG_ADD_GET_AWARD                   = "羁绊达成奖励"

	ITEMLOG_COST_UP_MAIN_LEVEL   = "船灵升级"
	ITEMLOG_COST_GEMSTONE_NUM    = "船灵宝石升级"
	ITEMLOG_COST_RESONANCE_COUNT = "船灵共鸣位解锁"
	ITEMLOG_COST_UP_TYPE_LEVEL   = "伙伴修炼"
	ITEMLOG_COST_UPDATE_STAR_1   = "勋章升星"
	ITEMLOG_COST_UPDATE_STAR_2   = "勋章觉醒"

	ITEMLOG_ADD_PASS_RESULT_ONREG = "通关冒险关卡"

	ITEMLOG_ADD_AWARD_ONHOOK = "领取挂机奖励"
	ITEMLOG_COST_ONHOOK_FAST = "使用快速挂机"
	ITEMLOG_ADD_ONHOOK_FAST  = "快速挂机奖励"

	ITEMLOG_ADD_FINISH_TARGET_TASK    = "领取通关奖励"
	ITEMLOG_ADD_FINISH_ACTIVITY_ONREG = "领取在线奖励"

	ITEMLOG_ADD_PASS_RESULT_EVENT = "海上事件"

	ITEMLOG_COST_REWARD_SET     = "接取情报"
	ITEMLOG_ADD_REWARD_GET      = "领取情报奖励"
	ITEMLOG_COST_REWARD_REFRESH = "刷新情报"

	ITEMLOG_COST_TREASURERE_CARD    = "探宝抽奖消耗"
	ITEMLOG_ADD_TREASURERE_CARD     = "探宝抽奖获得"
	ITEMLOG_ADD_TREASURERE_LUCKYBOX = "领取幸运值奖励"
	ITEMLOG_COST_TREASURE_REFRESH   = "探宝刷新消耗"

	ITEMLOG_COST_ONFORGING_ACTION      = "装备锻造消耗"
	ITEMLOG_ADD_ONFORGING_ACTION       = "装备锻造获得"
	ITEMLOG_COST_OONFORGING_ACTION_ALL = "装备一键锻造消耗"
	ITEMLOG_ADD_ONFORGING_ACTION_ALL   = "装备一键锻造获得"

	ITEMLOG_COST_SYNTHESIS_TALISMAN = "护符合成消耗"
	ITEMLOG_ADD_SYNTHESIS_TALISMAN  = "护符合成获得"
	ITEMLOG_COST_REPLACE_SKILL      = "护符重铸消耗"
	ITEMLOG_COST_OPTIONAL_SKILL     = "护符技能自选"

	ITEMLOG_ADD_GET_MAIL_ITEM     = "领取邮件奖励"
	ITEMLOG_ADD_GET_ALL_MAIL_ITEM = "一键领取邮件奖励"

	ITEMLOG_ADD_FRIEND_GET_AWARD = "好友一键领取和赠送"

	ITEMLOG_ADD_GET_RANK_AWARD = "领取排行榜奖励"

	ITEMLOG_COST_SHOP_BUY             = "商店购买道具消耗"
	ITEMLOG_ADD_SHOP_BUY              = "商店购买道具获得"
	ITEMLOG_COST_SHOP_RANDEM_REFRESH  = "刷新商店商品消耗"
	ITEMLOG_COST_ACTIVITY_SHOP_UNLOCK = "活动商店解锁"

	/*ITEMLOG_COST_DRAW_TYPE_GEM_1  = "单次高级招募消耗"
	ITEMLOG_ADD_DRAW_TYPE_GEM_1   = "单次高级招募获得"
	ITEMLOG_COST_DRAW_TYPE_GEM_10 = "十连高级招募消耗"
	ITEMLOG_ADD_DRAW_TYPE_GEM_10  = "十连高级招募获得"

	ITEMLOG_COST_DRAW_TYPE_BASE_1  = "单次普通招募消耗"
	ITEMLOG_ADD_DRAW_TYPE_BASE_1   = "单次普通招募获得"
	ITEMLOG_COST_DRAW_TYPE_BASE_10 = "十连普通招募消耗"
	ITEMLOG_ADD_DRAW_TYPE_BASE_10  = "十连普通招募获得"

	ITEMLOG_COST_DRAW_TYPE_CAMP  = "单次阵营招募消耗"
	ITEMLOG_ADD_DRAW_TYPE_CAMP   = "单次阵营招募获得"
	ITEMLOG_COST_DRAW_TYPE_SCORE = "积分招募消耗"
	ITEMLOG_ADD_DRAW_TYPE_SCORE  = "积分招募获得"*/
	//阵营积分招募
	//阵营积分招募
	ITEMLOG_ADD_RECHARGE       = "玉果充值获得"
	ITEMLOG_ADD_RECHARGE_LEVEL = "玉果充值获得贵族等级"
	ITEMLOG_COST_BUY_VIP_BOX   = "贵族礼包消耗"
	ITEMLOG_ADD_BUY_VIP_BOX    = "贵族礼包获得"

	ITEMLOG_COST_GEM_BUY = "金果充值消耗"
	ITEMLOG_ADD_GEM_BUY  = "金果充值获得"

	ITEMLOG_COST_ITEM_HANDLE   = "使用道具消耗"
	ITEMLOG_ADD_ITEM_HANDLE    = "使用道具获得"
	ITEMLOG_COST_HERO_COMPOUND = "合成道具消耗"
	ITEMLOG_ADD_HERO_COMPOUND  = "合成道具获得"

	ITEMLOG_COST_CREATE_UNION         = "创建大船团消耗"
	ITEMLOG_COST_ALERT_UNION_NAME     = "大船团改名消耗"
	ITEMLOG_COST_TALENT_UP_TYPE_LEVEL = "船团天赋消耗"
	//ITEMLOG_ADD_CHALLENGE_TRIAL_END     = "船团试炼挑战获得"
	ITEMLOG_ADD_CHALLENGE_TRIAL_END = "船团试炼挑战获得"

	ITEMLOG_ADD_ARENA_FIGHT      = "竞技场挑战奖励"
	ITEMLOG_COST_ARENA_FIGHT     = "竞技场挑战消耗"
	ITEMLOG_ADD_BUY_ARENA_COUNT  = "竞技场购买挑战卷获得"
	ITEMLOG_COST_BUY_ARENA_COUNT = "竞技场购买挑战卷消耗"
	ITEMLOG_ADD_ARENA_LIKE       = "竞技场点赞奖励"
	ITEMLOG_ADD_ARENA_GET_BOX    = "竞技场周宝箱奖励"

	//ITEMLOG_ADD_FIGHT_RESULT = "海底大监狱挑战奖励"
	//ITEMLOG_ADD_GET_RANK            = "海底大监狱排行奖励"
	ITEMLOG_ADD_FIGHT_RESULT        = "海底大监狱通关奖励"
	ITEMLOG_ADD_TOWER_TODAY_BOX     = "无线层每日奖励"
	ITEMLOG_COST_BUY_WAR_ORDER_HELP = "挑战助力购买消耗"
	ITEMLOG_ADD_BUY_WAR_ORDER_HELP  = "挑战助力购买获得"

	ITEMLOG_ADD_ABYSS_ALONE_END   = "海上列车挑战奖励"
	ITEMLOG_ADD_ABYSS_FIRST_AWARD = "海上列车通关奖励"

	ITEMLOG_ADD_RESOURCE_END    = "七水之都挑战奖励"
	ITEMLOG_COST_RESOURCE_SWEEP = "七水之都扫荡奖励"
	ITEMLOG_ADD_RESOURCE_SWEEP  = "七水之都扫荡消耗"

	ITEMLOG_ADD_MEMORY_PASSID    = "记忆回廊关卡奖励"
	ITEMLOG_ADD_MEMORY_ID        = "记忆回廊节点奖励"
	ITEMLOG_ADD_ACTMEMORY_PASSID = "海上逸闻关卡奖励"
	ITEMLOG_ADD_ACTMEMORY_ID     = "海上逸闻节点奖励"

	ITEMLOG_ADD_NEWBOSS_END = "紧急悬赏"

	ITEMLOG_COST_RICH_DICE     = "普通骰子消耗"
	ITEMLOG_ADD_RICH_DICE      = "普通骰子获得"
	ITEMLOG_COST_USE_RICH_DICE = "遥控骰子消耗"
	//ITEMLOG_ADD_USE_RICH_DICE   = "遥控骰子获得"
	ITEMLOG_ADD_RICH_GETCOMPASS = "黄金轮盘获得"
	ITEMLOG_COST_RICH_BUY       = "旅行商人消耗"
	ITEMLOG_ADD_RICH_BUY        = "旅行商人获得"

	ITEMLOG_ADD_ACIENT_FIGHT_END = "追逐草帽大冒险挑战奖励"
	ITEMLOG_ADD_GET_BOX_AWARD    = "追逐草帽大冒险星级宝箱奖励"

	ITEMLOG_ADD_GET_SEVEN_AWARD = "领取新服悬赏奖励"

	ITEMLOG_ADD_ACTIVITY_DAYBOSSTASK = "领取宿命交锋挑战任务奖励"

	ITEMLOG_ADD_ACT_TASK_AWARS = "伙伴认领"

	ITEMLOG_ADD_GET_ITEM = "购买金币"

	ITEMLOG_COST_DO_TURN_TABLE = "新服馈赠消耗"
	ITEMLOG_ADD_DO_TURN_TABLE  = "新服馈赠获得"

	ITEMLOG_ADD_ACT_TASK_AWARS_2 = "船员招募"

	ITEMLOG_ADD_ACTIVITY_CHALLENGE_END = "伙伴挑战"

	ITEMLOG_ADD_ACT_TASK_AWARS_3 = "四皇赠礼"

	ITEMLOG_ADD_CHECKIN = "每日签到"

	ITEMLOG_ADD_ACT_TASK_AWARS_4 = "等级礼包"

	ITEMLOG_ADD_ACT_TASK_AWARS_5 = "月卡"

	ITEMLOG_ADD_ACT_TASK_AWARS_6 = "积天豪礼"
	ITEMLOG_ADD_ACT_TASK_AWARS_7 = "每日充值"

	ITEMLOG_ADD_GETFIRSET_AWARD  = "首充（包括68累充）1"
	ITEMLOG_ADD_GETFIRSET_AWARD2 = "首充（包括68累充）2"
	ITEMLOG_ADD_DRAW_ITEM        = "限时礼包"

	ITEMLOG_COST_PRIVILEGE_BUY = "订阅特权消耗"
	ITEMLOG_ADD_PRIVILEGE_BUY  = "订阅特权获得"

	ITEMLOG_COST_ECONOMICSCARD = "跳跃消耗"
	ITEMLOG_ADD_ECONOMICS_CARD = "跳跃获得"
	ITEMLOG_ADD_GIFT_GET_AWARD = "勇者商店获得"

	ITEMLOG_COST_SMELTER_AWARD = "熔炼消耗"
	ITEMLOG_ADD_SMELTER_AWARD  = "熔炼获得"
	ITEMLOG_ADD_SMELTERAWARD   = "熔炼奖励领取"

	ITEMLOG_CROSS_SERVER_ARENA_CARD      = "苍穹战场翻卡牌"
	ITEMLOG_CROSS_SERVER_ARENA_TASK      = "苍穹战场任务"
	ITEMLOG_CROSS_SERVER_ARENA_ATTACK    = "苍穹战场攻击"
	ITEMLOG_CROSS_SERVER_ARENA_BUY_COUNT = "苍穹战场挑战卷"
	ITEMLOG_CROSS_SERVER_ARENA_LIKE      = "苍穹战场点赞"

	ITEMLOG_ADD_FINISH_PREPARATORY_TASK    = "领取征召奖励"
	ITEMLOG_ADD_FINISH_PREPARATORY_DO_TASK = "征召捐赠"
	ITEMLOG_CROSS_SERVER_RANK_LIKE         = "跨服排行点赞"

	ITEMLOG_ADD_SEEK_FATE_TREASURE_HUNT  = "寻宝大奖奖励"
	ITEMLOG_ADD_SEEK_FATE_TREASURE_HUNT2 = "寻宝普通奖励"
	ITEMLOG_COST_SEEK_FATE_TREASURE_HUNT = "寻宝消耗"
	ITEMLOG_ADD_ONE_KEY_TREASURE_HUNT    = "一键寻宝普通奖励"
	ITEMLOG_ADD_ONE_KEY_TREASURE_HUNT2   = "一键寻宝大奖奖励"
	ITEMLOG_COST_KEY_TREASURE_HUNT       = "一键寻宝消耗"

	ITEMLOG_ADD_LIKE = "点赞奖励"
)

// 平台相关
const (
	PLATFORM_DEFAULT = 0
	PLATFORM_WINDOWS = 1
	PLATFORM_ANDRIOD = 2
	PLATFORM_IOS     = 3
	PLATFORM_WP      = 4
	PLATFORM_MAC     = 5
)

// 排行榜
const (
	FightRank    = iota + 1 //1.战力排行榜
	TowerActRank            //2.爬塔排行榜
	PassStarRank            //3.关卡总星数排行榜
	TalentRank              //4.天赋总星级
	GemCostRank             //5.钻石消耗总星数排行榜

)

// 开启等级
const (
	LEVEL_OPEN_TITLE  = 6  // 称号开启等级
	LEVEL_OPEN_CASERN = 32 // 兵种开启等级
	LEVEL_OPEN_FATE   = 21 // 缘分开启等级
)

//! 阵营PVP
const (
	CAMP_PVP_FIGHT = 1 //! 国战
	CAMP_PVP_MINE  = 2 //! 矿点争夺
	CAMP_PVP_UNION = 3 //! 军团战
	CAMP_PVP_GVE   = 4 //! 资源争夺战-孤山夺宝
)

// 表里面的type要+1
const (
	TYPE_CASERN_GONG = 0 // 弓
	TYPE_CASERN_QI   = 1 // 骑
	TYPE_CASERN_BU   = 2 // 步
)

// 其他
const (
	DATEFORMAT    = "2006-01-02 15:04:05" // 时间格式化
	POWERMAX      = 1500                  // 最大体力限制
	SKILLPOINTMAX = 100                   // 最大技能点限制
	LEVELMAX      = 200                   // 最大等级限制80-90
	LEVELVIPMAX   = 15                    // vip最大等级
	ADDPOWERTIME  = 300                   // 体力回复时间(秒)
	ADDSPTIME     = 20                    // 技能点回复时间(秒)

	DEFAULT_JJC_WORSHIP_MONEY = 2000

	DEFAULT_GOLD          = 91000001 //! 金币
	DEFAULT_GEM           = 91000002 //! 元宝-钻石
	DEFAULT_EXP           = 91000005 //! 经验
	DEFAULT_INSPIRE       = 79000001 //! 鼓舞道具
	ACT_KEY_ITEM_ID       = 88800028 //! 转盘活动道具
	MONEY_MHERO           = 91000029 //! 无双币
	GVGNEED_ITEM_ID       = 91000020 //! GVG 消耗道具
	TigerChipId           = 91000031 // 铁矿
	GveTakeNum            = 91000036 // 据点占领值
	GveMaterial           = 91000037 // 战场物资
	GveCampMaterial       = 91000038 // 阵营战场物资
	GveGlory              = 91000039 // 孤山荣誉点
	HorseMatrial          = 91000027 //!
	HorseMatrial2         = 91000030 //! 战马材料2
	HeroSoul              = 91000032 // 魂石
	TechPoint             = 91000033 // 科技点
	BossMoney             = 91000034 // 巨兽精魄
	TowerStone            = 91000035 // 镇魂石
	HeroExp               = 91000041 // 卡牌经验
	LIVENESS_DAILY_POINT  = 91000046 // 日常活跃度
	LIVENESS_WEEK_POINT   = 91000047 // 周活跃度
	WARORDER_ITEM_1       = 91000053 // 赏金社1
	WARORDER_ITEM_2       = 91000054 // 赏金社2
	LIFE_TREE_ITEM        = 91000055 // 成长涓流
	UNION_ACTIVITY_POINT  = 91000057 // 公会活跃度
	FIND_SCORE            = 91000075 // 召唤积分
	ECONOMICS_CERTIFICATE = ******** // 勇者之证
	UNION_TALENT          = ********
	LIVENESS_ITEM         = ******** // 勋章

	DAY_SECS      = 86400
	HALF_DAY_SECS = 43200
	HOUR_SECS     = 3600
	MIN_SECS      = 60

	CAMP_SHU        = 1 // 帝国
	CAMP_WEI        = 2 // 联邦
	CAMP_WU         = 3 // 圣堂
	CAMP_QUN        = 4
	CITY_SHU        = 1104
	CITY_WEI        = 1059
	CITY_WU         = 1018
	FIGHTTYPE_DEF   = -200    // 城防军
	FIGHTTYPE_ROBOT = -500    // 机器人
	GM_ACCOUNT_ID   = 1000000 // 正常GM账号 = 10000，压测帐号放开限制

	SHOP_GENERAL     = 1  // 杂货-勋章
	SHOP_UNION       = 2  // 军团贡献
	SHOP_BOX         = 5  // 宝箱-斗技 神器
	SHOP_HONOR       = 6  // 荣誉 功勋 国库 商行
	SHOP_MAGICALSHOP = 7  // 无双商店
	SHOP_EXPEDITION  = 9  // 远征 秘境
	SHOP_TOWER       = 10 // 镇魂塔
	SHOP_DINIVITY    = 12 // 神格商店

	SHOP_NEW_NORMAL           = 1  //普通商店
	SHOP_NEW_PVP              = 2  //高阶竞技场商店
	SHOP_NEW_HERO             = 3  //英雄商店
	SHOP_NEW_RUINS            = 4  //遗迹商店
	SHOP_NEW_UNION            = 5  //公会商店
	SHOP_NEW_ABYSS            = 6  //深渊商店
	SHOP_NEW_SECRET_TERRITORY = 7  //秘境商店
	SHOP_NEW_SEGMENT_POSITION = 8  //段位
	SHOP_NEW_CROSS_SERVER     = 9  //跨服
	SHOP_NEW_KING             = 10 //王者
	SHOP_NEW_OVERARCHING_SKY  = 11 //苍穹
	SHOP_NEW_CAMP             = 12 //阵营
	SHOP_NEW_ECONOMICS        = 13 //勇者
	SHOP_NEW_EXPEDITION_1     = 14 //
	SHOP_NEW_EXPEDITION_2     = 15 //
	SHOP_NEW_PIT              = 16 //

	MAGICHERO_ACTIVITY_ID = 9005 // 神将开启活动
	ACTIVITY_ORDER_HERO   = 9006 // 预约神将活动
	GENERAL_HERO_ACT_TYPE = 9007 // 获得当前活动相关数据
	INVEST_MONEY          = 9008 // 招财猫
	FIT_SERVER_ACT_TYPE   = 9009 // 合服预热活动
	ACT_DIAL              = 9011 // 转盘
	ACT_TIMEGIFT          = 9021 //! 限时礼包
	//ACT_DAILY_RECHARGE1   = 9024 //! 每日首充-1
	//ACT_DAILY_RECHARGE2   = 9025 //! 每日首充-2
	ACT_DAILY_RECHARGE = 1015 //! 每日首充

	MAX_HORSE_AWAKEN_LEVEL = 7 // 最高等级

	ACTIVITY_STATUS_CLOSED = 0 // 活动关闭
	ACTIVITY_STATUS_OPEN   = 1 // 活动开启
	ACTIVITY_STATUS_SHOW   = 2 // 活动结束，可领奖，不可完成, 发送奖励

	MAX_UNION_DONATION = 6000 // 军团相关

	AWARD_NUM        = 7
	MAX_GENERAL_RANK = 50 // 最大神将排名

	MAX_CITY_POWER      = 150 // 军令上限
	MAX_CITY_POWER_TIME = 720 // 军令恢复时间

	MaxRankNum           = 1000 // 排行榜最大数量
	MaxRankShowNum       = 50   // 排行榜显示数量
	MaxAcientRankShowNum = 100
	ActRankNum           = 20 // 排行榜活动显示数量

	GVGNEED_ITEM_NUM  = 5
	GVGNEED_ITEM_DESC = "GVG消耗"

	FightAtt = 99

	DEFAULT_HEAD_ICON         = 1000
	DEFAULT_HEAD_ICON_PROCESS = 9100001

	MaxOnlineRankNum = 9 // 排行榜中在线的最大数量
)

// Tariff类型
const (
	TariffPvpBuy               = 1 // pvp挑战次数购买
	TariffPvpRest              = 2 // pvp重置次数
	TariffTowerSet             = 3
	TariffTowerAdvanceBuy      = 4
	TariffTowerBuffBuy         = 5
	TariffGemStone             = 6
	TariffbuffKingTaskAction   = 7
	TariffbuffKingFlush        = 8
	TariffTaskDone             = 9
	TariffBuyBox               = 10
	TariffKing                 = 11
	TariffEditName             = 12
	TariffGvgPower             = 13
	TariffCreateUnion          = 15
	TariffChangeUnionName      = 16
	TariffGiveItems            = 20
	TariffSoldierUpgrade       = 21
	TariffSoldierWash          = 22
	TariffTakePower            = 23
	TariffLootGold             = 24
	TariffLootGem              = 25
	TariffArmy                 = 27
	TariffBuyArmy              = 28
	TariffBuyDungeonReset      = 29
	TariffReBorn               = 30
	TariffTalentReset          = 31
	TariffDreamlandGoldRefresh = 32
	TariffDreamlandGemRefresh  = 33
	TariffDreamlandGoldLoot    = 34
	TariffDreamlandGemLoot     = 35
)

// OpenLevel 类型
const (
	OPEN_LEVEL_TALENT_RESET = 82
	OPEN_LEVEL_REBORN_FREE  = 86
	OPEN_LEVEL_57           = 57
	OPEN_LEVEL_35           = 35
	OPEN_LEVEL_80           = 80
	OPEN_LEVEL_ON_HOOK      = 89
	//新魔龙

	//OPEN_LEVEL_ARENA             = 99
	//OPEN_LEVEL_SPECIAL_ARENA     = 100
	//OPEN_LEVEL_HERO_REBORN       = 8 //大圣堂分解
	OPEN_LEVEL_HERO_SUPPORT      = 9
	OPEN_LEVEL_RESONANCE_CRYSTAL = 15
	//OPEN_LEVEL_NEWPITINFO        = 17
	//OPEN_LEVEL_RANK_TASK         = 21
	//OPEN_LEVEL_HIRE              = 40
	OPEN_LEVEL_WARORDER_2 = 46
	//OPEN_ASTROLOGY               = 52
	//OPEN_LEVEL_LAST_AWARD        = 57
	//OPEN_LEVEL_LIFE_TREE         = 62
	//OPEN_LEVEL_WARORDER_1        = 82
	//OPEN_LEVEL_WARORDERLIMIT_1   = 90
	//OPEN_LEVEL_WARORDERLIMIT_2   = 91
	//OPEN_LEVEL_NEWPITSHOP        = 107

	/*OPEN_LEVEL_HEROUPSTAR = 4 //升星
	OPEN_LEVEL_FINDPOOL   = 6 //英雄招募*/

)

const (
	TYPE_RED_EMBATTLE = 1  // 上阵
	TYPE_RED_HERO     = 2  // 英雄
	TYPE_RED_EQUIP    = 3  // 装备
	TYPE_RED_ARMY     = 4  // 佣兵
	TYPE_RED_STAR     = 5  //星界、魔宠
	TYPE_RED_BOSS     = 6  // 巨兽
	TYPE_RED_BAG      = 7  // 背包
	TYPE_RED_WORLD    = 8  // 世界
	TYPE_RED_SHOP     = 9  // 市场
	TYPE_RED_TECH     = 10 // 学院
	TYPE_RED_BATTLE   = 11 // 战役
	TYPE_RED_BEAUTY   = 12 // 圣物
	TYPE_RED_GOD      = 13 // 神殿
	TYPE_RED_UNION    = 14 // 军团
	TYPE_RED_PVP      = 15 // 竞技场
	TYPE_RED_MAIL     = 16 // 邮件
	TYPE_RED_FRIEND   = 17 // 好友
)

//const (
//	RED_EMBATTLE = 1 << iota
//	RED_HERO
//	RED_EQUIP
//	RED_ARMY
//	RED_STAR
//	RED_BOSS
//	RED_BAG
//	RED_WORLD
//	RED_SHOP
//	RED_TECH
//	RED_BATTLE
//	RED_BEAUTY
//	RED_GOD
//	RED_UNION
//	RED_PVP
//	RED_MAIL
//	RED_FRIEND
//)
