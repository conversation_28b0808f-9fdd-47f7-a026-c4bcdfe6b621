package network

import (
	"sync"
	"time"
	"zone/lib/core"
)

// ConnectionPool 连接池管理器，用于优化连接资源使用
type ConnectionPool struct {
	maxConnections     int                // 最大连接数
	currentConnections int                // 当前连接数
	connectionLimit    map[string]int     // 每IP连接限制
	ipConnections      map[string]int     // 每IP当前连接数
	sessions           map[int64]*Session // 活跃会话映射
	mutex              sync.RWMutex       // 读写锁
	cleanupTicker      *time.Ticker       // 清理定时器
	stopChan           chan struct{}      // 停止信号
	isRunning          bool               // 运行状态

	// 统计信息
	totalConnections    int64 // 总连接数
	rejectedConnections int64 // 拒绝连接数
	cleanedConnections  int64 // 清理连接数
}

// NewConnectionPool 创建新的连接池
func NewConnectionPool(maxConnections int) *ConnectionPool {
	return &ConnectionPool{
		maxConnections:  maxConnections,
		connectionLimit: make(map[string]int),
		ipConnections:   make(map[string]int),
		sessions:        make(map[int64]*Session),
		cleanupTicker:   time.NewTicker(30 * time.Second), // 30秒清理一次
		stopChan:        make(chan struct{}),
	}
}

// Start 启动连接池管理
func (cp *ConnectionPool) Start() {
	cp.mutex.Lock()
	defer cp.mutex.Unlock()

	if cp.isRunning {
		return
	}

	cp.isRunning = true
	go cp.cleanupLoop()
	core.LogInfo("连接池管理器已启动，最大连接数:", cp.maxConnections)
}

// Stop 停止连接池管理
func (cp *ConnectionPool) Stop() {
	cp.mutex.Lock()
	defer cp.mutex.Unlock()

	if !cp.isRunning {
		return
	}

	cp.isRunning = false
	close(cp.stopChan)
	cp.cleanupTicker.Stop()

	// 关闭所有活跃连接
	for _, session := range cp.sessions {
		session.CloseChan()
	}

	core.LogInfo("连接池管理器已停止")
}

// SetIPConnectionLimit 设置单个IP的连接限制
func (cp *ConnectionPool) SetIPConnectionLimit(ip string, limit int) {
	cp.mutex.Lock()
	defer cp.mutex.Unlock()
	cp.connectionLimit[ip] = limit
}

// CanAcceptConnection 检查是否可以接受新连接
func (cp *ConnectionPool) CanAcceptConnection(ip string) bool {
	cp.mutex.RLock()
	defer cp.mutex.RUnlock()

	// 检查总连接数限制
	if cp.currentConnections >= cp.maxConnections {
		core.LogDebug("达到最大连接数限制，拒绝连接，IP:", ip, "当前连接数:", cp.currentConnections)
		return false
	}

	// 检查单IP连接数限制
	if limit, exists := cp.connectionLimit[ip]; exists {
		if cp.ipConnections[ip] >= limit {
			core.LogDebug("达到单IP连接数限制，拒绝连接，IP:", ip, "当前连接数:", cp.ipConnections[ip], "限制:", limit)
			return false
		}
	} else {
		// 默认单IP限制为10个连接
		if cp.ipConnections[ip] >= 10 {
			core.LogDebug("达到默认单IP连接数限制，拒绝连接，IP:", ip, "当前连接数:", cp.ipConnections[ip])
			return false
		}
	}

	return true
}

// AddConnection 添加新连接
func (cp *ConnectionPool) AddConnection(session *Session, ip string) bool {
	cp.mutex.Lock()
	defer cp.mutex.Unlock()

	// 再次检查连接限制（双重检查）
	if cp.currentConnections >= cp.maxConnections {
		cp.rejectedConnections++
		return false
	}

	// 更新连接计数
	cp.currentConnections++
	cp.ipConnections[ip]++
	cp.totalConnections++

	// 添加到会话映射
	cp.sessions[session.ID] = session

	// 为高流量IP启用批处理
	if cp.ipConnections[ip] > 3 {
		session.EnableMessageBatch()
		session.SetBatchConfig(8, 30*time.Millisecond) // 8条消息或30ms批处理
	}

	core.LogDebug("新连接已添加，会话ID:", session.ID, "IP:", ip, "当前总连接数:", cp.currentConnections)
	return true
}

// RemoveConnection 移除连接
func (cp *ConnectionPool) RemoveConnection(sessionID int64, ip string) {
	cp.mutex.Lock()
	defer cp.mutex.Unlock()

	// 从会话映射中移除
	if session, exists := cp.sessions[sessionID]; exists {
		delete(cp.sessions, sessionID)

		// 停止批处理器
		if session.messageBatch != nil {
			session.messageBatch.Stop()
		}
	}

	// 更新连接计数
	if cp.currentConnections > 0 {
		cp.currentConnections--
	}

	if cp.ipConnections[ip] > 0 {
		cp.ipConnections[ip]--

		// 如果IP没有连接了，清理记录
		if cp.ipConnections[ip] == 0 {
			delete(cp.ipConnections, ip)
		}
	}

	core.LogDebug("连接已移除，会话ID:", sessionID, "IP:", ip, "当前总连接数:", cp.currentConnections)
}

// cleanupLoop 清理循环
func (cp *ConnectionPool) cleanupLoop() {
	for {
		select {
		case <-cp.stopChan:
			return
		case <-cp.cleanupTicker.C:
			cp.cleanupInactiveConnections()
		}
	}
}

// cleanupInactiveConnections 清理不活跃的连接
func (cp *ConnectionPool) cleanupInactiveConnections() {
	cp.mutex.Lock()
	defer cp.mutex.Unlock()

	cleanedCount := 0

	for sessionID, session := range cp.sessions {
		// 检查连接健康状态
		if !session.IsConnectionHealthy() {
			// 获取IP地址用于更新计数
			ip := session.IP

			// 关闭不健康的连接
			session.CloseChan()
			delete(cp.sessions, sessionID)

			// 更新连接计数
			if cp.currentConnections > 0 {
				cp.currentConnections--
			}

			if cp.ipConnections[ip] > 0 {
				cp.ipConnections[ip]--
				if cp.ipConnections[ip] == 0 {
					delete(cp.ipConnections, ip)
				}
			}

			cleanedCount++
			cp.cleanedConnections++
		}
	}

	if cleanedCount > 0 {
		core.LogInfo("清理不活跃连接完成，清理数量:", cleanedCount, "当前连接数:", cp.currentConnections)
	}
}

// GetStats 获取连接池统计信息
func (cp *ConnectionPool) GetStats() (int, int, int64, int64, int64) {
	cp.mutex.RLock()
	defer cp.mutex.RUnlock()

	return cp.currentConnections, cp.maxConnections, cp.totalConnections, cp.rejectedConnections, cp.cleanedConnections
}

// GetIPStats 获取IP连接统计
func (cp *ConnectionPool) GetIPStats() map[string]int {
	cp.mutex.RLock()
	defer cp.mutex.RUnlock()

	// 复制映射以避免并发访问问题
	stats := make(map[string]int)
	for ip, count := range cp.ipConnections {
		stats[ip] = count
	}

	return stats
}

// LogStats 记录统计信息
func (cp *ConnectionPool) LogStats() {
	current, max, total, rejected, cleaned := cp.GetStats()
	ipStats := cp.GetIPStats()

	core.LogInfo("=== 连接池统计信息 ===")
	core.LogInfo("当前连接数:", current, "/", max)
	core.LogInfo("总连接数:", total)
	core.LogInfo("拒绝连接数:", rejected)
	core.LogInfo("清理连接数:", cleaned)
	core.LogInfo("IP连接分布:", len(ipStats), "个不同IP")

	// 显示前10个最活跃的IP
	count := 0
	for ip, connections := range ipStats {
		if count >= 10 {
			break
		}
		core.LogInfo("  IP:", ip, "连接数:", connections)
		count++
	}
	core.LogInfo("=== 统计信息结束 ===")
}
