package manager

import (
	"log"
	"runtime/debug"
	"sync"
	"time"
	"zone/game/Request"
	"zone/game/mods"
	"zone/lib/core"
	"zone/lib/network"
)

// RoomValueCategory 房间价值分类枚举
type RoomValueCategory int

const (
	RoomValueLow    RoomValueCategory = iota // 低价值房间 (<100)
	RoomValueMedium                          // 中等价值房间 (100-999)
	RoomValueHigh                            // 高价值房间 (1000-9999)
	RoomValueUltra                           // 超高价值房间 (>=10000)
)

// RoomMatchStatus 房间匹配状态枚举
type RoomMatchStatus int

const (
	RoomStatusWaiting  RoomMatchStatus = iota // 等待匹配
	RoomStatusMatching                        // 正在匹配中
	RoomStatusMatched                         // 已匹配
)

// RoomInfo 房间信息结构
type RoomInfo struct {
	Session     *network.Session
	Room        *mods.LiveRoom
	GiftValue   int64
	Category    RoomValueCategory
	MatchStatus RoomMatchStatus
	AddTime     int64 // 添加时间戳
	LastUpdate  int64 // 最后更新时间戳
}

type MultiGameMatchMgr struct {
	// 房间分类列表，使用 sync.Map 保证并发安全
	LowValueRooms    *sync.Map // map[*network.Session]*RoomInfo - 低价值房间列表 (<100)
	MediumValueRooms *sync.Map // map[*network.Session]*RoomInfo - 中等价值房间列表 (100-999)
	HighValueRooms   *sync.Map // map[*network.Session]*RoomInfo - 高价值房间列表 (1000-9999)
	UltraValueRooms  *sync.Map // map[*network.Session]*RoomInfo - 超高价值房间列表 (>=10000)

	// 房间分类锁，用于保护分类操作
	categoryMutex sync.RWMutex

	// 定时器协调机制
	secondTaskRunning bool       // 秒级任务是否正在运行
	secondTaskMutex   sync.Mutex // 秒级任务锁
}

var s_MultiGameMatchMgr *MultiGameMatchMgr

func GetMultiGameMatchMgr() *MultiGameMatchMgr {
	if s_MultiGameMatchMgr == nil {
		s_MultiGameMatchMgr = &MultiGameMatchMgr{
			LowValueRooms:    &sync.Map{},
			MediumValueRooms: &sync.Map{},
			HighValueRooms:   &sync.Map{},
			UltraValueRooms:  &sync.Map{},
		}
		go s_MultiGameMatchMgr.Match()

		mods.RegisterMultiGameMatchManager(s_MultiGameMatchMgr)
	}
	return s_MultiGameMatchMgr
}

func (mgr *MultiGameMatchMgr) AddSession(session *network.Session) {
	// 验证输入参数
	if session == nil {
		core.LogInfo("MultiGameMatchMgr: AddSession 失败，session为空")
		return
	}

	// 获取房间信息
	room := mods.GetSessionRoomMgr().GetRoom(session)
	if room == nil {
		core.LogError("MultiGameMatchMgr: AddSession 失败，无法获取房间信息", "SessionID:", session.GetId())
		return
	}

	// 获取房间数据
	liveRoomData := room.LiveRoomData
	if liveRoomData == nil {
		core.LogError("MultiGameMatchMgr: AddSession 失败，房间数据为空", "RoomID:", room.RoomId, "SessionID:", session.GetId())
		return
	}

	// 根据房间的 GiftValue 进行分类
	giftValue := liveRoomData.GiftValue
	mgr.addRoomToCategory(session, room, giftValue)

	core.LogInfo("MultiGameMatchMgr: 成功添加房间到匹配系统",
		"RoomID:", room.RoomId,
		"GiftValue:", giftValue,
		"SessionID:", session.GetId())
}

func (mgr *MultiGameMatchMgr) RemoveSession(session *network.Session) {
	// 验证输入参数
	if session == nil {
		core.LogError("MultiGameMatchMgr: RemoveSession 失败，session为空")
		return
	}

	// 从房间分类中移除
	mgr.removeRoomFromCategory(session)

	core.LogInfo("MultiGameMatchMgr: 成功从匹配系统移除房间", "SessionID:", session.GetId())
}

func (mgr *MultiGameMatchMgr) Match() {
	defer func() {
		x := recover()
		if x != nil {
			log.Println(x, string(debug.Stack()))
			core.LogError(x, string(debug.Stack()))
		}
	}()

	// 创建两个定时器：1秒和10秒
	secondTicker := time.NewTicker(time.Second)         // 秒级定时器
	tenSecondTicker := time.NewTicker(time.Second * 10) // 10秒定时器

	// 确保所有Ticker都被正确停止
	defer func() {
		secondTicker.Stop()
		tenSecondTicker.Stop()
		core.LogDebug("MultiGameMatchMgr Match停止")
	}()

	for {
		// 检查应用关闭或房间停止
		if core.GetZoneApp().IsClosed() {
			break
		}

		select {
		case <-secondTicker.C:
			// 执行秒级任务
			mgr.onSecond()
		case <-tenSecondTicker.C:
			// 等待秒级任务完成后再执行10秒任务
			mgr.waitForSecondTaskAndExecuteTimer()
		}
	}
}

// onSecond 秒级定时任务 - 主要处理房间价值重新分类
func (mgr *MultiGameMatchMgr) onSecond() {
	mgr.secondTaskMutex.Lock()
	mgr.secondTaskRunning = true
	defer func() {
		mgr.secondTaskRunning = false
		mgr.secondTaskMutex.Unlock()
	}()

	// 执行房间价值重新分类检查
	mgr.recheckRoomCategories()
}

// waitForSecondTaskAndExecuteTimer 等待秒级任务完成后执行10秒定时任务
func (mgr *MultiGameMatchMgr) waitForSecondTaskAndExecuteTimer() {
	// 等待秒级任务完成
	mgr.secondTaskMutex.Lock()
	defer mgr.secondTaskMutex.Unlock()

	// 执行10秒定时任务
	mgr.onTimer()
}

func (mgr *MultiGameMatchMgr) onTimer() {
	// 定期记录房间分类统计信息（每10秒一次）
	mgr.LogRoomStatistics()

	// 执行房间匹配逻辑
	mgr.performRoomMatching()

	// 可以在这里添加其他定时任务，比如：
	// - 清理长时间未活动的房间
	// - 检查房间状态等
}

func (mgr *MultiGameMatchMgr) EnterMultiGame(roomA, roomB *RoomInfo) {
	defer func() {
		if r := recover(); r != nil {
			core.LogError("MultiGameMatchMgr: EnterMultiGame 发生错误", "Error:", r)
		}
	}()

	// 验证输入参数
	if roomA == nil || roomB == nil {
		core.LogError("MultiGameMatchMgr: EnterMultiGame 失败，房间信息为空")
		return
	}

	if roomA.Room == nil || roomB.Room == nil {
		core.LogError("MultiGameMatchMgr: EnterMultiGame 失败，房间对象为空")
		return
	}

	if roomA.Session == nil || roomB.Session == nil {
		core.LogError("MultiGameMatchMgr: EnterMultiGame 失败，会话为空")
		return
	}

	core.LogInfo("MultiGameMatchMgr: 房间匹配成功，进入多人游戏",
		"RoomA_ID:", roomA.Room.RoomId,
		"RoomA_GiftValue:", roomA.GiftValue,
		"RoomB_ID:", roomB.Room.RoomId,
		"RoomB_GiftValue:", roomB.GiftValue,
		"Category:", mgr.getCategoryName(roomA.Category))

	// 获取多人游戏管理器
	multiGameMgr := GetMultiGameMgr()
	if multiGameMgr == nil {
		core.LogError("MultiGameMatchMgr: 无法获取多人游戏管理器")
		return
	}

	// 创建多人游戏
	liveMultiGame := multiGameMgr.NewMultiGame(roomA, roomB)
	if liveMultiGame == nil {
		core.LogError("MultiGameMatchMgr: 创建多人游戏失败")
		return
	}

	// 发送进入多人游戏请求
	gameID := liveMultiGame.GetGameID()

	// 检查会话是否仍然连接
	if roomA.Session.IsConnected() {
		Request.EnterMutiGameRequest(roomA.Session, gameID)
		core.LogDebug("MultiGameMatchMgr: 已发送进入多人游戏请求",
			"RoomA_ID:", roomA.Room.RoomId,
			"GameID:", gameID)
	} else {
		core.LogError("MultiGameMatchMgr: RoomA 会话已断开连接", "RoomID:", roomA.Room.RoomId)
	}

	if roomB.Session.IsConnected() {
		Request.EnterMutiGameRequest(roomB.Session, gameID)
		core.LogDebug("MultiGameMatchMgr: 已发送进入多人游戏请求",
			"RoomB_ID:", roomB.Room.RoomId,
			"GameID:", gameID)
	} else {
		core.LogError("MultiGameMatchMgr: RoomB 会话已断开连接", "RoomID:", roomB.Room.RoomId)
	}

	core.LogInfo("MultiGameMatchMgr: 多人游戏创建完成",
		"GameID:", gameID,
		"RoomA_ID:", roomA.Room.RoomId,
		"RoomB_ID:", roomB.Room.RoomId)
}

// recheckRoomCategories 重新检查所有房间的分类
func (mgr *MultiGameMatchMgr) recheckRoomCategories() {
	defer func() {
		if r := recover(); r != nil {
			core.LogError("MultiGameMatchMgr: 房间重新分类过程中发生错误", "Error:", r)
		}
	}()

	mgr.categoryMutex.Lock()
	defer mgr.categoryMutex.Unlock()

	// 检查所有分类中的房间
	categories := []*sync.Map{
		mgr.LowValueRooms,
		mgr.MediumValueRooms,
		mgr.HighValueRooms,
		mgr.UltraValueRooms,
	}

	for categoryIndex, roomMap := range categories {
		currentCategory := RoomValueCategory(categoryIndex)
		var roomsToMove []*RoomInfo
		var roomsToRemove []*network.Session

		// 遍历当前分类中的所有房间
		roomMap.Range(func(key, value interface{}) bool {
			session := key.(*network.Session)
			roomInfo := value.(*RoomInfo)

			// 检查房间是否仍然有效
			if !mgr.isRoomValidForMatching(roomInfo) {
				roomsToRemove = append(roomsToRemove, session)
				core.LogDebug("MultiGameMatchMgr: 标记无效房间待移除",
					"RoomID:", roomInfo.Room.RoomId,
					"Category:", mgr.getCategoryName(currentCategory),
					"SessionID:", session.GetId())
				return true
			}

			// 获取房间当前的 GiftValue
			if roomInfo.Room != nil && roomInfo.Room.LiveRoomData != nil {
				currentGiftValue := roomInfo.Room.LiveRoomData.GiftValue
				correctCategory := mgr.categorizeRoomByGiftValue(currentGiftValue)

				// 如果房间的分类发生了变化
				if correctCategory != currentCategory {
					// 更新房间信息
					roomInfo.GiftValue = currentGiftValue
					roomInfo.Category = correctCategory
					roomInfo.LastUpdate = time.Now().Unix()

					// 标记需要移动
					roomsToMove = append(roomsToMove, roomInfo)

					core.LogDebug("MultiGameMatchMgr: 检测到房间分类变化",
						"RoomID:", roomInfo.Room.RoomId,
						"OldCategory:", mgr.getCategoryName(currentCategory),
						"NewCategory:", mgr.getCategoryName(correctCategory),
						"OldGiftValue:", roomInfo.GiftValue,
						"NewGiftValue:", currentGiftValue,
						"SessionID:", session.GetId())
				}
			}
			return true
		})

		// 移除无效房间
		for _, session := range roomsToRemove {
			roomMap.Delete(session)
		}

		// 移动需要重新分类的房间
		for _, roomInfo := range roomsToMove {
			// 从当前分类中移除
			roomMap.Delete(roomInfo.Session)

			// 添加到正确的分类中
			correctRoomMap := mgr.getRoomMapByCategory(roomInfo.Category)
			if correctRoomMap != nil {
				correctRoomMap.Store(roomInfo.Session, roomInfo)

				core.LogInfo("MultiGameMatchMgr: 房间已重新分类",
					"RoomID:", roomInfo.Room.RoomId,
					"FromCategory:", mgr.getCategoryName(currentCategory),
					"ToCategory:", mgr.getCategoryName(roomInfo.Category),
					"GiftValue:", roomInfo.GiftValue,
					"SessionID:", roomInfo.Session.GetId())
			} else {
				core.LogError("MultiGameMatchMgr: 无法获取目标分类映射",
					"Category:", roomInfo.Category,
					"RoomID:", roomInfo.Room.RoomId)
			}
		}
	}
}

// performRoomMatching 执行房间匹配逻辑
func (mgr *MultiGameMatchMgr) performRoomMatching() {
	defer func() {
		if r := recover(); r != nil {
			core.LogError("MultiGameMatchMgr: 房间匹配过程中发生错误", "Error:", r)
		}
	}()

	mgr.categoryMutex.Lock()
	defer mgr.categoryMutex.Unlock()

	totalMatched := 0

	// 按优先级顺序匹配：超高价值 -> 高价值 -> 中等价值 -> 低价值
	categories := []struct {
		category RoomValueCategory
		roomMap  *sync.Map
	}{
		{RoomValueUltra, mgr.UltraValueRooms},
		{RoomValueHigh, mgr.HighValueRooms},
		{RoomValueMedium, mgr.MediumValueRooms},
		{RoomValueLow, mgr.LowValueRooms},
	}

	for _, cat := range categories {
		matchedCount := mgr.matchRoomsInCategory(cat.category, cat.roomMap)
		if matchedCount > 0 {
			totalMatched += matchedCount
			core.LogInfo("MultiGameMatchMgr: 分类匹配完成",
				"Category:", mgr.getCategoryName(cat.category),
				"MatchedPairs:", matchedCount)
		}
	}

	if totalMatched > 0 {
		core.LogInfo("MultiGameMatchMgr: 本轮匹配完成", "TotalMatchedPairs:", totalMatched)
	}
}

// matchRoomsInCategory 在指定分类中进行房间匹配
func (mgr *MultiGameMatchMgr) matchRoomsInCategory(category RoomValueCategory, roomMap *sync.Map) int {
	defer func() {
		if r := recover(); r != nil {
			core.LogError("MultiGameMatchMgr: matchRoomsInCategory 发生错误",
				"Category:", mgr.getCategoryName(category),
				"Error:", r)
		}
	}()

	var availableRooms []*RoomInfo

	// 收集所有可用于匹配的房间
	roomMap.Range(func(key, value interface{}) bool {
		defer func() {
			if r := recover(); r != nil {
				core.LogError("MultiGameMatchMgr: 处理房间时发生错误", "Error:", r)
			}
		}()

		roomInfo, ok := value.(*RoomInfo)
		if !ok {
			core.LogError("MultiGameMatchMgr: 房间信息类型转换失败")
			return true
		}

		// 只匹配等待状态的房间
		if roomInfo.MatchStatus == RoomStatusWaiting {
			// 检查房间和会话是否有效
			if mgr.isRoomValidForMatching(roomInfo) {
				availableRooms = append(availableRooms, roomInfo)
				core.LogDebug("MultiGameMatchMgr: 添加可匹配房间",
					"RoomID:", roomInfo.Room.RoomId,
					"Category:", mgr.getCategoryName(category))
			} else {
				// 移除无效房间
				roomMap.Delete(key)
				core.LogDebug("MultiGameMatchMgr: 移除无效房间",
					"RoomID:", roomInfo.Room.RoomId,
					"Category:", mgr.getCategoryName(category))
			}
		}
		return true
	})

	core.LogDebug("MultiGameMatchMgr: 可匹配房间统计",
		"Category:", mgr.getCategoryName(category),
		"AvailableRooms:", len(availableRooms))

	matchedPairs := 0

	// 两两配对
	for i := 0; i < len(availableRooms)-1; i += 2 {
		roomA := availableRooms[i]
		roomB := availableRooms[i+1]

		// 再次验证房间有效性
		if !mgr.isRoomValidForMatching(roomA) || !mgr.isRoomValidForMatching(roomB) {
			core.LogError("MultiGameMatchMgr: 匹配时发现房间无效",
				"RoomA_Valid:", mgr.isRoomValidForMatching(roomA),
				"RoomB_Valid:", mgr.isRoomValidForMatching(roomB))
			continue
		}

		// 标记房间为匹配状态
		roomA.MatchStatus = RoomStatusMatching
		roomB.MatchStatus = RoomStatusMatching

		core.LogInfo("MultiGameMatchMgr: 开始匹配房间",
			"Category:", mgr.getCategoryName(category),
			"RoomA_ID:", roomA.Room.RoomId,
			"RoomA_GiftValue:", roomA.GiftValue,
			"RoomB_ID:", roomB.Room.RoomId,
			"RoomB_GiftValue:", roomB.GiftValue)

		// 执行匹配
		mgr.EnterMultiGame(roomA, roomB)

		// 从待匹配列表中移除已匹配的房间
		roomMap.Delete(roomA.Session)
		roomMap.Delete(roomB.Session)

		matchedPairs++

		core.LogInfo("MultiGameMatchMgr: 房间匹配完成",
			"Category:", mgr.getCategoryName(category),
			"RoomA_ID:", roomA.Room.RoomId,
			"RoomB_ID:", roomB.Room.RoomId,
			"MatchedPairs:", matchedPairs)
	}

	return matchedPairs
}

// isRoomValidForMatching 检查房间是否有效可用于匹配
func (mgr *MultiGameMatchMgr) isRoomValidForMatching(roomInfo *RoomInfo) bool {
	defer func() {
		if r := recover(); r != nil {
			core.LogError("MultiGameMatchMgr: isRoomValidForMatching 发生错误", "Error:", r)
		}
	}()

	if roomInfo == nil {
		core.LogDebug("MultiGameMatchMgr: 房间信息为空")
		return false
	}

	// 检查会话是否有效
	if roomInfo.Session == nil {
		core.LogDebug("MultiGameMatchMgr: 房间会话为空", "RoomID:", roomInfo.Room.RoomId)
		return false
	}

	if !roomInfo.Session.IsConnected() {
		core.LogDebug("MultiGameMatchMgr: 房间会话未连接", "RoomID:", roomInfo.Room.RoomId)
		return false
	}

	// 检查房间是否有效
	if roomInfo.Room == nil {
		core.LogDebug("MultiGameMatchMgr: 房间对象为空")
		return false
	}

	if roomInfo.Room.LiveRoomData == nil {
		core.LogDebug("MultiGameMatchMgr: 房间数据为空", "RoomID:", roomInfo.Room.RoomId)
		return false
	}

	// 检查房间是否已停止
	if roomInfo.Room.IsStopped {
		core.LogDebug("MultiGameMatchMgr: 房间已停止", "RoomID:", roomInfo.Room.RoomId)
		return false
	}

	return true
}

// GetRoomCountByStatus 获取指定状态的房间数量
func (mgr *MultiGameMatchMgr) GetRoomCountByStatus(status RoomMatchStatus) int {
	mgr.categoryMutex.RLock()
	defer mgr.categoryMutex.RUnlock()

	count := 0
	categories := []*sync.Map{
		mgr.LowValueRooms,
		mgr.MediumValueRooms,
		mgr.HighValueRooms,
		mgr.UltraValueRooms,
	}

	for _, roomMap := range categories {
		roomMap.Range(func(key, value interface{}) bool {
			if roomInfo, ok := value.(*RoomInfo); ok {
				if roomInfo.MatchStatus == status {
					count++
				}
			}
			return true
		})
	}

	return count
}

// GetMatchStatusName 获取匹配状态名称（用于日志）
func (mgr *MultiGameMatchMgr) GetMatchStatusName(status RoomMatchStatus) string {
	switch status {
	case RoomStatusWaiting:
		return "等待匹配"
	case RoomStatusMatching:
		return "正在匹配"
	case RoomStatusMatched:
		return "已匹配"
	default:
		return "未知状态"
	}
}

// UpdateRoomMatchStatus 更新房间匹配状态
func (mgr *MultiGameMatchMgr) UpdateRoomMatchStatus(session *network.Session, status RoomMatchStatus) bool {
	if session == nil {
		return false
	}

	mgr.categoryMutex.Lock()
	defer mgr.categoryMutex.Unlock()

	// 从所有分类中查找房间
	categories := []*sync.Map{
		mgr.LowValueRooms,
		mgr.MediumValueRooms,
		mgr.HighValueRooms,
		mgr.UltraValueRooms,
	}

	for _, roomMap := range categories {
		if roomInfo, exists := roomMap.Load(session); exists {
			if info, ok := roomInfo.(*RoomInfo); ok {
				oldStatus := info.MatchStatus
				info.MatchStatus = status
				info.LastUpdate = time.Now().Unix()

				core.LogDebug("MultiGameMatchMgr: 房间匹配状态已更新",
					"RoomID:", info.Room.RoomId,
					"OldStatus:", mgr.GetMatchStatusName(oldStatus),
					"NewStatus:", mgr.GetMatchStatusName(status),
					"SessionID:", session.GetId())
				return true
			}
		}
	}

	return false
}

// categorizeRoomByGiftValue 根据礼物价值对房间进行分类
func (mgr *MultiGameMatchMgr) categorizeRoomByGiftValue(giftValue int64) RoomValueCategory {
	if giftValue < 100 {
		return RoomValueLow
	} else if giftValue < 1000 {
		return RoomValueMedium
	} else if giftValue < 10000 {
		return RoomValueHigh
	} else {
		return RoomValueUltra
	}
}

// getRoomMapByCategory 根据分类获取对应的房间映射
func (mgr *MultiGameMatchMgr) getRoomMapByCategory(category RoomValueCategory) *sync.Map {
	switch category {
	case RoomValueLow:
		return mgr.LowValueRooms
	case RoomValueMedium:
		return mgr.MediumValueRooms
	case RoomValueHigh:
		return mgr.HighValueRooms
	case RoomValueUltra:
		return mgr.UltraValueRooms
	default:
		return mgr.LowValueRooms // 默认返回低价值房间列表
	}
}

// addRoomToCategory 将房间添加到指定分类
func (mgr *MultiGameMatchMgr) addRoomToCategory(session *network.Session, room *mods.LiveRoom, giftValue int64) {
	if session == nil || room == nil {
		core.LogError("MultiGameMatchMgr: 无法添加房间到分类，session或room为空")
		return
	}

	mgr.categoryMutex.Lock()
	defer mgr.categoryMutex.Unlock()

	// 检查房间是否已经存在于某个分类中
	if existingInfo := mgr.getRoomInfoBySessionUnsafe(session); existingInfo != nil {
		core.LogDebug("MultiGameMatchMgr: 房间已存在于分类中，将先移除再重新添加",
			"RoomID:", room.RoomId,
			"ExistingCategory:", mgr.getCategoryName(existingInfo.Category),
			"SessionID:", session.GetId())
		mgr.removeRoomFromCategoryUnsafe(session)
	}

	// 确定房间分类
	category := mgr.categorizeRoomByGiftValue(giftValue)

	// 创建房间信息
	roomInfo := &RoomInfo{
		Session:     session,
		Room:        room,
		GiftValue:   giftValue,
		Category:    category,
		MatchStatus: RoomStatusWaiting, // 初始状态为等待匹配
		AddTime:     time.Now().Unix(),
		LastUpdate:  time.Now().Unix(),
	}

	// 获取对应的房间映射并添加
	roomMap := mgr.getRoomMapByCategory(category)
	if roomMap == nil {
		core.LogError("MultiGameMatchMgr: 无法获取房间映射", "Category:", category)
		return
	}

	roomMap.Store(session, roomInfo)

	// 记录日志
	categoryName := mgr.getCategoryName(category)
	core.LogDebug("MultiGameMatchMgr: 房间已添加到分类",
		"RoomID:", room.RoomId,
		"GiftValue:", giftValue,
		"Category:", categoryName,
		"SessionID:", session.GetId())
}

// removeRoomFromCategory 从分类中移除房间
func (mgr *MultiGameMatchMgr) removeRoomFromCategory(session *network.Session) {
	if session == nil {
		core.LogError("MultiGameMatchMgr: 无法移除房间，session为空")
		return
	}

	mgr.categoryMutex.Lock()
	defer mgr.categoryMutex.Unlock()

	// 从所有分类中查找并移除
	categories := []*sync.Map{
		mgr.LowValueRooms,
		mgr.MediumValueRooms,
		mgr.HighValueRooms,
		mgr.UltraValueRooms,
	}

	for i, roomMap := range categories {
		if roomInfo, exists := roomMap.Load(session); exists {
			roomMap.Delete(session)

			// 记录日志
			if info, ok := roomInfo.(*RoomInfo); ok {
				categoryName := mgr.getCategoryName(RoomValueCategory(i))
				core.LogDebug("MultiGameMatchMgr: 房间已从分类中移除",
					"RoomID:", info.Room.RoomId,
					"Category:", categoryName,
					"SessionID:", session.GetId())
			}
			break
		}
	}
}

// getCategoryName 获取分类名称（用于日志）
func (mgr *MultiGameMatchMgr) getCategoryName(category RoomValueCategory) string {
	switch category {
	case RoomValueLow:
		return "低价值(<100)"
	case RoomValueMedium:
		return "中等价值(100-999)"
	case RoomValueHigh:
		return "高价值(1000-9999)"
	case RoomValueUltra:
		return "超高价值(>=10000)"
	default:
		return "未知分类"
	}
}

// GetRoomCountByCategory 获取指定分类的房间数量
func (mgr *MultiGameMatchMgr) GetRoomCountByCategory(category RoomValueCategory) int {
	mgr.categoryMutex.RLock()
	defer mgr.categoryMutex.RUnlock()

	roomMap := mgr.getRoomMapByCategory(category)
	count := 0
	roomMap.Range(func(key, value interface{}) bool {
		count++
		return true
	})
	return count
}

// GetTotalRoomCount 获取所有分类的房间总数
func (mgr *MultiGameMatchMgr) GetTotalRoomCount() int {
	return mgr.GetRoomCountByCategory(RoomValueLow) +
		mgr.GetRoomCountByCategory(RoomValueMedium) +
		mgr.GetRoomCountByCategory(RoomValueHigh) +
		mgr.GetRoomCountByCategory(RoomValueUltra)
}

// GetRoomsByCategory 获取指定分类的所有房间信息
func (mgr *MultiGameMatchMgr) GetRoomsByCategory(category RoomValueCategory) []*RoomInfo {
	mgr.categoryMutex.RLock()
	defer mgr.categoryMutex.RUnlock()

	roomMap := mgr.getRoomMapByCategory(category)
	var rooms []*RoomInfo

	roomMap.Range(func(key, value interface{}) bool {
		if roomInfo, ok := value.(*RoomInfo); ok {
			rooms = append(rooms, roomInfo)
		}
		return true
	})

	return rooms
}

// GetRoomInfoBySession 根据session获取房间信息
func (mgr *MultiGameMatchMgr) GetRoomInfoBySession(session *network.Session) *RoomInfo {
	if session == nil {
		return nil
	}

	mgr.categoryMutex.RLock()
	defer mgr.categoryMutex.RUnlock()

	// 从所有分类中查找
	categories := []*sync.Map{
		mgr.LowValueRooms,
		mgr.MediumValueRooms,
		mgr.HighValueRooms,
		mgr.UltraValueRooms,
	}

	for _, roomMap := range categories {
		if roomInfo, exists := roomMap.Load(session); exists {
			if info, ok := roomInfo.(*RoomInfo); ok {
				return info
			}
		}
	}

	return nil
}

// LogRoomStatistics 记录房间分类统计信息
func (mgr *MultiGameMatchMgr) LogRoomStatistics() {
	lowCount := mgr.GetRoomCountByCategory(RoomValueLow)
	mediumCount := mgr.GetRoomCountByCategory(RoomValueMedium)
	highCount := mgr.GetRoomCountByCategory(RoomValueHigh)
	ultraCount := mgr.GetRoomCountByCategory(RoomValueUltra)
	totalCount := mgr.GetTotalRoomCount()

	// 统计匹配状态
	waitingCount := mgr.GetRoomCountByStatus(RoomStatusWaiting)
	matchingCount := mgr.GetRoomCountByStatus(RoomStatusMatching)
	matchedCount := mgr.GetRoomCountByStatus(RoomStatusMatched)

	core.LogInfo("MultiGameMatchMgr: 房间分类统计",
		"低价值房间:", lowCount,
		"中等价值房间:", mediumCount,
		"高价值房间:", highCount,
		"超高价值房间:", ultraCount,
		"总房间数:", totalCount,
		"等待匹配:", waitingCount,
		"正在匹配:", matchingCount,
		"已匹配:", matchedCount)
}

// getRoomInfoBySessionUnsafe 根据session获取房间信息（不加锁版本，内部使用）
func (mgr *MultiGameMatchMgr) getRoomInfoBySessionUnsafe(session *network.Session) *RoomInfo {
	if session == nil {
		return nil
	}

	// 从所有分类中查找
	categories := []*sync.Map{
		mgr.LowValueRooms,
		mgr.MediumValueRooms,
		mgr.HighValueRooms,
		mgr.UltraValueRooms,
	}

	for _, roomMap := range categories {
		if roomInfo, exists := roomMap.Load(session); exists {
			if info, ok := roomInfo.(*RoomInfo); ok {
				return info
			}
		}
	}

	return nil
}

// removeRoomFromCategoryUnsafe 从分类中移除房间（不加锁版本，内部使用）
func (mgr *MultiGameMatchMgr) removeRoomFromCategoryUnsafe(session *network.Session) {
	if session == nil {
		return
	}

	// 从所有分类中查找并移除
	categories := []*sync.Map{
		mgr.LowValueRooms,
		mgr.MediumValueRooms,
		mgr.HighValueRooms,
		mgr.UltraValueRooms,
	}

	for i, roomMap := range categories {
		if roomInfo, exists := roomMap.Load(session); exists {
			roomMap.Delete(session)

			// 记录日志
			if info, ok := roomInfo.(*RoomInfo); ok {
				categoryName := mgr.getCategoryName(RoomValueCategory(i))
				core.LogDebug("MultiGameMatchMgr: 房间已从分类中移除",
					"RoomID:", info.Room.RoomId,
					"GiftValue:", info.GiftValue,
					"Category:", categoryName,
					"SessionID:", session.GetId())
			}
			break
		}
	}
}
