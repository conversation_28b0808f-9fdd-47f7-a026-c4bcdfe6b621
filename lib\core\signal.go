// JoysGames copyrights this specification. No part of this specification may be
// reproduced in any form or means, without the prior written consent of JoysGames.
//
// This specification is preliminary and is subject to change at any time without notice.
// JoysGames assumes no responsibility for any errors contained herein.
//
// The above copyright notice and this permission notice shall be included
// in all copies or substantial portions of the Software.
// @package JGServer
// @copyright joysgames.cn All rights reserved.
// @version v1.0

package core

import (
	"log"
	"reflect"
	"sync/atomic"
)

// 总线信号(多线程安全)
type Signal struct {
	rec atomic.Value // 监听者记录器
}

// 获取监听者
func (that *Signal) getListener() *Delegate {
	listerer := that.rec.Load()
	if listerer == nil {
		that.rec.Store(new(Delegate))
		listerer = that.rec.Load()
	}
	return listerer.(*Delegate)
}

// 注册更新事件
func (that *Signal) AddListener(callback interface{}) {
	listerer := that.getListener()
	listerer.AddListener(1, callback)
}

// 注册单次更新事件
func (that *Signal) Once(callback interface{}) {
	if !(reflect.TypeOf(callback).Kind() == reflect.Func) {
		log.Fatalf("%s is not of type reflect.Func", reflect.TypeOf(callback).Kind())
		return
	}
	listerer := that.getListener()
	handler := func(options ...interface{}) {}
	handler = func(options ...interface{}) {
		listerer.RemoveListener(1, handler)
		argv := make([]reflect.Value, len(options))
		for i, v := range options {
			argv[i] = reflect.ValueOf(v)
		}
		reflect.ValueOf(callback).Call(argv)
	}
	listerer.AddListener(1, handler)
}

// 移除事件
func (that *Signal) RemoveListener(callback interface{}) {
	listerer := that.rec.Load()
	if listerer == nil {
		return
	}
	listerer.(*Delegate).RemoveListener(1, callback)
}

// 事件通知
func (that *Signal) Notify(args ...interface{}) {
	listerer := that.rec.Load()
	if listerer == nil {
		return
	}
	listerer.(*Delegate).Notify(1, args...)
}

// 事件通知
func (that *Signal) Clear() {
	that.rec.Store(nil)
}
