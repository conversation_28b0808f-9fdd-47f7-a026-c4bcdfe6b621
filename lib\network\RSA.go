package network

import (
	"crypto"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"encoding/pem"
	"errors"
	"fmt"
	"os"
	"zone/lib/core"
)

const (
	privateKeyPath = "key/private.pem"
	publicKeyPath  = "key/public.pem"
)

var privateKey *rsa.PrivateKey = nil

func SignSHA256RSA(data []byte) ([]byte, error) {
	privKey, err := LoadPrivateKeyFromFile()
	if err != nil {
		core.LogInfo("SignSHA256RSA", "LoadPrivateKeyFromFile", err.Error())
		return nil, err
	}
	hasher := sha256.New()
	hasher.Write(data)
	digest := hasher.Sum(nil)
	signature, err := rsa.SignPKCS1v15(nil, privKey, crypto.SHA256, digest)
	if err != nil {
		panic(fmt.Sprintf("签名失败: %v", err))
		return nil, err
	}

	return signature, nil
}

func VerifySHA256RSA(data, signature []byte) bool {
	pubKey, err := LoadPublicKeyFromFile()
	if err != nil {
		core.LogInfo("SignSHA256RSA", "LoadPrivateKeyFromFile", err.Error())
		return false
	}
	hashed := sha256.Sum256(data)

	err = rsa.VerifyPSS(
		pubKey,
		crypto.SHA256,
		hashed[:],
		signature,
		&rsa.PSSOptions{SaltLength: rsa.PSSSaltLengthAuto},
	)
	return err == nil
}

func LoadPrivateKeyFromFile() (*rsa.PrivateKey, error) {
	if privateKey != nil {
		return privateKey, nil
	}
	pemData, err := os.ReadFile(privateKeyPath)
	if err != nil {
		return nil, fmt.Errorf("读取文件失败: %w", err)
	}
	block, _ := pem.Decode(pemData)
	if block == nil {
		return nil, errors.New("无效的 PEM 格式")
	}
	// 先尝试 PKCS#8 解析
	key, err := x509.ParsePKCS8PrivateKey(block.Bytes)
	if err == nil {
		switch priv := key.(type) {
		case *rsa.PrivateKey:
			return priv, nil
		default:
			return nil, fmt.Errorf("不支持的私钥类型: %T", priv)
		}
	}
	// 回退到 PKCS#1 解析
	privateKey, err = x509.ParsePKCS1PrivateKey(block.Bytes)
	if err != nil {
		return nil, fmt.Errorf("解析私钥失败: %w", err)
	}
	return privateKey, err
}

// 从 PEM 文件加载公钥
func LoadPublicKeyFromFile() (*rsa.PublicKey, error) {
	pemData, err := os.ReadFile(publicKeyPath)
	if err != nil {
		return nil, err
	}
	block, _ := pem.Decode(pemData)
	if block == nil {
		return nil, errors.New("failed to decode PEM block")
	}
	pub, err := x509.ParsePKIXPublicKey(block.Bytes)
	if err != nil {
		return nil, err
	}
	return pub.(*rsa.PublicKey), nil
}
