// Code generated by pb_exporter.go. DO NOT EDIT.
// Generated at: 2025-06-12 16:35:42

package Response

import (
	"zone/game/Request"
	"zone/game/mods"
	"zone/lib/core"
	"zone/lib/network"
	"zone/pb/Message"
)

func ReloginGameResponse(session *network.Session, msg any) {
	// 将 msg 转换为对应的 C2S 消息结构指针
	relogingame := msg.(*Message.ReloginGameC2S)
	core.LogDebug("ReloginGameC2S:", relogingame)

	var room = mods.GetRoomMgr().GetRoomById(relogingame.RoomID)
	if room == nil {

		Request.ErrorCodeRequest(session, 0, 0, "查找房间失败，请重启直播间")
		return
	} else {
		var oldSession = room.Session
		room.SetSession(session)
		session.RoomID = room.RoomId
		// 处理旧session的清理
		if oldSession != nil {
			core.LogInfo("重连时清理旧session，房间ID:", room.RoomId, "旧会话ID:", oldSession.ID, "新会话ID:", session.ID)

			// 从SessionRoom映射中删除旧session
			mods.GetSessionRoomMgr().Delete(oldSession)

			// 强制关闭旧session连接
			oldSession.ForceCloseConnection("重连替换")

			// 从session管理器中移除
			network.GetSessionMgr().RemoveSession(oldSession)
		}
	}
	core.LogInfo("Protocol_ReloginGame 重连成功", room.RoomId)
	mods.GetSessionRoomMgr().Add(session, room)
	room.ReloginGame()
	// TODO: 实现具体的业务逻辑
	// 从 relogingame 中提取字段值
	// 处理业务逻辑
	// 发送响应消息

	// 示例：创建响应消息
	// ReloginGameRequire(session, /* 参数 */)
}
