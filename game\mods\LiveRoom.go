package mods

import (
	"encoding/json"
	"log"
	"runtime/debug"
	"sync"
	"time"
	"zone/game/Request"
	"zone/game/models"
	"zone/game/platform/bytedance"
	"zone/game/platform/kuaishou"
	"zone/lib/core"
	"zone/lib/network"
	"zone/lib/payload"
	"zone/lib/storage"
	"zone/lib/utils"
	"zone/pb/Message"
)

// GameMode 游戏模式枚举
type GameMode int

const (
	GameModeLive  GameMode = iota // 单人直播游戏模式
	GameModeMulti                 // 多人游戏模式
)

// GameType 定义游戏类型枚举（保持向后兼容）
type GameType int

const (
	GameTypeLive  GameType = iota // 单人直播游戏类型
	GameTypeMulti                 // 多人游戏类型
)

// 房间状态枚举
const (
	RoomStatusDisconnected = iota // 断开连接
	RoomStatusConnecting          // 连接中
	RoomStatusConnected           // 已连接
	RoomStatusSyncing             // 同步中
	RoomStatusActive              // 活跃状态
)

// RoomConnection 房间连接信息
type RoomConnection struct {
	Room       *LiveRoom // 房间实例
	Status     int       // 连接状态
	JoinTime   int64     // 加入时间
	LastSync   int64     // 最后同步时间
	SyncErrors int       // 同步错误计数
}

// ! 游戏状态
const (
	PLAY_STATE_WAIT = iota
	PLAY_STATE_END
	PLAY_STATE_READY
	PLAY_STATE_START
	PLAY_STATE_PAUSE // 暂停状态
)

// ! 队伍定义
const (
	EX_COMMENT_PERSECOND = 20  //! 直播评论
	EX_GIFT_PERSECOND    = 20  //! 直播礼物
	EX_LIKE_PERSECOND    = 100 //! 直播点赞
	EX_FANS_PERSECOND    = 20  //! 直播粉丝团
)

// LiveRoom ! 直播房间
type LiveRoom struct {
	//	房间相关
	Session        *network.Session //! 网络通道
	sessionMutex   sync.RWMutex     //! Session访问锁
	RoomId         int32            //! 本地房间ID
	RoomPlatformId string           //! 抖音的房间ID
	AnchorId       string           //! 主播ID
	AnchorData     *models.AnchorDB //! 主播数据
	Page           int
	Size           int
	CurIndex       int //!
	StopTimer      *time.Timer
	IsStopped      bool // 房间是否已停止（用于停止logicRun）

	// 游戏逻辑管理器（独立引用，不再使用统一接口）
	LiveGame      *LiveGame      //! 单人直播游戏逻辑管理器
	LiveMultiGame *LiveMultiGame //! 多人游戏逻辑管理器
	GameMode      GameMode       //! 当前游戏模式

	//	房间数据 包括玩家数据 和 固定游戏数据
	LiveRoomData *LiveRoomData

	// 混合任务管理器 - 替代原有的SafeMapString任务去重映射
	TaskManager *storage.ProductionHybridTaskManager //! 混合任务去重管理器

	Log string //! 记录
}

func (liveRoom *LiveRoom) Init() {
	// 初始化混合任务管理器
	liveRoom.TaskManager = storage.NewProductionHybridTaskManager()

	// 初始化游戏逻辑管理器（默认为单人游戏模式）
	liveRoom.LiveGame = NewLiveGame(liveRoom)
	liveRoom.LiveMultiGame = nil
	liveRoom.GameMode = GameModeLive
	liveRoom.LiveRoomData = NewLiveRoomData()

	liveRoom.Page = 1
	liveRoom.Size = 100
	liveRoom.CurIndex = 0
	liveRoom.Log = ""
	liveRoom.StopTimer = nil
	liveRoom.IsStopped = false // 初始化停止标志
}

// ==================== 游戏模式管理方法 ====================

// GetCurrentGame 获取当前活跃的游戏实例
func (liveRoom *LiveRoom) GetCurrentGame() interface{} {
	switch liveRoom.GameMode {
	case GameModeLive:
		return liveRoom.LiveGame
	case GameModeMulti:
		return liveRoom.LiveMultiGame
	default:
		return liveRoom.LiveGame // 默认返回单人游戏
	}
}

// SwitchToLiveGame 切换到单人游戏模式
func (liveRoom *LiveRoom) SwitchToLiveGame() {
	if liveRoom.GameMode == GameModeLive {
		return // 已经是单人游戏模式
	}

	// 如果当前是多人游戏，需要先从多人游戏中移除
	if liveRoom.GameMode == GameModeMulti {
		// multiGameMgr := GetMultiGameMgr()
		// if multiGameMgr != nil {
		// 	multiGameMgr.RemoveRoomFromGame(liveRoom.RoomId)
		// }
	}

	// 创建或恢复单人游戏
	if liveRoom.LiveGame == nil {
		liveRoom.LiveGame = NewLiveGame(liveRoom)
	} else if liveRoom.LiveGame.IsPausedState() {
		// 如果LiveGame处于暂停状态，恢复它
		liveRoom.LiveGame.Resume()
		core.LogInfo("LiveGame已恢复", "RoomId:", liveRoom.RoomId, "ResumedState:", liveRoom.LiveGame.GetPlayState())
	} else {
		// 如果LiveGame没有暂停，重置它
		liveRoom.LiveGame.Reset(false)
	}

	liveRoom.LiveMultiGame = nil
	liveRoom.GameMode = GameModeLive

	core.LogInfo("房间切换到单人游戏模式", "RoomId:", liveRoom.RoomId)
}

// SwitchToMultiGame 切换到多人游戏模式
func (liveRoom *LiveRoom) SwitchToMultiGame(multiGame *LiveMultiGame) {
	if liveRoom.GameMode == GameModeMulti && liveRoom.LiveMultiGame == multiGame {
		return // 已经是指定的多人游戏
	}

	// 暂停当前的LiveGame
	if liveRoom.LiveGame != nil {
		liveRoom.LiveGame.Pause()
		core.LogInfo("LiveGame已暂停", "RoomId:", liveRoom.RoomId, "PausedState:", liveRoom.LiveGame.GetPlayState())
	}

	liveRoom.LiveMultiGame = multiGame
	liveRoom.GameMode = GameModeMulti

	core.LogInfo("房间切换到多人游戏模式", "RoomId:", liveRoom.RoomId, "GameID:", multiGame.GetGameID())
}

// GetGameMode 获取当前游戏模式
func (liveRoom *LiveRoom) GetGameMode() GameMode {
	return liveRoom.GameMode
}

// IsLiveGame 检查当前是否为单人游戏模式
func (liveRoom *LiveRoom) IsLiveGame() bool {
	return liveRoom.GameMode == GameModeLive
}

// IsMultiGame 检查当前是否为多人游戏模式
func (liveRoom *LiveRoom) IsMultiGame() bool {
	return liveRoom.GameMode == GameModeMulti
}

// ==================== 游戏方法委托辅助函数 ====================

// callGameMethod 调用当前游戏的方法（无返回值）
func (liveRoom *LiveRoom) callGameMethod(liveGameMethod func(*LiveGame), multiGameMethod func(*LiveMultiGame)) {
	switch liveRoom.GameMode {
	case GameModeLive:
		if liveRoom.LiveGame != nil && liveGameMethod != nil {
			liveGameMethod(liveRoom.LiveGame)
		}
	case GameModeMulti:
		if liveRoom.LiveMultiGame != nil && multiGameMethod != nil {
			multiGameMethod(liveRoom.LiveMultiGame)
		}
	}
}

// callGameMethodWithReturn 调用当前游戏的方法（有返回值）
func (liveRoom *LiveRoom) callGameMethodWithReturn(
	liveGameMethod func(*LiveGame) interface{},
	multiGameMethod func(*LiveMultiGame) interface{},
	defaultValue interface{}) interface{} {

	switch liveRoom.GameMode {
	case GameModeLive:
		if liveRoom.LiveGame != nil && liveGameMethod != nil {
			return liveGameMethod(liveRoom.LiveGame)
		}
	case GameModeMulti:
		if liveRoom.LiveMultiGame != nil && multiGameMethod != nil {
			return multiGameMethod(liveRoom.LiveMultiGame)
		}
	}
	return defaultValue
}

// ==================== 游戏状态访问器方法 ====================

// GetPlayState 获取游戏状态
func (liveRoom *LiveRoom) GetPlayState() int {
	switch liveRoom.GameMode {
	case GameModeLive:
		if liveRoom.LiveGame != nil {
			return liveRoom.LiveGame.GetPlayState()
		}
	case GameModeMulti:
		if liveRoom.LiveMultiGame != nil {
			return liveRoom.LiveMultiGame.GetPlayState()
		}
	}
	return PLAY_STATE_WAIT // 默认状态
}

// SetPlayState 设置游戏状态
func (liveRoom *LiveRoom) SetPlayState(state int) {
	switch liveRoom.GameMode {
	case GameModeLive:
		if liveRoom.LiveGame != nil {
			liveRoom.LiveGame.SetPlayState(state)
		}
	case GameModeMulti:
		if liveRoom.LiveMultiGame != nil {
			liveRoom.LiveMultiGame.SetPlayState(state)
		}
	}
}

// GetScorePool 获取积分池
func (liveRoom *LiveRoom) GetScorePool() int64 {
	switch liveRoom.GameMode {
	case GameModeLive:
		if liveRoom.LiveGame != nil {
			return liveRoom.LiveGame.GetScorePool()
		}
	case GameModeMulti:
		if liveRoom.LiveMultiGame != nil {
			return liveRoom.LiveMultiGame.GetScorePool()
		}
	}
	return 0 // 默认值
}

// SetScorePool 设置积分池
func (liveRoom *LiveRoom) SetScorePool(pool int64) {
	switch liveRoom.GameMode {
	case GameModeLive:
		if liveRoom.LiveGame != nil {
			liveRoom.LiveGame.SetScorePool(pool)
		}
	case GameModeMulti:
		if liveRoom.LiveMultiGame != nil {
			liveRoom.LiveMultiGame.SetScorePool(pool)
		}
	}
}

// GetWinStreakPool 获取连胜池
func (liveRoom *LiveRoom) GetWinStreakPool() int32 {
	switch liveRoom.GameMode {
	case GameModeLive:
		if liveRoom.LiveGame != nil {
			return liveRoom.LiveGame.GetWinStreakPool()
		}
	case GameModeMulti:
		if liveRoom.LiveMultiGame != nil {
			return liveRoom.LiveMultiGame.GetWinStreakPool()
		}
	}
	return 0 // 默认值
}

// SetWinStreakPool 设置连胜池
func (liveRoom *LiveRoom) SetWinStreakPool(pool int32) {
	switch liveRoom.GameMode {
	case GameModeLive:
		if liveRoom.LiveGame != nil {
			liveRoom.LiveGame.SetWinStreakPool(pool)
		}
	case GameModeMulti:
		if liveRoom.LiveMultiGame != nil {
			liveRoom.LiveMultiGame.SetWinStreakPool(pool)
		}
	}
}

// GetLogicFrame 获取逻辑帧
func (liveRoom *LiveRoom) GetLogicFrame() int32 {
	switch liveRoom.GameMode {
	case GameModeLive:
		if liveRoom.LiveGame != nil {
			return liveRoom.LiveGame.GetLogicFrame()
		}
	case GameModeMulti:
		if liveRoom.LiveMultiGame != nil {
			return liveRoom.LiveMultiGame.GetLogicFrame()
		}
	}
	return 0 // 默认值
}

// SetLogicFrame 设置逻辑帧
func (liveRoom *LiveRoom) SetLogicFrame(frame int32) {
	switch liveRoom.GameMode {
	case GameModeLive:
		if liveRoom.LiveGame != nil {
			liveRoom.LiveGame.SetLogicFrame(frame)
		}
	case GameModeMulti:
		if liveRoom.LiveMultiGame != nil {
			liveRoom.LiveMultiGame.SetLogicFrame(frame)
		}
	}
}

// GetStartTime 获取开始时间
func (liveRoom *LiveRoom) GetStartTime() int64 {
	switch liveRoom.GameMode {
	case GameModeLive:
		if liveRoom.LiveGame != nil {
			return liveRoom.LiveGame.GetStartTime()
		}
	case GameModeMulti:
		if liveRoom.LiveMultiGame != nil {
			return liveRoom.LiveMultiGame.GetStartTime()
		}
	}
	return 0 // 默认值
}

// SetStartTime 设置开始时间
func (liveRoom *LiveRoom) SetStartTime(time int64) {
	switch liveRoom.GameMode {
	case GameModeLive:
		if liveRoom.LiveGame != nil {
			liveRoom.LiveGame.SetStartTime(time)
		}
	case GameModeMulti:
		if liveRoom.LiveMultiGame != nil {
			liveRoom.LiveMultiGame.SetStartTime(time)
		}
	}
}

// GetEndTime 获取结束时间
func (liveRoom *LiveRoom) GetEndTime() int64 {
	switch liveRoom.GameMode {
	case GameModeLive:
		if liveRoom.LiveGame != nil {
			return liveRoom.LiveGame.GetEndTime()
		}
	case GameModeMulti:
		if liveRoom.LiveMultiGame != nil {
			return liveRoom.LiveMultiGame.GetEndTime()
		}
	}
	return 0 // 默认值
}

// SetEndTime 设置结束时间
func (liveRoom *LiveRoom) SetEndTime(time int64) {
	switch liveRoom.GameMode {
	case GameModeLive:
		if liveRoom.LiveGame != nil {
			liveRoom.LiveGame.SetEndTime(time)
		}
	case GameModeMulti:
		if liveRoom.LiveMultiGame != nil {
			liveRoom.LiveMultiGame.SetEndTime(time)
		}
	}
}

// GetIsOver 获取游戏是否结束
func (liveRoom *LiveRoom) GetIsOver() bool {
	switch liveRoom.GameMode {
	case GameModeLive:
		if liveRoom.LiveGame != nil {
			return liveRoom.LiveGame.GetIsOver()
		}
	case GameModeMulti:
		if liveRoom.LiveMultiGame != nil {
			return liveRoom.LiveMultiGame.GetIsOver()
		}
	}
	return false // 默认值
}

// SetIsOver 设置游戏是否结束
func (liveRoom *LiveRoom) SetIsOver(over bool) {
	switch liveRoom.GameMode {
	case GameModeLive:
		if liveRoom.LiveGame != nil {
			liveRoom.LiveGame.SetIsOver(over)
		}
	case GameModeMulti:
		if liveRoom.LiveMultiGame != nil {
			liveRoom.LiveMultiGame.SetIsOver(over)
		}
	}
}

// GetRoundID 获取回合ID
func (liveRoom *LiveRoom) GetRoundID() string {
	switch liveRoom.GameMode {
	case GameModeLive:
		if liveRoom.LiveGame != nil {
			return liveRoom.LiveGame.GetRoundID()
		}
	case GameModeMulti:
		if liveRoom.LiveMultiGame != nil {
			return liveRoom.LiveMultiGame.GetRoundID()
		}
	}
	return "" // 默认值
}

// SetRoundID 设置回合ID
func (liveRoom *LiveRoom) SetRoundID(id string) {
	switch liveRoom.GameMode {
	case GameModeLive:
		if liveRoom.LiveGame != nil {
			liveRoom.LiveGame.SetRoundID(id)
		}
	case GameModeMulti:
		if liveRoom.LiveMultiGame != nil {
			liveRoom.LiveMultiGame.SetRoundID(id)
		}
	}
}

func (liveRoom *LiveRoom) SetAnnouncement(content string) {
	if liveRoom.GetPlayState() == PLAY_STATE_WAIT {
		return
	}
	if !liveRoom.IsSerssionNil() {
		Request.AnnouncementRequest(liveRoom.Session, content)
	}
}

func (liveRoom *LiveRoom) ReStoreLivePlayer(month bool) {
	if liveRoom.GetPlayState() == PLAY_STATE_WAIT || liveRoom.GetPlayState() == PLAY_STATE_END {
		return
	}

	// 根据游戏模式获取玩家映射
	var mapPlayer interface{}
	switch liveRoom.GameMode {
	case GameModeLive:
		if liveRoom.LiveGame != nil {
			mapPlayer = liveRoom.LiveRoomData.GetMapPlayer()
		}
	case GameModeMulti:
		if liveRoom.LiveMultiGame != nil {
			mapPlayer = liveRoom.LiveMultiGame.GetMapPlayer()
		}
	}

	if mapPlayer == nil {
		return
	}

	mapPlayer.(*utils.SafeMapInt64).Range(func(key, value interface{}) bool {
		var player = value.(*LivePlayer)
		liveScore := player.GetLiveScore(liveRoom.RoomId)
		if month {
			player.Data.MonthScore += int64(liveScore.GameScore)
			player.Data.TotalCall += int64(liveScore.Call)
		} else {
			player.Data.WeekScore += int64(liveScore.GameScore)
		}
		return true
	})
	if month {
		liveRoom.SetWinStreakPool(0)
		if !liveRoom.IsSerssionNil() {
			Request.SyncWinStreakPoolRequest(liveRoom.Session, liveRoom.GetWinStreakPool())
		}
	}
}

// LogicRun 公开的逻辑运行方法，供外部调用
func (liveRoom *LiveRoom) LogicRun() {
	liveRoom.logicRun()
}

func (liveRoom *LiveRoom) logicRun() {
	defer func() {
		x := recover()
		if x != nil {
			log.Println(x, string(debug.Stack()))
			core.LogError(x, string(debug.Stack()))
		}
	}()

	core.LogDebug("房间logicRun启动", "RoomId:", liveRoom.RoomId, "RoomPlatformId:", liveRoom.RoomPlatformId)

	//! 0.1s 消息循环
	ticker := time.NewTicker(time.Second * 1)
	tenticker := time.NewTicker(time.Second * 10)
	minuteticker := time.NewTicker(time.Minute * 1)
	milliticker := time.NewTicker(time.Millisecond * 100)

	// 确保所有Ticker都被正确停止
	defer func() {
		ticker.Stop()
		tenticker.Stop()
		minuteticker.Stop()
		milliticker.Stop()
		core.LogDebug("房间logicRun停止", "RoomId:", liveRoom.RoomId, "RoomPlatformId:", liveRoom.RoomPlatformId)
	}()

	for {
		// 检查应用关闭或房间停止
		if core.GetZoneApp().IsClosed() || liveRoom.IsStopped {
			if liveRoom.IsStopped {
				core.LogDebug("房间已停止，退出logicRun", "RoomId:", liveRoom.RoomId)
			} else {
				core.LogDebug("应用关闭，退出logicRun", "RoomId:", liveRoom.RoomId)
			}
			break
		}

		select {
		case <-ticker.C:
			liveRoom.onSecondTimer()
		case <-milliticker.C:
			liveRoom.onMilliTimer()
		case <-tenticker.C:
			liveRoom.onTenSecondTimer()
		case <-minuteticker.C:
			liveRoom.onMinuteTimer()
		}
	}
}

func (liveRoom *LiveRoom) SyncLogicFrame() {
	if liveRoom.IsSerssionNil() || liveRoom.GetPlayState() < PLAY_STATE_END {
		return
	}
	Request.SyncFrameRequest(liveRoom.Session, liveRoom.GetLogicFrame())
	liveRoom.SetLogicFrame(liveRoom.GetLogicFrame() + 1)
}

func (liveRoom *LiveRoom) onMinuteTimer() {
	if liveRoom.IsSerssionNil() {
		return
	}

	if liveRoom.GetPlayState() == PLAY_STATE_WAIT || liveRoom.GetPlayState() == PLAY_STATE_END {
		return
	}

	if network.Platform == network.Platform_DouYin {
		// 始终执行直播状态请求（移除测试模式限制）
		GetLiveInfoMgr().AddDYLiveReq(liveRoom.RoomPlatformId, nil, nil, bytedance.LIVE_MSG_TYPE_GIFT, Type_LiveStatus)
		GetLiveInfoMgr().AddDYLiveReq(liveRoom.RoomPlatformId, nil, nil, bytedance.LIVE_MSG_TYPE_COMMENT, Type_LiveStatus)
		GetLiveInfoMgr().AddDYLiveReq(liveRoom.RoomPlatformId, nil, nil, bytedance.LIVE_MSG_TYPE_LIKE, Type_LiveStatus)
		GetLiveInfoMgr().AddDYLiveReq(liveRoom.RoomPlatformId, nil, nil, bytedance.LIVE_MSG_TYPE_FANS, Type_LiveStatus)

	} else {
		DataCallBack := func(data []*kuaishou.GiftQueryItem) {
			if data != nil {
				for _, item := range data {
					payloadList := item.Payload
					for _, pay := range payloadList {

						gifts := pay.Payload
						liveRoom.AddGift(payload.ConvertKSPayloadData(gifts), item.UniqueMessageID)
					}
				}
			}
		}

		GetLiveInfoMgr().CheckKSFailData(liveRoom.RoomPlatformId, DataCallBack)
	}

}

func (liveRoom *LiveRoom) onMilliTimer() {
	if liveRoom.IsSerssionNil() {
		return
	}

	if liveRoom.GetPlayState() == PLAY_STATE_WAIT || liveRoom.GetPlayState() == PLAY_STATE_END {
		return
	}
	liveRoom.SetLogicFrame(liveRoom.GetLogicFrame() + 1)

	liveRoom.callGameMethod(
		func(lg *LiveGame) { lg.ExGift() },
		func(mg *LiveMultiGame) { mg.ExGift() },
	)
	liveRoom.callGameMethod(
		func(lg *LiveGame) { lg.ExFans() },
		func(mg *LiveMultiGame) { mg.ExFans() },
	)
	liveRoom.callGameMethod(
		func(lg *LiveGame) { lg.ExComment() },
		func(mg *LiveMultiGame) { mg.ExComment() },
	)

	liveRoom.SyncLogicFrame()
}

// ! 逻辑循环
func (liveRoom *LiveRoom) onSecondTimer() {
	if liveRoom.IsSerssionNil() {
		return
	}
	if liveRoom.GetPlayState() == PLAY_STATE_WAIT || liveRoom.GetPlayState() == PLAY_STATE_END {
		return
	}

	nowTime := core.TimeServer().Unix()

	if liveRoom.GetEndTime() < nowTime {
		return
	}
	liveRoom.callGameMethod(
		func(lg *LiveGame) { lg.ExLike() },
		func(mg *LiveMultiGame) { mg.ExLike() },
	)
}

func (liveRoom *LiveRoom) onTenSecondTimer() {
	if liveRoom.IsSerssionNil() {
		return
	}
	if network.Platform != network.Platform_DouYin {
		return
	}
	if liveRoom.GetPlayState() == PLAY_STATE_WAIT || liveRoom.GetPlayState() == PLAY_STATE_END {
		return
	}
	failDataCallBack := func(suc bool, failData []*bytedance.JsFailData) {
		if suc {
			liveRoom.Page += 1
			liveRoom.CurIndex = 0
		}
		if failData != nil {
			if !suc {
				liveRoom.CurIndex = len(failData)
				for _, item := range failData {
					pay := item.Payload
					var gifts []*bytedance.Payload
					err1 := json.Unmarshal([]byte(pay), &gifts)
					if err1 == nil {
						liveRoom.callGameMethod(
							func(lg *LiveGame) { lg.AddGift(payload.ConvertDYPayloadData(gifts), "") },
							func(mg *LiveMultiGame) { mg.AddGift(payload.ConvertDYPayloadData(gifts), "") },
						)
					}
				}
			}
		}
	}

	GetLiveInfoMgr().CheckDYFailData(liveRoom.RoomPlatformId, bytedance.LIVE_MSG_TYPE_GIFT, liveRoom.Page, liveRoom.Size, failDataCallBack)
}

func (liveRoom *LiveRoom) Create(createGame *Message.CreateGameC2S) {
	switch liveRoom.GameMode {
	case GameModeLive:
		if liveRoom.LiveGame != nil {
			liveRoom.LiveGame.Create(createGame)
		}
	case GameModeMulti:
		if liveRoom.LiveMultiGame != nil {
			liveRoom.LiveMultiGame.Create(createGame)
		}
	}
}

func (liveRoom *LiveRoom) InitGameOver() {
	switch liveRoom.GameMode {
	case GameModeLive:
		if liveRoom.LiveGame != nil {
			liveRoom.LiveGame.InitGameOver()
		}
	case GameModeMulti:
		if liveRoom.LiveMultiGame != nil {
			liveRoom.LiveMultiGame.InitGameOver()
		}
	}
}

func (liveRoom *LiveRoom) GameStart() {
	switch liveRoom.GameMode {
	case GameModeLive:
		if liveRoom.LiveGame != nil {
			liveRoom.LiveGame.GameStart()
		}
	case GameModeMulti:
		if liveRoom.LiveMultiGame != nil {
			liveRoom.LiveMultiGame.GameStart()
		}
	}
}

func (liveRoom *LiveRoom) ReloginGame() {
	if liveRoom.StopTimer != nil {
		liveRoom.StopTimer.Stop()
		liveRoom.StopTimer = nil
	}

	if !liveRoom.IsSerssionNil() {
		Request.ReloginGameRequest(liveRoom.Session)
	}
}

func (liveRoom *LiveRoom) PlayerKillCount(playerKillCount *Message.PlayerKillCountC2S) {
	switch liveRoom.GameMode {
	case GameModeLive:
		if liveRoom.LiveGame != nil {
			liveRoom.LiveGame.PlayerKillCount(playerKillCount)
		}
	case GameModeMulti:
		if liveRoom.LiveMultiGame != nil {
			liveRoom.LiveMultiGame.PlayerKillCount(playerKillCount)
		}
	}
}

// SafeCloseSession 安全地关闭session并处理room清理
func (liveRoom *LiveRoom) SafeCloseSession(session *network.Session) {
	liveRoom.sessionMutex.Lock()
	defer liveRoom.sessionMutex.Unlock()

	// 检查session是否匹配当前room的session
	if liveRoom.Session != nil && liveRoom.Session.ID == session.ID {
		core.LogInfo("安全关闭session，房间ID:", liveRoom.RoomId, "会话ID:", session.ID)

		// 立即清理session引用
		liveRoom.Session = nil

		// 启动延迟停止定时器，给重连机会
		liveRoom.startStopInternal()
	} else {
		core.LogDebug("session不匹配，跳过清理，房间ID:", liveRoom.RoomId, "会话ID:", session.ID)
	}
}

func (liveRoom *LiveRoom) StartStop() {
	liveRoom.sessionMutex.Lock()
	defer liveRoom.sessionMutex.Unlock()
	liveRoom.startStopInternal()
}

// startStopInternal 内部停止方法，调用前需要获取锁
func (liveRoom *LiveRoom) startStopInternal() {
	// 如果session已经为nil，说明已经被SafeCloseSession处理过了
	if liveRoom.Session != nil {
		liveRoom.Session = nil
	}

	// 取消之前的停止定时器（如果存在）
	if liveRoom.StopTimer != nil {
		liveRoom.StopTimer.Stop()
		liveRoom.StopTimer = nil
	}

	liveRoom.StopTimer = time.NewTimer(time.Second * time.Duration(120))
	go func() {
		<-liveRoom.StopTimer.C
		liveRoom.Stop()
	}()
}

// Stop ! 直播结束
func (liveRoom *LiveRoom) Stop() {
	// 设置停止标志，让logicRun退出
	liveRoom.IsStopped = true
	core.LogDebug("设置房间停止标志", "RoomId:", liveRoom.RoomId, "RoomPlatformId:", liveRoom.RoomPlatformId)

	liveRoom.AnchorData.CloseTime = core.TimeServer().Unix()

	// 清理混合任务管理器
	if liveRoom.TaskManager != nil {
		liveRoom.TaskManager.ClearRoomTasks(liveRoom.RoomId)
		liveRoom.TaskManager.Close()
		core.LogDebug("混合任务管理器已清理", "RoomId:", liveRoom.RoomId)
	}
	core.LogDebug("关闭房间", liveRoom.RoomPlatformId, "主播名称：", liveRoom.AnchorData.Name, "房间号：", liveRoom.RoomId)

	liveRoom.Reset(true)
	//! 删除直播房间
	GetRoomMgr().GetRoomMap().(*utils.SafeMapInt32).Delete(liveRoom.RoomId)
	GetRoomMgr().GetRoomPlatformMap().(*utils.SafeMapString).Delete(liveRoom.RoomPlatformId)
	//GetLiveInfoMgr().AddLiveReq(liveRoom.RoomPlatformId, myCallback, liveRoom.IsTest, LiveReqStop)

	// GetLiveInfoMgr().AddLiveReq(liveRoom.RoomPlatformId, nil, network.LIVE_MSG_TYPE_GIFT, liveRoom.IsTest, Type_LiveStop)
	// GetLiveInfoMgr().AddLiveReq(liveRoom.RoomPlatformId, nil, network.LIVE_MSG_TYPE_COMMENT, liveRoom.IsTest, Type_LiveStop)
	// GetLiveInfoMgr().AddLiveReq(liveRoom.RoomPlatformId, nil, network.LIVE_MSG_TYPE_LIKE, liveRoom.IsTest, Type_LiveStop)
}

// GameOver ! 直播房间停止
func (liveRoom *LiveRoom) GameOver(gameOverC2S *Message.GameOverC2S) {
	switch liveRoom.GameMode {
	case GameModeLive:
		if liveRoom.LiveGame != nil {
			liveRoom.LiveGame.GameOver(gameOverC2S)
		}
	case GameModeMulti:
		if liveRoom.LiveMultiGame != nil {
			liveRoom.LiveMultiGame.GameOver(gameOverC2S)
		}
	}
}

// Reset ! 重置玩法
func (liveRoom *LiveRoom) Reset(close bool) {
	if close {
		// 如果是关闭重置，设置停止标志
		liveRoom.IsStopped = true
		core.LogDebug("Reset设置房间停止标志", "RoomId:", liveRoom.RoomId, "close:", close)
	} else {
		// 如果不是关闭重置，确保停止标志为false（用于房间重用）
		liveRoom.IsStopped = false
		core.LogDebug("Reset重置房间状态", "RoomId:", liveRoom.RoomId, "close:", close)
	}

	// 委托给Game处理游戏逻辑重置
	switch liveRoom.GameMode {
	case GameModeLive:
		if liveRoom.LiveGame != nil {
			liveRoom.LiveGame.Reset(close)
		}
	case GameModeMulti:
		if liveRoom.LiveMultiGame != nil {
			liveRoom.LiveMultiGame.Reset(close)
		}
	}

	// 重新初始化混合任务管理器
	if liveRoom.TaskManager != nil {
		liveRoom.TaskManager.Close()
	}
	liveRoom.TaskManager = storage.NewProductionHybridTaskManager()

	liveRoom.Page = 1
	liveRoom.Size = 100
	liveRoom.CurIndex = 0
}

func (liveRoom *LiveRoom) AddPlayer(p *LivePlayer) bool {
	liveRoom.LiveRoomData.AddPlayer(p)
	return true
}

func (liveRoom *LiveRoom) IsSerssionNil() bool {
	if utils.IsNil(liveRoom) {
		return true
	}

	liveRoom.sessionMutex.RLock()
	defer liveRoom.sessionMutex.RUnlock()

	result := utils.IsNil(liveRoom.Session)
	return result
}

// SetSession 安全地设置session
func (liveRoom *LiveRoom) SetSession(session *network.Session) {
	liveRoom.sessionMutex.Lock()
	defer liveRoom.sessionMutex.Unlock()

	// 如果有旧的session，记录日志
	if liveRoom.Session != nil && session != nil && liveRoom.Session.ID != session.ID {
		core.LogInfo("替换房间session，房间ID:", liveRoom.RoomId, "旧会话ID:", liveRoom.Session.ID, "新会话ID:", session.ID)
	}

	liveRoom.Session = session

	// 如果设置了新的session，取消停止定时器
	if session != nil && liveRoom.StopTimer != nil {
		liveRoom.StopTimer.Stop()
		liveRoom.StopTimer = nil
		core.LogInfo("新session连接，取消房间停止定时器，房间ID:", liveRoom.RoomId, "会话ID:", session.ID)
	}
}

// GetSession 安全地获取session
func (liveRoom *LiveRoom) GetSession() *network.Session {
	liveRoom.sessionMutex.RLock()
	defer liveRoom.sessionMutex.RUnlock()
	return liveRoom.Session
}

func (liveRoom *LiveRoom) HasPlayer(uid int64) bool {
	return liveRoom.LiveRoomData.HasPlayer(uid)
}

func (liveRoom *LiveRoom) GetRoomPlayer(liveId int64) *LivePlayer {
	result := liveRoom.LiveRoomData.GetPlayer(liveId)
	if result != nil {
		return result
	}
	return nil
}

func (liveRoom *LiveRoom) AddComment(comment []byte) {
	liveRoom.callGameMethod(
		func(lg *LiveGame) { lg.AddComment(comment) },
		func(mg *LiveMultiGame) { mg.AddComment(comment) },
	)
}

func (liveRoom *LiveRoom) AddGift(comment []*payload.PayloadData, msgId string) {
	liveRoom.callGameMethod(
		func(lg *LiveGame) { lg.AddGift(comment, msgId) },
		func(mg *LiveMultiGame) { mg.AddGift(comment, msgId) },
	)
}

func (liveRoom *LiveRoom) AddFans(comment []*payload.PayloadData, msgId string) {
	liveRoom.callGameMethod(
		func(lg *LiveGame) { lg.AddFans(comment, msgId) },
		func(mg *LiveMultiGame) { mg.AddFans(comment, msgId) },
	)
}

func (liveRoom *LiveRoom) AddLike(comment []byte) {
	liveRoom.callGameMethod(
		func(lg *LiveGame) { lg.AddLike(comment) },
		func(mg *LiveMultiGame) { mg.AddLike(comment) },
	)
}

func (liveRoom *LiveRoom) ExComment() {
	// 委托给Game处理，但保留DoComment的调用
	liveRoom.callGameMethod(
		func(lg *LiveGame) { lg.ExComment() },
		func(mg *LiveMultiGame) { mg.ExComment() },
	)
}

func (liveRoom *LiveRoom) ExGift() {
	// 委托给Game处理，但保留DoGift的调用
	liveRoom.callGameMethod(
		func(lg *LiveGame) { lg.ExGift() },
		func(mg *LiveMultiGame) { mg.ExGift() },
	)
}

func (liveRoom *LiveRoom) ExFans() {
	// 委托给Game处理，但保留DoFans的调用
	liveRoom.callGameMethod(
		func(lg *LiveGame) { lg.ExFans() },
		func(mg *LiveMultiGame) { mg.ExFans() },
	)
}

func (liveRoom *LiveRoom) DoneOp(op *Message.PlayerOperateC2S) {
	// 始终执行操作报告（移除测试模式限制）
	if op.IsSuccess == 1 && op.Type == 1 && op.MsgId != "" {
		GetLiveInfoMgr().Reporting(liveRoom.RoomPlatformId, 2, op.MsgId, bytedance.LIVE_MSG_TYPE_GIFT)
	}
	if op.Type == 1 && op.MsgId != "" {
		GetGiftRecordMgr().DoRecord(op.MsgId)
	}
}

func (liveRoom *LiveRoom) ExLike() {
	// 委托给Game处理，但保留DoLike的调用
	liveRoom.callGameMethod(
		func(lg *LiveGame) { lg.ExLike() },
		func(mg *LiveMultiGame) { mg.ExLike() },
	)
}

func (liveRoom *LiveRoom) DoLike(payload *payload.PayloadData) {
	// 始终执行任务去重检查和报告（移除测试模式限制）
	if !liveRoom.TaskManager.CheckAndMarkLikeTask(liveRoom.RoomId, payload.MsgId) {
		core.LogError("重复消息，跳过处理:", payload.MsgId)
		return // 重复消息，跳过处理
	}
	GetLiveInfoMgr().Reporting(liveRoom.RoomPlatformId, 1, payload.MsgId, bytedance.LIVE_MSG_TYPE_LIKE)
	// core.LogInfo("点赞数量:", payload.LikeNum, "玩家ID:", payload.OpenId)
	livePlayer := GetLiveMgr().GetLivePlayerByOpenId(payload.OpenId)
	if livePlayer == nil {
		//! 还没有加入游戏，加入人少的一方，一样，则加入到
		liveData := GetLiveMgr().GetLiveDataByOpenId(payload.OpenId)
		if liveData == nil {
			liveData = GetLiveMgr().NewLiveData(payload.OpenId, payload.Nickname)
			liveData.Icon = payload.AvatarUrl
		} else {
			liveData.Icon = payload.AvatarUrl
			liveData.PlayerName = payload.Nickname
		}

		livePlayer = GetLiveMgr().GetLivePlayer(liveData.PlayerId)
		if livePlayer == nil {
			livePlayer = GetLiveMgr().NewLivePlayer(liveData.OpenId, liveData.PlayerName)
		}
	}

	if !liveRoom.HasPlayer(livePlayer.LiveId) {
		GetLiveInfoMgr().Reporting(liveRoom.RoomPlatformId, 2, payload.MsgId, bytedance.LIVE_MSG_TYPE_LIKE)
		return
	}

	livePlayer = liveRoom.GetRoomPlayer(livePlayer.LiveId)
	if livePlayer == nil {
		// core.LogError("角色不存在：", livePlayer.LiveId)
		GetLiveInfoMgr().Reporting(liveRoom.RoomPlatformId, 2, payload.MsgId, bytedance.LIVE_MSG_TYPE_LIKE)
		return
	}

	livePlayerInfo := livePlayer.GetPlayerLiveInfo(liveRoom.RoomId)
	if livePlayerInfo == nil {

		// core.LogError("角色信息不存在：", livePlayer.LiveId, "RoomID:", liveRoom.RoomId)
		GetLiveInfoMgr().Reporting(liveRoom.RoomPlatformId, 2, payload.MsgId, bytedance.LIVE_MSG_TYPE_LIKE)
		return
	}
	if livePlayerInfo.IsDead {

		// core.LogInfo("角色已经死亡：", livePlayer.LiveId, "RoomID:", liveRoom.RoomId)
		GetLiveInfoMgr().Reporting(liveRoom.RoomPlatformId, 2, payload.MsgId, bytedance.LIVE_MSG_TYPE_LIKE)
		return
	}

	livePlayer.AddGiftById(liveRoom.RoomId, 12, payload.LikeNum)

	GetLiveInfoMgr().Reporting(liveRoom.RoomPlatformId, 2, payload.MsgId, bytedance.LIVE_MSG_TYPE_LIKE)
}

// RestartRoom 重新启动房间（用于房间重用）
func (liveRoom *LiveRoom) RestartRoom() {
	if liveRoom.StopTimer != nil {
		liveRoom.StopTimer.Stop()
		liveRoom.StopTimer = nil
	}
	if liveRoom.IsStopped {
		liveRoom.IsStopped = false
		core.LogDebug("重新启动房间", "RoomId:", liveRoom.RoomId, "RoomPlatformId:", liveRoom.RoomPlatformId)
		// 重新启动logicRun
		go liveRoom.logicRun()
	}
}
