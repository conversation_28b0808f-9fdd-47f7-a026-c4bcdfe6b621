// Code generated by pb_exporter.go. DO NOT EDIT.
// Generated at: 2025-06-12 16:35:42

package Response

import (
	"fmt"
	"zone/game/Request"
	"zone/game/mods"
	"zone/game/platform/bytedance"
	"zone/game/platform/kuaishou"
	"zone/lib/core"
	"zone/lib/network"
	"zone/pb/Message"
)

func CreateRoomResponse(session *network.Session, msg any) {
	// 将 msg 转换为对应的 C2S 消息结构指针
	createroom := msg.(*Message.CreateRoomC2S)
	// core.LogDebug("CreateRoomC2S:", createroom)

	if createroom.Token == "" {
		Request.ErrorCodeRequest(session, 0, 0, "正式包，直播伴侣Token错误，无法创建直播间,请重启游戏。")
		network.GetSessionMgr().RemoveSession(session)
		return
	}

	if network.Platform == network.Platform_DouYin {
		callBack := func(result any) {
			if result == nil {
				Request.ErrorCodeRequest(session, 0, 0, "直播伴侣Token错误，无法创建直播间")
				network.GetSessionMgr().RemoveSession(session)
				return
			}
			roomInfo, ok := result.(*bytedance.JsLiveInfoResInfo)
			if ok && roomInfo != nil {
				anchorData := mods.GetAnchorMgr().AnchorOnLine(roomInfo.AnchorOpenId, roomInfo.AvatarUrl, roomInfo.NickName)
				roomId := roomInfo.RoomId
				if roomId > 0 {
					//! 获取成功
					liveRoom := mods.GetRoomMgr().CreateRoomByPlatformId(fmt.Sprintf("%d", roomId))
					// if err != nil {
					// 	Request.ErrorCodeRequest(session, 0, 0, "直播间已经存在，无法创建。。请关闭直播伴侣并重试。")
					// 	network.GetSessionMgr().RemoveSession(session)
					// 	return
					// }
					core.LogDebug("创建房间:", liveRoom.RoomId)
					liveRoom.AnchorId = roomInfo.AnchorOpenId
					liveRoom.AnchorData = anchorData
					liveRoom.SetSession(session)
					session.RoomID = liveRoom.RoomId
					Request.CreateRoomRequest(session, liveRoom.RoomId)
					mods.GetSessionRoomMgr().Add(session, liveRoom)
					return
				}
			}
		}

		failcallBack := func(result any) {
			bytedance.GetAccessToken()
			// 始终使用false，移除测试模式限制
			mods.GetLiveInfoMgr().AddDYLiveReq(createroom.Token, callBack, nil, "", mods.Type_LiveInfoReq)
			return
		}
		mods.GetLiveInfoMgr().AddDYLiveReq(createroom.Token, callBack, failcallBack, "", mods.Type_LiveInfoReq)
	} else {
		// kuaishou.KS_Round(createroom.Token, "start", &kuaishou.RoundData{
		// 	RoundId:   "1",
		// 	RoundType: "singleNotGroup",
		// })

		callBack := func(result any) {
			if result == nil {
				Request.ErrorCodeRequest(session, 0, 0, "直播伴侣Token错误，无法创建直播间")
				network.GetSessionMgr().RemoveSession(session)
				return
			}
			roomInfo := result.(*kuaishou.DataInfo)
			if roomInfo != nil {
				anchorData := mods.GetAnchorMgr().AnchorOnLine(roomInfo.AnchorOpenId, roomInfo.AvatarUrl, roomInfo.NickName)
				roomId := roomInfo.RoomCode
				if roomId != "" {
					//! 获取成功
					liveRoom := mods.GetRoomMgr().CreateRoomByPlatformId(roomId)
					core.LogDebug("创建房间:", liveRoom.RoomId)
					liveRoom.AnchorId = roomInfo.AnchorOpenId
					liveRoom.AnchorData = anchorData
					liveRoom.SetSession(session)
					session.RoomID = liveRoom.RoomId
					Request.CreateRoomRequest(session, liveRoom.RoomId)
					mods.GetSessionRoomMgr().Add(session, liveRoom)
					return
				}
			}
		}
		var recall = func() {}

		failcallBack := func(result any) {
			err := result.(error)
			core.LogError(err)
			recall()
			return
		}
		recall = func() {
			// 始终使用false，移除测试模式限制
			mods.GetLiveInfoMgr().AddKSLiveReq(createroom.Token, callBack, failcallBack, mods.Type_Bind, "start", nil)
		}
		mods.GetLiveInfoMgr().AddKSLiveReq(createroom.Token, callBack, failcallBack, mods.Type_Bind, "start", nil)
	}
	// 始终使用false，移除测试模式限制
}
