package utils

import (
	"fmt"
	"sync"
)

type SafeSlice[T any] struct {
	mu    *sync.RWMutex
	items []T
}

func NewSafeSlice[T any]() *SafeSlice[T] {
	return &SafeSlice[T]{
		mu:    new(sync.RWMutex),
		items: make([]T, 0),
	}
}

func (ss *SafeSlice[T]) Append(item T) {
	ss.mu.Lock()
	defer ss.mu.Unlock()
	ss.items = append(ss.items, item)
}

func (ss *SafeSlice[T]) Len() int {
	ss.mu.RLock()
	defer ss.mu.RUnlock()
	return len(ss.items)
}

func (ss *SafeSlice[T]) Get(index int) (T, error) {
	ss.mu.RLock()
	defer ss.mu.RUnlock()
	if index < 0 || index >= len(ss.items) {
		var zero T
		return zero, fmt.Errorf("invalid index")
	}
	val := ss.items[index]
	return val, nil
}

func (ss *SafeSlice[T]) RemoveAt(index int) (T, error) {
	ss.mu.Lock()
	defer ss.mu.Unlock()

	if index < 0 || index >= len(ss.items) {
		var zero T
		return zero, fmt.Errorf("invalid index")
	}

	val := ss.items[index]
	ss.items = append(ss.items[:index], ss.items[index+1:]...)
	return val, nil
}

func (ss *SafeSlice[T]) InsertAt(index int, item T) error {
	ss.mu.Lock()
	defer ss.mu.Unlock()
	if index < 0 || index > len(ss.items) {
		return fmt.Errorf("index %d out of range [0-%d]",
			index, len(ss.items))
	}
	ss.items = append(ss.items[:index],
		append([]T{item}, ss.items[index:]...)...)
	return nil
}

func (ss *SafeSlice[T]) AppendAll(items ...T) {
	ss.mu.Lock()
	defer ss.mu.Unlock()
	ss.items = append(ss.items, items...)
}

func (ss *SafeSlice[T]) AppendIfAbsent(item T, existsFunc func(T) bool) bool {
	ss.mu.Lock()
	defer ss.mu.Unlock()

	for _, v := range ss.items {
		if existsFunc(v) {
			return false
		}
	}
	ss.items = append(ss.items, item)
	return true
}

func (ss *SafeSlice[T]) BatchAppend(items []T) {
	ss.mu.Lock()
	defer ss.mu.Unlock()
	ss.items = append(ss.items, items...)
}

func (ss *SafeSlice[T]) RemoveFirst(matchFunc func(T) bool) (T, error) {
	ss.mu.Lock()
	defer ss.mu.Unlock()
	var zero T
	for i, item := range ss.items {
		if matchFunc(item) {
			removed := ss.items[i]
			ss.items = append(ss.items[:i], ss.items[i+1:]...)
			return removed, nil
		}
	}
	return zero, fmt.Errorf("element not found")
}

func (ss *SafeSlice[T]) FastRemoveFirst(matchFunc func(T) bool) (T, error) {
	ss.mu.Lock()
	defer ss.mu.Unlock()
	var zero T
	for i, item := range ss.items {
		if matchFunc(item) {
			removed := ss.items[i]
			// 用最后一个元素替换并缩短切片
			last := len(ss.items) - 1
			ss.items[i] = ss.items[last]
			ss.items = ss.items[:last]
			return removed, nil
		}
	}
	return zero, fmt.Errorf("not found")
}

func (ss *SafeSlice[T]) Exists(matchFunc func(T) bool) bool {
	ss.mu.RLock()
	defer ss.mu.RUnlock()

	for _, item := range ss.items {
		if matchFunc(item) {
			return true
		}
	}
	return false
}

func (ss *SafeSlice[T]) FindFirst(matchFunc func(T) bool) (int, T, error) {
	ss.mu.RLock() // 读锁保证并发安全
	defer ss.mu.RUnlock()
	var zero T
	if len(ss.items) == 0 {
		return -1, zero, fmt.Errorf("element not found")
	}
	for index, item := range ss.items {
		if matchFunc(item) {
			return index, item, nil // 返回第一个匹配项的索引和值
		}
	}
	return -1, zero, fmt.Errorf("element not found")
}
