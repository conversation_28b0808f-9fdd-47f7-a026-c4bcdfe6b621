package storage

import (
	"fmt"
	"path/filepath"
	"sync"
	"time"
	"zone/lib/core"
	"zone/lib/db"

	"github.com/gomodule/redigo/redis"
)

// ProductionHybridTaskManager 生产级混合任务管理器
// 集成现有的Redis连接池，支持故障降级和性能优化
type ProductionHybridTaskManager struct {
	// 内存缓存层 - 用于高频访问
	memoryCache *sync.Map

	// Redis连接管理
	redisMgr     *db.RedisMgr
	redisEnabled bool

	// 配置参数
	keyPrefix      string
	defaultTTL     time.Duration
	maxMemoryItems int
	asyncSync      bool

	// 统计信息
	stats      *TaskManagerStats
	statsMutex sync.RWMutex

	// 性能优化
	batchSize     int
	batchInterval time.Duration
	batchChan     chan *batchOperation
	stopChan      chan struct{}
	wg            sync.WaitGroup

	// 关闭状态管理
	closed     bool
	closeMutex sync.Mutex
}

// 使用已有的TaskManagerStats，添加批量操作统计
// 注意：需要在hybrid_manager.go中的TaskManagerStats添加BatchOperations字段

// batchOperation 批量操作结构
type batchOperation struct {
	key   string
	value interface{}
	ttl   time.Duration
}

// NewProductionHybridTaskManager 创建生产级混合任务管理器
func NewProductionHybridTaskManager() *ProductionHybridTaskManager {
	manager := &ProductionHybridTaskManager{
		memoryCache:    &sync.Map{},
		redisMgr:       db.GetRedisMgr(),
		redisEnabled:   db.GetRedisMgr().IsInit(),
		keyPrefix:      "live_task",
		defaultTTL:     time.Hour * 2,
		maxMemoryItems: 10000,
		asyncSync:      true,
		stats:          &TaskManagerStats{},
		batchSize:      100,
		batchInterval:  time.Second * 5,
		batchChan:      make(chan *batchOperation, 1000),
		stopChan:       make(chan struct{}),
	}

	// 启动批量处理协程
	if manager.redisEnabled && manager.asyncSync {
		manager.startBatchProcessor()
	}

	core.LogInfo("生产级混合任务管理器已创建，Redis启用:", manager.redisEnabled)
	return manager
}

// CheckAndMarkGiftTask 检查并标记礼物任务
func (h *ProductionHybridTaskManager) CheckAndMarkGiftTask(roomId int32, msgId string) bool {
	return h.checkAndMarkTask("gift", roomId, msgId)
}

// CheckAndMarkCommentTask 检查并标记评论任务
func (h *ProductionHybridTaskManager) CheckAndMarkCommentTask(roomId int32, msgId string) bool {
	if msgId == "" {
		return true
	}
	return h.checkAndMarkTask("comment", roomId, msgId)
}

// CheckAndMarkLikeTask 检查并标记点赞任务
func (h *ProductionHybridTaskManager) CheckAndMarkLikeTask(roomId int32, msgId string) bool {
	if msgId == "" {
		return true
	}
	return h.checkAndMarkTask("like", roomId, msgId)
}

// CheckAndMarkFansTask 检查并标记粉丝团任务
func (h *ProductionHybridTaskManager) CheckAndMarkFansTask(roomId int32, msgId string) bool {
	return h.checkAndMarkTask("fans", roomId, msgId)
}

// checkAndMarkTask 通用的检查并标记任务方法
func (h *ProductionHybridTaskManager) checkAndMarkTask(taskType string, roomId int32, msgId string) bool {
	h.updateStats(func(s *TaskManagerStats) { s.TotalChecks++ })

	key := fmt.Sprintf("%s:%s:%d:%s", h.keyPrefix, taskType, roomId, msgId)

	// 1. 首先检查内存缓存（最快）
	if _, exists := h.memoryCache.Load(key); exists {
		h.updateStats(func(s *TaskManagerStats) { s.MemoryHits++ })
		return false // 任务已存在
	}

	h.updateStats(func(s *TaskManagerStats) { s.MemoryMisses++ })

	// 2. 标记为已处理（使用空结构体节省内存）
	h.memoryCache.Store(key, struct{}{})

	// 3. 异步同步到Redis（不阻塞主流程）
	if h.redisEnabled && h.asyncSync && !h.isClosed() {
		select {
		case h.batchChan <- &batchOperation{
			key:   key,
			value: 1,
			ttl:   h.defaultTTL,
		}:
		default:
			// 批量通道满了，直接同步处理
			go h.syncToRedis(key, 1, h.defaultTTL)
		}
	} else if h.redisEnabled && !h.isClosed() {
		// 同步模式下直接处理
		go h.syncToRedis(key, 1, h.defaultTTL)
	}

	// 4. 检查内存缓存大小，必要时清理
	h.checkAndCleanMemory()

	return true // 新任务
}

// syncToRedis 同步数据到Redis
func (h *ProductionHybridTaskManager) syncToRedis(key string, value interface{}, ttl time.Duration) {
	if !h.redisEnabled {
		return
	}

	conn := h.redisMgr.GetRedisConn()
	defer conn.Close()

	// 使用SETEX命令设置键值和过期时间
	_, err := conn.Do("SETEX", key, int(ttl.Seconds()), value)
	if err != nil {
		h.updateStats(func(s *TaskManagerStats) { s.RedisErrors++ })
		core.LogError("同步任务到Redis失败:", err, "key:", key)
	} else {
		h.updateStats(func(s *TaskManagerStats) { s.SyncOperations++ })
	}
}

// startBatchProcessor 启动批量处理器
func (h *ProductionHybridTaskManager) startBatchProcessor() {
	h.wg.Add(1)
	go func() {
		defer h.wg.Done()

		batch := make([]*batchOperation, 0, h.batchSize)
		ticker := time.NewTicker(h.batchInterval)
		defer ticker.Stop()

		for {
			select {
			case <-h.stopChan:
				// 处理剩余的批量操作
				if len(batch) > 0 {
					h.processBatch(batch)
				}
				// 处理channel中剩余的操作
				for {
					select {
					case op := <-h.batchChan:
						batch = append(batch, op)
						if len(batch) >= h.batchSize {
							h.processBatch(batch)
							batch = batch[:0]
						}
					default:
						// 没有更多操作，处理最后一批
						if len(batch) > 0 {
							h.processBatch(batch)
						}
						return
					}
				}

			case op, ok := <-h.batchChan:
				if !ok {
					// channel已关闭，处理剩余批量操作后退出
					if len(batch) > 0 {
						h.processBatch(batch)
					}
					return
				}
				batch = append(batch, op)
				if len(batch) >= h.batchSize {
					h.processBatch(batch)
					batch = batch[:0] // 重置切片
				}

			case <-ticker.C:
				if len(batch) > 0 {
					h.processBatch(batch)
					batch = batch[:0] // 重置切片
				}
			}
		}
	}()
}

// processBatch 处理批量操作
func (h *ProductionHybridTaskManager) processBatch(batch []*batchOperation) {
	if !h.redisEnabled || len(batch) == 0 {
		return
	}

	conn := h.redisMgr.GetRedisConn()
	defer conn.Close()

	// 使用Redis管道提高性能
	for _, op := range batch {
		conn.Send("SETEX", op.key, int(op.ttl.Seconds()), op.value)
	}

	err := conn.Flush()
	if err != nil {
		h.updateStats(func(s *TaskManagerStats) { s.RedisErrors++ })
		core.LogError("批量同步到Redis失败:", err)
		return
	}

	// 接收所有响应
	for range batch {
		_, err := conn.Receive()
		if err != nil {
			h.updateStats(func(s *TaskManagerStats) { s.RedisErrors++ })
		}
	}

	h.updateStats(func(s *TaskManagerStats) {
		s.BatchOperations++
		s.SyncOperations += int64(len(batch))
	})

	core.LogDebug("批量同步完成，操作数:", len(batch))
}

// checkAndCleanMemory 检查并清理内存缓存
func (h *ProductionHybridTaskManager) checkAndCleanMemory() {
	// 简单的计数检查（避免频繁遍历）
	count := 0
	h.memoryCache.Range(func(key, value interface{}) bool {
		count++
		return count < h.maxMemoryItems
	})

	if count >= h.maxMemoryItems {
		go h.cleanOldMemoryItems()
	}
}

// cleanOldMemoryItems 清理旧的内存项（简化版本）
func (h *ProductionHybridTaskManager) cleanOldMemoryItems() {
	// 清理策略：随机删除一部分项目
	deleteCount := h.maxMemoryItems / 4 // 删除25%
	deleted := 0

	h.memoryCache.Range(func(key, value interface{}) bool {
		if deleted >= deleteCount {
			return false
		}
		h.memoryCache.Delete(key)
		deleted++
		return true
	})

	if deleted > 0 {
		core.LogDebug("清理内存缓存项:", deleted)
	}
}

// ClearRoomTasks 清理房间任务
func (h *ProductionHybridTaskManager) ClearRoomTasks(roomId int32) error {
	pattern := fmt.Sprintf("%s:*:%d:*", h.keyPrefix, roomId)

	// 清理内存缓存
	keysToDelete := make([]interface{}, 0)
	h.memoryCache.Range(func(key, value interface{}) bool {
		if keyStr, ok := key.(string); ok {
			if matched, _ := filepath.Match(pattern, keyStr); matched {
				keysToDelete = append(keysToDelete, key)
			}
		}
		return true
	})

	for _, key := range keysToDelete {
		h.memoryCache.Delete(key)
	}

	// 清理Redis（如果启用）
	if h.redisEnabled {
		go func() {
			conn := h.redisMgr.GetRedisConn()
			defer conn.Close()

			// 使用SCAN命令查找匹配的键
			cursor := 0
			for {
				reply, err := redis.Values(conn.Do("SCAN", cursor, "MATCH", pattern, "COUNT", 100))
				if err != nil {
					core.LogError("扫描Redis键失败:", err)
					break
				}

				var newCursor int
				var keys []string
				if _, err := redis.Scan(reply, &newCursor, &keys); err != nil {
					core.LogError("解析SCAN结果失败:", err)
					break
				}

				// 删除找到的键
				if len(keys) > 0 {
					for _, key := range keys {
						conn.Send("DEL", key)
					}
					conn.Flush()
					for range keys {
						conn.Receive()
					}
				}

				if newCursor == 0 {
					break
				}
				cursor = newCursor
			}
		}()
	}

	core.LogDebug("清理房间任务完成，房间ID:", roomId, "内存清理:", len(keysToDelete))
	return nil
}

// GetStats 获取统计信息
func (h *ProductionHybridTaskManager) GetStats() TaskManagerStats {
	h.statsMutex.RLock()
	defer h.statsMutex.RUnlock()
	return *h.stats
}

// updateStats 更新统计信息
func (h *ProductionHybridTaskManager) updateStats(updater func(*TaskManagerStats)) {
	h.statsMutex.Lock()
	defer h.statsMutex.Unlock()
	updater(h.stats)
}

// isClosed 检查管理器是否已关闭
func (h *ProductionHybridTaskManager) isClosed() bool {
	h.closeMutex.Lock()
	defer h.closeMutex.Unlock()
	return h.closed
}

// Close 关闭管理器
func (h *ProductionHybridTaskManager) Close() error {
	h.closeMutex.Lock()
	defer h.closeMutex.Unlock()

	// 防止重复关闭
	if h.closed {
		core.LogDebug("生产级混合任务管理器已经关闭，跳过重复关闭")
		return nil
	}

	// 标记为已关闭
	h.closed = true

	// 安全关闭stopChan（只关闭一次）
	defer func() {
		if r := recover(); r != nil {
			core.LogDebug("关闭stopChan时发生panic（可能已经关闭）:", r)
		}
	}()
	close(h.stopChan)

	// 等待所有goroutine完成
	h.wg.Wait()

	// 安全关闭batchChan（只关闭一次）
	defer func() {
		if r := recover(); r != nil {
			core.LogDebug("关闭batchChan时发生panic（可能已经关闭）:", r)
		}
	}()
	close(h.batchChan)

	core.LogInfo("生产级混合任务管理器已关闭")
	return nil
}

// HealthCheck 健康检查
func (h *ProductionHybridTaskManager) HealthCheck() error {
	if !h.redisEnabled {
		return nil
	}

	_, err := h.redisMgr.Ping()
	if err != nil {
		return fmt.Errorf("Redis连接检查失败: %v", err)
	}
	return nil
}

// SetConfig 设置配置参数
func (h *ProductionHybridTaskManager) SetConfig(maxMemoryItems int, batchSize int, batchInterval time.Duration) {
	h.maxMemoryItems = maxMemoryItems
	h.batchSize = batchSize
	h.batchInterval = batchInterval

	core.LogDebug("混合任务管理器配置已更新")
}
