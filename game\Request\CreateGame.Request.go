// Code generated by pb_exporter.go. DO NOT EDIT.
package Request

import (
	"zone/lib/network"
	"zone/pb/Message"
)

func CreateGameRequest(session *network.Session, roomid int32, scorepool int64, seed uint64, weeklasttime uint64, monthlasttime uint64) {
	creategameS2C := &Message.CreateGameS2C{
		RoomId: int32(roomid),
		ScorePool: scorepool,
		Seed: seed,
		WeekLastTime: weeklasttime,
		MonthLastTime: monthlasttime,
	}
	pbMsg := network.NewPBMsg(CreateGame, creategameS2C)
	session.SendPBMsg(pbMsg.MsgToBytes())
}
