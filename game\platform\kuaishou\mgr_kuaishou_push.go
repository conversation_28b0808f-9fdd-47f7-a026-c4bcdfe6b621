package kuaishou

import (
	"crypto/md5"
	"encoding/json"
	"fmt"
	"sync"
	"time"
	"zone/lib/core"
	"zone/lib/httpclient"
	"zone/lib/utils"

	"github.com/gin-gonic/gin"
)

const (
	HTTPStatusOK    = 200
	HeaderSignature = "kwaisign"
	// EmptyResponse   = ""
	// HeaderNonceStr  = "x-nonce-str"
	// HeaderTimestamp = "x-timestamp"
	// HeaderRoomID    = "x-roomid"
	// HeaderMsgType   = "x-msg-type"
)

// 回调函数类型定义
type (
	ResetDataCallback  func()
	AddCommentCallback func(roomId string, comment []byte)
	AddGiftCallback    func(roomId, msgId string, gifts []*Payload) // 修改为结构体切片
	AddLikeCallback    func(roomId string, like []byte)
	AddFansCallback    func(roomId, msgId string, fans []*Payload) // 修改为结构体切片
)

var (
	resetDataWeeklyCallback  ResetDataCallback
	resetDataMonthlyCallback ResetDataCallback
	addCommentCallback       AddCommentCallback
	addGiftCallback          AddGiftCallback
	addLikeCallback          AddLikeCallback
	addFansCallback          AddFansCallback
)

func RegisterCallbacks(
	weeklyReset ResetDataCallback,
	monthlyReset ResetDataCallback,
	addComment AddCommentCallback,
	addGift AddGiftCallback,
	addLike AddLikeCallback,
	addFans AddFansCallback,
) {
	resetDataWeeklyCallback = weeklyReset
	resetDataMonthlyCallback = monthlyReset
	addCommentCallback = addComment
	addGiftCallback = addGift
	addLikeCallback = addLike
	addFansCallback = addFans
	core.LogInfo("快手：回调函数注册完成")
}

type PayloadData struct {
	MessageId string           `json:"message_id"`
	Event     string           `json:"event"`
	AppId     string           `json:"app_id"`
	TimeStamp int64            `json:"timestamp"`
	Data      *PayloadDataInfo `json:"data"`
}

// 基础消息头（添加MsgId字段）
type BaseHeader struct {
	UniqueMessageID string `json:"unique_message_id"` // 兼容字段
	AuthorOpenID    string `json:"author_open_id"`
	RoomCode        string `json:"room_code"`
	PushType        string `json:"push_type"`
}

// 礼物数据结构
type PayloadDataInfo struct {
	BaseHeader
	Payload []*Payload `json:"payload"`
}

type Payload struct {
	UserInfo       User   `json:"userInfo"`
	UniqueNo       string `json:"uniqueNo"`
	GiftID         string `json:"giftId"`
	GiftName       string `json:"giftName"`
	GiftCount      int    `json:"giftCount"`
	GiftUnitPrice  int    `json:"giftUnitPrice"`
	GiftTotalPrice int    `json:"giftTotalPrice"`
	NormalGift     bool   `json:"normalGift"`
	Count          int    `json:"count"`
	Content        string `json:"content"`
	Timestamp      int64  `json:"timestamp"`
}

type User struct {
	UserID    string `json:"userId"`
	UserName  string `json:"userName"`
	AvatarUrl string `json:"headUrl"`
}

type PayloadResult struct {
	Result    int    `json:"result"`
	MessageId string `json:"message_id"`
}

var GiftTimeRecord *sync.Map = new(sync.Map)

// 修正签名验证函数
func verifySignature(body []byte) string {

	signStr := string(body) + LIVE_SECRET
	// 4. 计算 MD5
	hasher := md5.New()
	hasher.Write([]byte(signStr))
	return fmt.Sprintf("%x", hasher.Sum(nil))
}

type kuaishouPayload struct{}

var kuaishouPayloadInstance *kuaishouPayload

func GetkuaishouPayload() *kuaishouPayload {
	if kuaishouPayloadInstance == nil {
		kuaishouPayloadInstance = &kuaishouPayload{}
	}
	return kuaishouPayloadInstance
}

// RegisterGinRoutes 注册快手推送服务的Gin路由
// 包括评论、点赞、礼物、粉丝等推送接口，以及对应的测试接口和周/月清空接口
// 路由注册完成后会打印日志提示
func (payload *kuaishouPayload) RegisterGinRoutes(router *gin.Engine) {
	router.POST("/ks/comment/push/", payload.GinPayloadComment)
	router.POST("/ks/like/push/", payload.GinPayloadLike)
	router.POST("/ks/gift/push/", payload.GinPayloadGift)
	router.POST("/ks/fans/push/", payload.GinPayloadFans)

	router.POST("/gifttest", payload.GinPayloadGiftTest)
	router.POST("/commenttest", payload.GinPayloadCommentTest)
	router.POST("/liketest", payload.GinPayloadLikeTest)
	router.POST("/fanstest", payload.GinPayloadFansTest)

	router.POST("/weekclear", payload.GinWeekClear)
	router.POST("/monthclear", payload.GinMonthClear)

	core.LogInfo("kuaishou推送服务路由注册完成")
}

// 以下为修正后的关键处理器
func (payload *kuaishouPayload) GinPayloadFansTest(c *gin.Context) {
	result, err := httpclient.ReadRequestBody(c.Request.Body)
	if err != nil {
		core.LogError("Payload Read Body : ", result)
		c.Status(200)
		return
	}

	var gifts []*Payload
	if err := json.Unmarshal(result, &gifts); err != nil {
		core.LogError("Payload Gift Err:", string(result), err.Error())
		c.Status(200)
		return
	}

	if addFansCallback != nil && len(gifts) > 0 {
		addFansCallback(gifts[0].UserInfo.AvatarUrl, "", gifts) // 传递正确的Payload数据
	}
	c.Status(200)
}

// GinPayloadGiftTest 修正礼物测试处理器
func (payload *kuaishouPayload) GinPayloadGiftTest(c *gin.Context) {
	result, err := httpclient.ReadRequestBody(c.Request.Body)
	if err != nil {
		core.LogError("Payload Read Body : ", result)
		c.Status(200)
		return
	}

	var gifts []*Payload
	if err := json.Unmarshal(result, &gifts); err != nil {
		core.LogError("Payload Gift Err:", string(result), err.Error())
		c.Status(200)
		return
	}

	if addGiftCallback != nil && len(gifts) > 0 {
		addGiftCallback(gifts[0].UserInfo.AvatarUrl, "", gifts) // 传递正确的Payload数据
	}
	c.Status(200)
}

// GinPayloadFans 处理快手粉丝推送请求
// 1. 读取并验证请求体
// 2. 检查签名有效性
// 3. 解析推送数据
// 4. 记录非压测消息的时间戳
// 5. 调用粉丝添加回调函数
// 6. 返回处理结果
// 参数:
//
//	c *gin.Context: Gin框架的上下文对象
//
// 返回值: 通过c.JSON返回PayloadResult结构体
func (payload *kuaishouPayload) GinPayloadFans(c *gin.Context) {
	// headers := map[string]string{
	// 	"x-nonce-str": c.GetHeader(HeaderNonceStr),
	// 	"x-timestamp": c.GetHeader(HeaderTimestamp),
	// 	"x-roomid":    c.GetHeader(HeaderRoomID),
	// 	"x-msg-type":  c.GetHeader(HeaderMsgType),
	// }
	// core.LogInfo("礼物推送")

	// 读取请求体
	result, err := httpclient.ReadRequestBody(c.Request.Body)
	if err != nil {
		core.LogError("Payload Read Body Error:", err.Error())
		c.JSON(200, PayloadResult{
			Result:    0,
			MessageId: "无法读取PayloadGift数据",
		})
		return
	}

	// core.LogInfo("礼物推送:", string(result))
	// 获取签名
	signature := c.GetHeader(HeaderSignature)
	if signature != "" {
		// 验证签名
		expectedSign := verifySignature(result)
		if expectedSign != signature {
			core.LogError(fmt.Printf("签名验证失败 预期:%s 实际:%s", expectedSign, signature))
			c.JSON(200, PayloadResult{
				Result:    0,
				MessageId: fmt.Sprintf("签名验证失败 预期:%s 实际:%s", expectedSign, signature),
			})
			return
		}
	}

	// 解析数据
	var data PayloadData
	if err := json.Unmarshal(result, &data); err != nil {
		core.LogError("礼物数据解析失败:", string(result), err.Error())
		c.JSON(200, PayloadResult{
			Result:    0,
			MessageId: fmt.Sprintf("礼物数据解析失败:%s 错误:%v", string(result), err),
		})
		return
	}

	// 如果数据唯一标识符不是以"stress_"开头，则记录时间
	if !utils.HasPrefixOnly(data.Data.UniqueMessageID, "stress_") {
		GiftTimeRecord.Store(data.Data.UniqueMessageID, time.Now().UnixMilli())
	}

	// 如果有添加粉丝回调函数，则调用回调函数
	if addFansCallback != nil {
		addFansCallback(data.Data.RoomCode, data.Data.UniqueMessageID, data.Data.Payload)
	}
	c.JSON(200, PayloadResult{
		Result:    1,
		MessageId: data.MessageId,
	})
}

// GinPayloadGift 修正礼物推送处理器
func (payload *kuaishouPayload) GinPayloadGift(c *gin.Context) {
	// headers := map[string]string{
	// 	"x-nonce-str": c.GetHeader(HeaderNonceStr),
	// 	"x-timestamp": c.GetHeader(HeaderTimestamp),
	// 	"x-roomid":    c.GetHeader(HeaderRoomID),
	// 	"x-msg-type":  c.GetHeader(HeaderMsgType),
	// }
	// core.LogInfo("礼物推送")

	result, err := httpclient.ReadRequestBody(c.Request.Body)
	if err != nil {
		core.LogError("Payload Read Body Error:", err.Error())
		c.JSON(200, PayloadResult{
			Result:    0,
			MessageId: "无法读取PayloadGift数据",
		})
		return
	}

	core.LogInfo("礼物推送:", string(result))
	signature := c.GetHeader(HeaderSignature)
	if signature != "" {
		expectedSign := verifySignature(result)
		if expectedSign != signature {
			core.LogError(fmt.Printf("签名验证失败 预期:%s 实际:%s", expectedSign, signature))
			c.JSON(200, PayloadResult{
				Result:    0,
				MessageId: fmt.Sprintf("签名验证失败 预期:%s 实际:%s", expectedSign, signature),
			})
			return
		}
	}

	var data PayloadData
	if err := json.Unmarshal(result, &data); err != nil {
		core.LogError("礼物数据解析失败:", string(result), err.Error())
		c.JSON(200, PayloadResult{
			Result:    0,
			MessageId: fmt.Sprintf("礼物数据解析失败:%s 错误:%v", string(result), err),
		})
		return
	}

	if !utils.HasPrefixOnly(data.Data.UniqueMessageID, "stress_") {
		GiftTimeRecord.Store(data.Data.UniqueMessageID, time.Now().UnixMilli())
	}

	if addGiftCallback != nil {
		addGiftCallback(data.Data.RoomCode, data.Data.UniqueMessageID, data.Data.Payload)
	}
	c.JSON(200, PayloadResult{
		Result:    1,
		MessageId: data.MessageId,
	})
}

// GinPayloadComment 修正评论处理器签名验证
func (payload *kuaishouPayload) GinPayloadComment(c *gin.Context) {
	// headers := map[string]string{
	// 	"x-nonce-str": c.GetHeader(HeaderNonceStr),
	// 	"x-timestamp": c.GetHeader(HeaderTimestamp),
	// 	"x-roomid":    c.GetHeader(HeaderRoomID),
	// 	"x-msg-type":  c.GetHeader(HeaderMsgType),
	// }
	// core.LogInfo("评论推送")

	result, err := httpclient.ReadRequestBody(c.Request.Body)
	if err != nil {
		core.LogError("Payload Read Body Error:", err.Error())
		c.JSON(200, PayloadResult{
			Result:    0,
			MessageId: fmt.Sprintf("PayloadComment Error:%v", err),
		})
		return
	}

	// core.LogInfo("评论推送：", string(result))

	if signature := c.GetHeader(HeaderSignature); signature != "" {
		expectedSign := verifySignature(result)
		if expectedSign != signature {
			core.LogError(fmt.Printf("评论签名验证失败 预期:%s 实际:%s", expectedSign, signature))

			c.JSON(200, PayloadResult{
				Result:    0,
				MessageId: fmt.Sprintf("评论签名验证失败 预期:%s 实际:%s", expectedSign, signature),
			})
			return
		}
	}

	var data PayloadData
	if err := json.Unmarshal(result, &data); err != nil {
		core.LogError("评论数据解析失败:", string(result), err.Error())
		c.JSON(200, PayloadResult{
			Result:    0,
			MessageId: fmt.Sprintf("评论数据:%s,解析失败:%v", string(result), err),
		})
		return
	}

	if addCommentCallback != nil {
		addCommentCallback(data.Data.RoomCode, utils.HF_JtoB(data.Data.Payload))
	}
	c.JSON(200, PayloadResult{
		Result:    1,
		MessageId: data.MessageId,
	})
}

// Like相关数据结构保持不变（已包含在之前的完整代码中）

// GinPayloadLikeTest 修正点赞测试处理器
func (payload *kuaishouPayload) GinPayloadLikeTest(c *gin.Context) {
	result, err := httpclient.ReadRequestBody(c.Request.Body)
	if err != nil {
		core.LogError("Payload Read Error:", err)
		c.Status(200)
		return
	}

	var likes []*Payload
	// core.LogError("Payload Read Body : ", string(result))
	if err := json.Unmarshal(result, &likes); err != nil {
		core.LogError("点赞数据解析失败:", string(result), err.Error())
		c.Status(200)
		return
	}

	if addLikeCallback != nil && len(likes) > 0 {
		// 转换数据结构
		addLikeCallback(likes[0].UserInfo.AvatarUrl, result)
	}
	c.Status(200)
}

// GinPayloadLike 修正点赞推送接口
func (payload *kuaishouPayload) GinPayloadLike(c *gin.Context) {
	// 获取必要请求头
	// headers := map[string]string{
	// 	"x-nonce-str": c.GetHeader(HeaderNonceStr),
	// 	"x-timestamp": c.GetHeader(HeaderTimestamp),
	// 	"x-roomid":    c.GetHeader(HeaderRoomID),
	// 	"x-msg-type":  c.GetHeader(HeaderMsgType),
	// }
	// core.LogInfo("点赞推送")

	// 读取请求体
	result, err := httpclient.ReadRequestBody(c.Request.Body)
	if err != nil {
		core.LogError("请求体读取失败:", err.Error())

		c.JSON(200, PayloadResult{
			Result:    0,
			MessageId: fmt.Sprintf("PayloadLike Error:%v", err),
		})
		return
	}

	// core.LogInfo("点赞推送：", string(result))

	// 签名验证流程
	if signature := c.GetHeader(HeaderSignature); signature != "" {
		expectedSign := verifySignature(result)
		if expectedSign != signature {
			core.LogError(fmt.Printf("点赞签名验证失败 计算值:%s 请求头:%s", expectedSign, signature))

			c.JSON(200, PayloadResult{
				Result:    0,
				MessageId: fmt.Sprintf("点赞签名验证失败 计算值:%s 请求头:%s", expectedSign, signature),
			})
			return
		}
	}

	// 解析数据结构
	var data PayloadData
	if err := json.Unmarshal(result, &data); err != nil {
		core.LogError("评论数据解析失败:", string(result), err.Error())

		c.JSON(200, PayloadResult{
			Result:    0,
			MessageId: fmt.Sprintf("评论数据:%s 解析失败:%v", string(result), err),
		})
		return
	}

	// 执行回调处理
	if addLikeCallback != nil {
		addLikeCallback(data.Data.RoomCode, utils.HF_JtoB(data.Data.Payload))
	}

	c.JSON(200, PayloadResult{
		Result:    1,
		MessageId: data.MessageId,
	})
}

// 补充评论测试接口
func (payload *kuaishouPayload) GinPayloadCommentTest(c *gin.Context) {
	result, err := httpclient.ReadRequestBody(c.Request.Body)
	if err != nil {
		core.LogError("评论测试读取失败:", err)
		c.Status(HTTPStatusOK)
		return
	}

	var comments []*Payload
	if err := json.Unmarshal(result, &comments); err != nil {
		core.LogError("评论测试数据解析错误:", string(result), err)
		c.Status(200)
		return
	}

	if addCommentCallback != nil && len(comments) > 0 {
		addCommentCallback(comments[0].UserInfo.AvatarUrl, result)
	}
	c.Status(200)
}

func (payload *kuaishouPayload) readRequestBody(context *gin.Context) ([]byte, error) {
	return httpclient.ReadRequestBody(context.Request.Body)
}

// sendSuccessResponse 发送成功响应
// context: Gin上下文对象
func (payload *kuaishouPayload) sendSuccessResponse(context *gin.Context) {
	context.Status(200)
}

// GinWeekClear 周数据清理处理器
// 处理周数据重置请求
func (payload *kuaishouPayload) GinWeekClear(context *gin.Context) {
	core.LogInfo("收到周数据清理请求")

	// 读取请求体（虽然不使用，但保持API一致性）
	_, err := payload.readRequestBody(context)
	if err != nil {
		return
	}

	// 执行周数据重置
	if resetDataWeeklyCallback != nil {
		resetDataWeeklyCallback()
		core.LogInfo("周数据清理完成")
	} else {
		core.LogError("周数据重置回调函数未注册")
	}

	payload.sendSuccessResponse(context)
}

// GinMonthClear 月数据清理处理器
// 处理月数据重置请求
func (payload *kuaishouPayload) GinMonthClear(context *gin.Context) {
	core.LogInfo("收到月数据清理请求")

	// 读取请求体（虽然不使用，但保持API一致性）
	_, err := payload.readRequestBody(context)
	if err != nil {
		return
	}

	// 执行月数据重置
	if resetDataMonthlyCallback != nil {
		resetDataMonthlyCallback()
		core.LogInfo("月数据清理完成")
	} else {
		core.LogError("月数据重置回调函数未注册")
	}

	payload.sendSuccessResponse(context)
}
