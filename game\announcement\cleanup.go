package announcement

import (
	"fmt"
	"time"
	"zone/game/models"
	"zone/lib/core"
	"zone/lib/db"
	"zone/lib/storage"
)

// CleanupManager 公告清理管理器
type CleanupManager struct {
	cleanupInterval   time.Duration // 清理间隔
	batchSize         int           // 批量处理大小
	retentionPeriod   time.Duration // 保留期限
	enableAutoCleanup bool          // 是否启用自动清理
}

// CleanupStats 清理统计信息
type CleanupStats struct {
	TotalCleaned     int           `json:"total_cleaned"`      // 总清理数量
	LastCleanupTime  time.Time     `json:"last_cleanup_time"`  // 上次清理时间
	CleanupDuration  time.Duration `json:"cleanup_duration"`   // 清理耗时
	ExpiredCount     int           `json:"expired_count"`      // 过期公告数量
	DisabledCount    int           `json:"disabled_count"`     // 禁用公告数量
	RedisKeysCleared int           `json:"redis_keys_cleared"` // 清理的Redis键数量
}

// NewCleanupManager 创建清理管理器
func NewCleanupManager() *CleanupManager {
	return &CleanupManager{
		cleanupInterval:   1 * time.Hour,  // 每小时清理一次
		batchSize:         50,             // 每批处理50个
		retentionPeriod:   24 * time.Hour, // 过期后保留24小时再删除
		enableAutoCleanup: true,           // 默认启用自动清理
	}
}

// SetCleanupInterval 设置清理间隔
func (cm *CleanupManager) SetCleanupInterval(interval time.Duration) {
	cm.cleanupInterval = interval
	core.LogInfo("设置公告清理间隔", "间隔:", interval)
}

// SetBatchSize 设置批量处理大小
func (cm *CleanupManager) SetBatchSize(size int) {
	if size > 0 && size <= 1000 {
		cm.batchSize = size
		core.LogInfo("设置公告清理批量大小", "大小:", size)
	} else {
		core.LogError("无效的批量大小", "大小:", size)
	}
}

// SetRetentionPeriod 设置保留期限
func (cm *CleanupManager) SetRetentionPeriod(period time.Duration) {
	cm.retentionPeriod = period
	core.LogInfo("设置公告保留期限", "期限:", period)
}

// EnableAutoCleanup 启用自动清理
func (cm *CleanupManager) EnableAutoCleanup() {
	cm.enableAutoCleanup = true
	core.LogInfo("启用公告自动清理")
}

// DisableAutoCleanup 禁用自动清理
func (cm *CleanupManager) DisableAutoCleanup() {
	cm.enableAutoCleanup = false
	core.LogInfo("禁用公告自动清理")
}

// CleanupExpiredAnnouncements 清理过期公告
func (cm *CleanupManager) CleanupExpiredAnnouncements() *CleanupStats {
	startTime := core.TimeServer()
	stats := &CleanupStats{
		LastCleanupTime: startTime,
	}

	if !cm.enableAutoCleanup {
		core.LogDebug("自动清理已禁用，跳过清理")
		return stats
	}

	// core.LogInfo("开始清理过期公告")

	// 计算清理截止时间（过期时间 + 保留期限）
	// 使用服务器时间确保时区一致性
	cutoffTime := core.TimeServer().Add(-cm.retentionPeriod)

	// 查询需要清理的公告
	expiredAnnouncements := cm.findExpiredAnnouncements(cutoffTime)
	stats.ExpiredCount = len(expiredAnnouncements)

	if len(expiredAnnouncements) == 0 {
		stats.CleanupDuration = time.Since(startTime)
		return stats
	}

	// 批量清理公告
	cleanedCount := cm.batchCleanupAnnouncements(expiredAnnouncements)
	stats.TotalCleaned = cleanedCount

	// 清理相关的Redis键
	redisKeysCleared := cm.cleanupRelatedRedisKeys(expiredAnnouncements)
	stats.RedisKeysCleared = redisKeysCleared

	stats.CleanupDuration = time.Since(startTime)

	// core.LogInfo("公告清理完成",
	// 	"清理数量:", cleanedCount,
	// 	"Redis键清理数量:", redisKeysCleared,
	// 	"耗时:", stats.CleanupDuration)

	return stats
}

// CleanupDisabledAnnouncements 清理禁用的公告
func (cm *CleanupManager) CleanupDisabledAnnouncements(olderThan time.Duration) *CleanupStats {
	startTime := core.TimeServer()
	stats := &CleanupStats{
		LastCleanupTime: startTime,
	}

	core.LogInfo("开始清理禁用公告", "保留期限:", olderThan)

	// 计算清理截止时间（使用服务器时间确保时区一致性）
	cutoffTime := core.TimeServer().Add(-olderThan)

	// 查询需要清理的禁用公告
	disabledAnnouncements := cm.findDisabledAnnouncements(cutoffTime)
	stats.DisabledCount = len(disabledAnnouncements)

	if len(disabledAnnouncements) == 0 {
		core.LogDebug("没有找到需要清理的禁用公告")
		stats.CleanupDuration = time.Since(startTime)
		return stats
	}

	// 批量清理公告
	cleanedCount := cm.batchCleanupAnnouncements(disabledAnnouncements)
	stats.TotalCleaned = cleanedCount

	// 清理相关的Redis键
	redisKeysCleared := cm.cleanupRelatedRedisKeys(disabledAnnouncements)
	stats.RedisKeysCleared = redisKeysCleared

	stats.CleanupDuration = time.Since(startTime)

	core.LogInfo("禁用公告清理完成",
		"清理数量:", cleanedCount,
		"Redis键清理数量:", redisKeysCleared,
		"耗时:", stats.CleanupDuration)

	return stats
}

// findExpiredAnnouncements 查找过期公告
func (cm *CleanupManager) findExpiredAnnouncements(cutoffTime time.Time) []models.AnnouncementDB {
	// 将时间转换为Unix时间戳进行比较
	cutoffTimestamp := cutoffTime.Unix()

	query := fmt.Sprintf(`
		SELECT * FROM %s
		WHERE end_time <= %d
		ORDER BY end_time ASC
		LIMIT %d`,
		storage.TABLE_Announcement,
		cutoffTimestamp,
		cm.batchSize*2) // 查询更多以便批量处理

	// core.LogDebug("查找过期公告", "截止时间戳:", cutoffTimestamp, "截止时间:", cutoffTime.Format("2006-01-02 15:04:05"), "查询:", query)

	var announcementTemplate models.AnnouncementDB
	results := db.GetDBMgr().DBUser.GetAllData(query, &announcementTemplate)

	announcements := make([]models.AnnouncementDB, 0, len(results))
	for _, result := range results {
		announcement := *result.(*models.AnnouncementDB)
		announcements = append(announcements, announcement)
	}

	return announcements
}

// findDisabledAnnouncements 查找禁用公告
func (cm *CleanupManager) findDisabledAnnouncements(cutoffTime time.Time) []models.AnnouncementDB {
	// 将时间转换为Unix时间戳进行比较
	cutoffTimestamp := cutoffTime.Unix()

	query := fmt.Sprintf(`
		SELECT * FROM %s
		WHERE status = 0 AND updated_time <= %d
		ORDER BY updated_time ASC
		LIMIT %d`,
		storage.TABLE_Announcement,
		cutoffTimestamp,
		cm.batchSize*2)

	// core.LogDebug("查找禁用公告", "截止时间戳:", cutoffTimestamp, "截止时间:", cutoffTime.Format("2006-01-02 15:04:05"), "查询:", query)

	var announcementTemplate models.AnnouncementDB
	results := db.GetDBMgr().DBUser.GetAllData(query, &announcementTemplate)

	announcements := make([]models.AnnouncementDB, 0, len(results))
	for _, result := range results {
		announcement := *result.(*models.AnnouncementDB)
		announcements = append(announcements, announcement)
	}

	return announcements
}

// batchCleanupAnnouncements 批量清理公告
func (cm *CleanupManager) batchCleanupAnnouncements(announcements []models.AnnouncementDB) int {
	cleanedCount := 0

	for i := 0; i < len(announcements); i += cm.batchSize {
		end := i + cm.batchSize
		if end > len(announcements) {
			end = len(announcements)
		}

		batch := announcements[i:end]
		batchCleaned := cm.cleanupBatch(batch)
		cleanedCount += batchCleaned

		// 批量处理间隔，避免对数据库造成过大压力
		if i+cm.batchSize < len(announcements) {
			time.Sleep(100 * time.Millisecond)
		}
	}

	return cleanedCount
}

// cleanupBatch 清理一批公告
func (cm *CleanupManager) cleanupBatch(announcements []models.AnnouncementDB) int {
	cleanedCount := 0

	for _, announcement := range announcements {
		success := cm.deleteAnnouncement(announcement.Id)
		if success {
			cleanedCount++
		}
	}

	return cleanedCount
}

// deleteAnnouncement 删除单个公告
func (cm *CleanupManager) deleteAnnouncement(announcementID int) bool {
	deleteQuery := fmt.Sprintf("DELETE FROM %s WHERE id = %d", storage.TABLE_Announcement, announcementID)
	_, _, success := db.GetDBMgr().DBUser.Exec(deleteQuery)

	if !success {
		core.LogError("删除公告失败", "ID:", announcementID)
	}

	return success
}

// cleanupRelatedRedisKeys 清理相关的Redis键
func (cm *CleanupManager) cleanupRelatedRedisKeys(announcements []models.AnnouncementDB) int {
	totalCleared := 0

	for _, announcement := range announcements {
		// 使用新的key格式：announcement_sent:{session_id}:{announcement_id}
		// 由于我们不知道具体的session_id，使用通配符匹配
		newPattern := fmt.Sprintf("announcement_sent:*:%d", announcement.Id)

		// 同时清理可能存在的旧格式键：announcement_sent:{session_id}:{room_id}:{announcement_id}
		oldPattern := fmt.Sprintf("announcement_sent:*:*:%d", announcement.Id)

		patterns := []string{newPattern, oldPattern}

		for _, pattern := range patterns {
			keys, err := db.GetRedisMgr().Keys(pattern)
			if err != nil {
				core.LogError("获取Redis键失败:", err, "模式:", pattern)
				continue
			}

			// 删除匹配的键
			for _, key := range keys {
				_, err := db.GetRedisMgr().Del(key)
				if err != nil {
					core.LogError("删除Redis键失败:", err, "键:", key)
				} else {
					totalCleared++
				}
			}

			if len(keys) > 0 {
				core.LogDebug("清理公告Redis键", "公告ID:", announcement.Id, "模式:", pattern, "键数量:", len(keys))
			}
		}
	}

	core.LogInfo("Redis键清理完成", "总清理数量:", totalCleared)
	return totalCleared
}

// ForceCleanupAll 强制清理所有过期和禁用的公告
func (cm *CleanupManager) ForceCleanupAll() *CleanupStats {
	core.LogInfo("开始强制清理所有过期和禁用公告")

	// 清理过期公告
	expiredStats := cm.CleanupExpiredAnnouncements()

	// 清理7天前禁用的公告
	disabledStats := cm.CleanupDisabledAnnouncements(7 * 24 * time.Hour)

	// 合并统计信息
	totalStats := &CleanupStats{
		TotalCleaned:     expiredStats.TotalCleaned + disabledStats.TotalCleaned,
		LastCleanupTime:  core.TimeServer(),
		CleanupDuration:  expiredStats.CleanupDuration + disabledStats.CleanupDuration,
		ExpiredCount:     expiredStats.ExpiredCount,
		DisabledCount:    disabledStats.DisabledCount,
		RedisKeysCleared: expiredStats.RedisKeysCleared + disabledStats.RedisKeysCleared,
	}

	core.LogInfo("强制清理完成",
		"总清理数量:", totalStats.TotalCleaned,
		"过期公告:", totalStats.ExpiredCount,
		"禁用公告:", totalStats.DisabledCount,
		"Redis键清理:", totalStats.RedisKeysCleared,
		"总耗时:", totalStats.CleanupDuration)

	return totalStats
}

// GetCleanupConfig 获取清理配置
func (cm *CleanupManager) GetCleanupConfig() map[string]interface{} {
	return map[string]interface{}{
		"cleanup_interval":    cm.cleanupInterval.String(),
		"batch_size":          cm.batchSize,
		"retention_period":    cm.retentionPeriod.String(),
		"enable_auto_cleanup": cm.enableAutoCleanup,
	}
}
