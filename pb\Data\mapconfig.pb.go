// Generated from Excel file: MapConfig.xlsx

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.5.1
// source: mapconfig.proto

package Data

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Mapconfig 配置数据
type Mapconfig struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 编号
	Id int32 `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty"`
	// 礼物名称
	Name          string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Mapconfig) Reset() {
	*x = Mapconfig{}
	mi := &file_mapconfig_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Mapconfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Mapconfig) ProtoMessage() {}

func (x *Mapconfig) ProtoReflect() protoreflect.Message {
	mi := &file_mapconfig_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Mapconfig.ProtoReflect.Descriptor instead.
func (*Mapconfig) Descriptor() ([]byte, []int) {
	return file_mapconfig_proto_rawDescGZIP(), []int{0}
}

func (x *Mapconfig) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Mapconfig) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// MapconfigList 配置数据列表
type MapconfigList struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Items         []*Mapconfig           `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MapconfigList) Reset() {
	*x = MapconfigList{}
	mi := &file_mapconfig_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MapconfigList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MapconfigList) ProtoMessage() {}

func (x *MapconfigList) ProtoReflect() protoreflect.Message {
	mi := &file_mapconfig_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MapconfigList.ProtoReflect.Descriptor instead.
func (*MapconfigList) Descriptor() ([]byte, []int) {
	return file_mapconfig_proto_rawDescGZIP(), []int{1}
}

func (x *MapconfigList) GetItems() []*Mapconfig {
	if x != nil {
		return x.Items
	}
	return nil
}

var File_mapconfig_proto protoreflect.FileDescriptor

const file_mapconfig_proto_rawDesc = "" +
	"\n" +
	"\x0fmapconfig.proto\x12\x04Data\"/\n" +
	"\tMapconfig\x12\x0e\n" +
	"\x02Id\x18\x01 \x01(\x05R\x02Id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\"6\n" +
	"\rMapconfigList\x12%\n" +
	"\x05items\x18\x01 \x03(\v2\x0f.Data.MapconfigR\x05itemsB\x0eZ\fzone/pb/Datab\x06proto3"

var (
	file_mapconfig_proto_rawDescOnce sync.Once
	file_mapconfig_proto_rawDescData []byte
)

func file_mapconfig_proto_rawDescGZIP() []byte {
	file_mapconfig_proto_rawDescOnce.Do(func() {
		file_mapconfig_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_mapconfig_proto_rawDesc), len(file_mapconfig_proto_rawDesc)))
	})
	return file_mapconfig_proto_rawDescData
}

var file_mapconfig_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_mapconfig_proto_goTypes = []any{
	(*Mapconfig)(nil),     // 0: Data.Mapconfig
	(*MapconfigList)(nil), // 1: Data.MapconfigList
}
var file_mapconfig_proto_depIdxs = []int32{
	0, // 0: Data.MapconfigList.items:type_name -> Data.Mapconfig
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_mapconfig_proto_init() }
func file_mapconfig_proto_init() {
	if File_mapconfig_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_mapconfig_proto_rawDesc), len(file_mapconfig_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_mapconfig_proto_goTypes,
		DependencyIndexes: file_mapconfig_proto_depIdxs,
		MessageInfos:      file_mapconfig_proto_msgTypes,
	}.Build()
	File_mapconfig_proto = out.File
	file_mapconfig_proto_goTypes = nil
	file_mapconfig_proto_depIdxs = nil
}
