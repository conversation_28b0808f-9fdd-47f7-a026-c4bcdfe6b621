package utils

import (
	"sync"
	"sync/atomic"
)

type CountableSyncMap struct {
	sync.Map
	count int64
}

func (m *CountableSyncMap) Store(key, value interface{}) {
	m.Map.Store(key, value)
	atomic.AddInt64(&m.count, 1)
}
func (m *CountableSyncMap) Delete(key interface{}) {
	if _, loaded := m.Map.LoadAndDelete(key); loaded {
		atomic.AddInt64(&m.count, -1)
	}
}
func (m *CountableSyncMap) LoadAndDelete(key interface{}) (value interface{}, loaded bool) {
	value, loaded = m.Map.LoadAndDelete(key)
	if loaded {
		atomic.AddInt64(&m.count, -1)
	}
	return
}
func (m *CountableSyncMap) Len() int {
	return int(atomic.LoadInt64(&m.count))
}

func (m *CountableSyncMap) GetByIndex(n int) (key, value interface{}, ok bool) {
	if n < 0 || n >= m.Len() {
		return nil, nil, false
	}
	current := 0
	m.Range(func(k, v interface{}) bool {
		if current == n {
			key = k
			value = v
			ok = true
			return false // 终止遍历
		}
		current++
		return true // 继续遍历
	})
	return
}
