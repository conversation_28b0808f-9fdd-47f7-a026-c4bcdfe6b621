package utils

import (
	"bytes"
	"fmt"
	"github.com/gomodule/redigo/redis"
	"math"
	"runtime"
	"strconv"
	"strings"
	"time"
	"unsafe"
	"zone/lib/core"
)

func HF_MinInt64(a int64, b int) int64 {
	if a < int64(b) {
		return int64(a)
	}

	return int64(b)
}

//! int取最大
//func HF_MaxInt(a int, b int) int {
//	if a > b {
//		return a
//	}
//
//	return b
//}

//! 系统时间的偏移
//var ServerTimeOffset int64 = 0

//func TimeServer() time.Time {
//	return time.Unix(time.Now().Unix()+ServerTimeOffset, 0)
//}

//扩展支持各种格式
func HF_GetStartTime(start string) int64 {
	startday := HF_Atoi(start)
	startTime := core.GetZoneApp().GetOpenServer() + int64(startday-1)*86400
	return startTime
}

// 获得传入时间的五点，每日的刷新时间
func HF_GetDayUpdateTime(times int64) int64 {
	now := time.Unix(times, 0)

	if now.Hour() < 5 {
		return time.Date(now.Year(), now.Month(), now.Day(), 5, 0, 0, 0, now.Location()).Unix() - 86400
	} else {
		return time.Date(now.Year(), now.Month(), now.Day(), 5, 0, 0, 0, now.Location()).Unix()
	}
}

func HF_ItoA(s int) string {
	num := strconv.Itoa(s)
	return num
}

func HF_I64toA(s int64) string {
	num := strconv.FormatInt(s, 10)
	return num
}

// 重写生成连接池方法
func NewPool(ip string, db int, auth string) *redis.Pool {
	return &redis.Pool{
		MaxIdle:     50,
		MaxActive:   12000, // max number of connections
		IdleTimeout: 180 * time.Second,
		Dial: func() (redis.Conn, error) {
			c, err := redis.Dial("tcp", ip)
			if err != nil {
				panic(err.Error())
			}
			if auth != "" {
				c.Do("AUTH", auth)
			}
			c.Do("SELECT", db)
			return c, err
		},
	}
}

//把string转为[]int
func HF_StringArrToIntArr(src string) []int {
	stringArr := strings.Split(src, "|")
	intArr := make([]int, 0)
	for _, v := range stringArr {
		intArr = append(intArr, HF_Atoi(v))
	}
	return intArr
}

//获取当前队伍战斗力
//func HF_GetCurTeamFightNum(playerUID int64) int64 {
//	player := player.GetPlayerMgr().GetPlayer(playerUID, true)
//	if player == nil {
//		return 0
//	}
//	return player.Sql_UserBase.Fight
//}

//获取总队伍战斗力
//func HF_GetAllTeamFightNum(playerUID int64) int64 {
//	return HF_GetCurTeamFightNum(playerUID)
//}

//!获取武将武魂数量
//func HF_GetHeroWuHun(player *player.Player, heroid int) int {
//	if player == nil {
//		LogError("player is nil.")
//		return 0
//	}
//	itemId := 12000000 + heroid/10*100 + 1
//	return player.GetObjectNum(itemId)
//}

//! 加密消息
//func HF_DecodeCenterMsg(msghead string, msgdata []byte) []byte {
//	data := &protocol.MsgBase{
//		Msghead: proto.String(msghead),
//		Msgtime: proto.Int64(TimeServer().Unix()),
//		Msgsign: proto.String("222"),
//		Msgdata: []byte(msgdata),
//	}
//
//	msg, err := proto.Marshal(data)
//	if err != nil {
//		log.Println("HF_DecodeCenterMsg:", err)
//		return []byte("")
//	}
//
//	return msg
//}

// 修正玩家uid, king需要特殊处理
func HF_SetPUid(uid *int64, playerIndex int64) {
	if *uid > 0 && *uid < playerIndex {
		*uid = *uid + playerIndex
	}
}

//! 判断是否新的一天
//func HF_IsNewDate(checkDate string) bool {
//	lct, err := time.ParseInLocation(core.DATEFORMAT, checkDate, time.Local)
//	if err != nil {
//		LogError("HF_IsNewDate:", err.Error(), checkDate)
//		return false
//	}
//
//	checkToday := time.Date(lct.Year(), lct.Month(), lct.Day(), 5, 0, 0, 0, TimeServer().Location()).Unix()
//	if lct.Hour() >= 5 {
//		checkToday += core.DAY_SECS
//	}
//
//	if TimeServer().Unix() >= checkToday {
//		return true
//	} else {
//		return false
//	}
//}

//func HF_AbsInt(a int) int {
//	if a > 0 {
//		return a
//	}
//	return a * -1
//}

// 打印堆栈信息
func DumpStacks() {
	buf := make([]byte, 16384)
	buf = buf[:runtime.Stack(buf, true)]
	fmt.Printf("=== BEGIN goroutine stack dump ===\n%s\n=== END goroutine stack dump ===", buf)
}

func HF_Round(num float64) int {
	return int(num + math.Copysign(0.5, num))
}

func HF_ToFixed(num float64, precision int) float64 {
	output := math.Pow(10, float64(precision))
	return float64(HF_Round(num*output)) / output
}

func HF_SetBit(n int64, pos uint) int64 {
	n |= (1 << pos)
	return n
}

func HF_ClearBit(n int64, pos uint) int64 {
	var mask int64
	mask = ^(1 << pos)
	n &= int64(mask)
	return n
}

//计算下次月刷新时间
func HF_GetNextMonthStart() int64 {
	now := core.TimeServer()
	now = now.AddDate(0, 0, -now.Day()+1)
	return now.AddDate(0, 1, 0).Unix()
}

//计算下次周刷新时间
func HF_GetNextWeekStart() int64 {

	now := core.TimeServer()

	offset := int(time.Monday - now.Weekday())
	if offset > 0 {
		offset = -6
	}
	weekStart := time.Date(now.Year(), now.Month(), now.Day(), 5, 0, 0, 0, time.Local).AddDate(0, 0, offset)
	//fmt.Print(weekStart.Unix())
	return weekStart.Unix() + 7*core.DAY_SECS
}

//计算下次日刷新时间
func HF_GetNextDayStart() int64 {
	now := core.TimeServer()
	if now.Hour() < 5 {
		return time.Date(now.Year(), now.Month(), now.Day(), 5, 0, 0, 0, now.Location()).Unix()
	} else {
		return time.Date(now.Year(), now.Month(), now.Day(), 5, 0, 0, 0, now.Location()).Unix() + core.DAY_SECS
	}
}

//远古计算下次刷新时间
func HF_GetNextStart() int64 {
	//regTime := 1
	serverOpenTime := core.GetZoneApp().GetOpenServer()
	start := int64(0)
	nowStart := time.Unix(serverOpenTime, 0)
	if nowStart.Hour() < 5 {
		start = time.Date(nowStart.Year(), nowStart.Month(), nowStart.Day(), 5, 0, 0, 0, nowStart.Location()).Unix() - core.DAY_SECS
	} else {
		start = time.Date(nowStart.Year(), nowStart.Month(), nowStart.Day(), 5, 0, 0, 0, nowStart.Location()).Unix()
	}
	now := core.TimeServer()
	return start + ((now.Unix()-start)/core.DAY_SECS*2)*core.DAY_SECS*2 + core.DAY_SECS*2
}
func HF_GetNextTimeToLog() int64 {
	now := core.TimeServer()
	nextTime := time.Date(now.Year(), now.Month(), now.Day(), 23, 59, 0, 0, now.Location()).Unix()
	if nextTime < now.Unix() {
		nextTime += core.DAY_SECS
	}
	return nextTime
}

func HF_GetNewPitMapId() int {
	now := core.TimeServer().Unix()
	openTime := core.GetZoneApp().GetOpenServer()
	times := int(now-openTime) / (core.DAY_SECS * 2)
	return times%2 + 1
}

//角色创建时间为基准，参数为天数偏移
func HF_CalPlayerCreateTime(calTime int64, disDay int) int64 {
	cal := time.Unix(calTime, 0)
	rel := int64(0)
	if cal.Hour() < 5 {
		rel = time.Date(cal.Year(), cal.Month(), cal.Day(), 5, 0, 0, 0, cal.Location()).Unix() + core.DAY_SECS*int64(disDay-1)
	} else {
		rel = time.Date(cal.Year(), cal.Month(), cal.Day(), 5, 0, 0, 0, cal.Location()).Unix() + core.DAY_SECS*int64(disDay)
	}

	return rel
}

//计算时间 动态支持3种模式
func HF_CalTimeForConfig(stime string, rtime string) int64 {
	relTime := int64(0)
	//! 开放时间为整数时，为开服天数，一次性有效
	startday := HF_Atoi(stime)
	//! 如果是按照开服时间算
	if startday > 0 {
		//! 计算开服时间
		relTime = core.GetZoneApp().GetOpenServer() + int64((startday-1)*core.DAY_SECS)
	} else if startday < 0 {
		rTime, _ := time.ParseInLocation(core.DATEFORMAT, rtime, time.Local)
		relTime = rTime.Unix() + int64((-startday-1)*core.DAY_SECS)
	} else {
		//! 否则按照实际时间算
		t, err := time.ParseInLocation(core.DATEFORMAT, stime, time.Local)
		if err != nil {
			t, err = NewTimeUtil(core.TimeServer()).Parse(stime)
			if err != nil {
				core.LogError("时间填写错误:", err.Error())
				return relTime
			}
		}
		relTime = t.Unix()
	}
	return relTime
}

func str2bytes(s string) []byte {
	x := (*[2]uintptr)(unsafe.Pointer(&s))
	h := [3]uintptr{x[0], x[1], x[1]}
	return *(*[]byte)(unsafe.Pointer(&h))
}

func HF_GetGoroutineID() uint64 {
	b := make([]byte, 64)
	runtime.Stack(b, false)
	b = bytes.TrimPrefix(b, []byte("goroutine "))
	b = b[:bytes.IndexByte(b, ' ')]
	n, _ := strconv.ParseUint(string(b), 10, 64)
	return n
}

func HF_CheckStringForSea(context string) string {
	if strings.Contains(context, "'") {
		context = strings.Replace(context, "'", "’", -1)
	}
	return context
}
