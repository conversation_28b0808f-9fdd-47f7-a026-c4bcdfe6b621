// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.5.1
// source: common.proto

package Message

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 频繁同步数据
type PlayerSyncDTO struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	GameScore     int32                  `protobuf:"varint,1,opt,name=gameScore,proto3" json:"gameScore,omitempty"`         // 本局积分
	GiftScore     int32                  `protobuf:"varint,2,opt,name=giftScore,proto3" json:"giftScore,omitempty"`         // 本局礼物积分
	GameKillScore int32                  `protobuf:"varint,3,opt,name=gameKillScore,proto3" json:"gameKillScore,omitempty"` // 本局击杀积分
	CurrentRank   int32                  `protobuf:"varint,4,opt,name=currentRank,proto3" json:"currentRank,omitempty"`     // 本局排行
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PlayerSyncDTO) Reset() {
	*x = PlayerSyncDTO{}
	mi := &file_common_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PlayerSyncDTO) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlayerSyncDTO) ProtoMessage() {}

func (x *PlayerSyncDTO) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlayerSyncDTO.ProtoReflect.Descriptor instead.
func (*PlayerSyncDTO) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{0}
}

func (x *PlayerSyncDTO) GetGameScore() int32 {
	if x != nil {
		return x.GameScore
	}
	return 0
}

func (x *PlayerSyncDTO) GetGiftScore() int32 {
	if x != nil {
		return x.GiftScore
	}
	return 0
}

func (x *PlayerSyncDTO) GetGameKillScore() int32 {
	if x != nil {
		return x.GameKillScore
	}
	return 0
}

func (x *PlayerSyncDTO) GetCurrentRank() int32 {
	if x != nil {
		return x.CurrentRank
	}
	return 0
}

// 排行数据
type PlayerRankDTO struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	WinStreak     int32                  `protobuf:"varint,1,opt,name=winStreak,proto3" json:"winStreak,omitempty"`         // 连胜数
	CurrentRank   int32                  `protobuf:"varint,2,opt,name=currentRank,proto3" json:"currentRank,omitempty"`     // 本局排行
	WeekRank      int32                  `protobuf:"varint,3,opt,name=weekRank,proto3" json:"weekRank,omitempty"`           // 周排行
	WeekScore     int64                  `protobuf:"varint,4,opt,name=weekScore,proto3" json:"weekScore,omitempty"`         // 周积分
	MonthRank     int32                  `protobuf:"varint,5,opt,name=monthRank,proto3" json:"monthRank,omitempty"`         // 月排行
	MonthScore    int64                  `protobuf:"varint,6,opt,name=monthScore,proto3" json:"monthScore,omitempty"`       // 总积分
	FamilyDetails int32                  `protobuf:"varint,8,opt,name=familyDetails,proto3" json:"familyDetails,omitempty"` // 家族内排行
	GameScore     int32                  `protobuf:"varint,9,opt,name=gameScore,proto3" json:"gameScore,omitempty"`         // 本局积分
	Title         int32                  `protobuf:"varint,10,opt,name=title,proto3" json:"title,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PlayerRankDTO) Reset() {
	*x = PlayerRankDTO{}
	mi := &file_common_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PlayerRankDTO) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlayerRankDTO) ProtoMessage() {}

func (x *PlayerRankDTO) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlayerRankDTO.ProtoReflect.Descriptor instead.
func (*PlayerRankDTO) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{1}
}

func (x *PlayerRankDTO) GetWinStreak() int32 {
	if x != nil {
		return x.WinStreak
	}
	return 0
}

func (x *PlayerRankDTO) GetCurrentRank() int32 {
	if x != nil {
		return x.CurrentRank
	}
	return 0
}

func (x *PlayerRankDTO) GetWeekRank() int32 {
	if x != nil {
		return x.WeekRank
	}
	return 0
}

func (x *PlayerRankDTO) GetWeekScore() int64 {
	if x != nil {
		return x.WeekScore
	}
	return 0
}

func (x *PlayerRankDTO) GetMonthRank() int32 {
	if x != nil {
		return x.MonthRank
	}
	return 0
}

func (x *PlayerRankDTO) GetMonthScore() int64 {
	if x != nil {
		return x.MonthScore
	}
	return 0
}

func (x *PlayerRankDTO) GetFamilyDetails() int32 {
	if x != nil {
		return x.FamilyDetails
	}
	return 0
}

func (x *PlayerRankDTO) GetGameScore() int32 {
	if x != nil {
		return x.GameScore
	}
	return 0
}

func (x *PlayerRankDTO) GetTitle() int32 {
	if x != nil {
		return x.Title
	}
	return 0
}

// 玩家状态数据
type PlayerStateDTO struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	HeroId        int32                  `protobuf:"varint,1,opt,name=heroId,proto3" json:"heroId,omitempty"`         // 英雄id
	HeroLevel     int32                  `protobuf:"varint,2,opt,name=heroLevel,proto3" json:"heroLevel,omitempty"`   // 英雄等级
	Ally          int32                  `protobuf:"varint,3,opt,name=ally,proto3" json:"ally,omitempty"`             // 盟友id
	TargetCamp    int32                  `protobuf:"varint,4,opt,name=targetCamp,proto3" json:"targetCamp,omitempty"` // 目标阵营
	Name          string                 `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`              // 玩家名称
	Family        string                 `protobuf:"bytes,6,opt,name=family,proto3" json:"family,omitempty"`          // 家族名称
	BlockId       int32                  `protobuf:"varint,7,opt,name=blockId,proto3" json:"blockId,omitempty"`       // 区块id
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PlayerStateDTO) Reset() {
	*x = PlayerStateDTO{}
	mi := &file_common_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PlayerStateDTO) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlayerStateDTO) ProtoMessage() {}

func (x *PlayerStateDTO) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlayerStateDTO.ProtoReflect.Descriptor instead.
func (*PlayerStateDTO) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{2}
}

func (x *PlayerStateDTO) GetHeroId() int32 {
	if x != nil {
		return x.HeroId
	}
	return 0
}

func (x *PlayerStateDTO) GetHeroLevel() int32 {
	if x != nil {
		return x.HeroLevel
	}
	return 0
}

func (x *PlayerStateDTO) GetAlly() int32 {
	if x != nil {
		return x.Ally
	}
	return 0
}

func (x *PlayerStateDTO) GetTargetCamp() int32 {
	if x != nil {
		return x.TargetCamp
	}
	return 0
}

func (x *PlayerStateDTO) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PlayerStateDTO) GetFamily() string {
	if x != nil {
		return x.Family
	}
	return ""
}

func (x *PlayerStateDTO) GetBlockId() int32 {
	if x != nil {
		return x.BlockId
	}
	return 0
}

// class=Commo
// 玩家数据
type PlayerDTO struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PlayerId      int32                  `protobuf:"varint,1,opt,name=playerId,proto3" json:"playerId,omitempty"`    // 玩家id
	HeaderUrl     string                 `protobuf:"bytes,2,opt,name=headerUrl,proto3" json:"headerUrl,omitempty"`   // 头像url
	AnchorId      int32                  `protobuf:"varint,3,opt,name=anchorId,proto3" json:"anchorId,omitempty"`    // 主播id
	PlayerName    string                 `protobuf:"bytes,4,opt,name=playerName,proto3" json:"playerName,omitempty"` // 玩家昵称
	Sync          *PlayerSyncDTO         `protobuf:"bytes,5,opt,name=sync,proto3" json:"sync,omitempty"`             // 玩家积分
	Rank          *PlayerRankDTO         `protobuf:"bytes,6,opt,name=rank,proto3" json:"rank,omitempty"`             // 玩家排行
	State         *PlayerStateDTO        `protobuf:"bytes,7,opt,name=state,proto3" json:"state,omitempty"`           // 玩家状态
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PlayerDTO) Reset() {
	*x = PlayerDTO{}
	mi := &file_common_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PlayerDTO) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlayerDTO) ProtoMessage() {}

func (x *PlayerDTO) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlayerDTO.ProtoReflect.Descriptor instead.
func (*PlayerDTO) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{3}
}

func (x *PlayerDTO) GetPlayerId() int32 {
	if x != nil {
		return x.PlayerId
	}
	return 0
}

func (x *PlayerDTO) GetHeaderUrl() string {
	if x != nil {
		return x.HeaderUrl
	}
	return ""
}

func (x *PlayerDTO) GetAnchorId() int32 {
	if x != nil {
		return x.AnchorId
	}
	return 0
}

func (x *PlayerDTO) GetPlayerName() string {
	if x != nil {
		return x.PlayerName
	}
	return ""
}

func (x *PlayerDTO) GetSync() *PlayerSyncDTO {
	if x != nil {
		return x.Sync
	}
	return nil
}

func (x *PlayerDTO) GetRank() *PlayerRankDTO {
	if x != nil {
		return x.Rank
	}
	return nil
}

func (x *PlayerDTO) GetState() *PlayerStateDTO {
	if x != nil {
		return x.State
	}
	return nil
}

// 战斗进行中的同步数据
type PlayeringDTO struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PlayerId      int32                  `protobuf:"varint,1,opt,name=playerId,proto3" json:"playerId,omitempty"` // 玩家id
	Sync          *PlayerSyncDTO         `protobuf:"bytes,2,opt,name=sync,proto3" json:"sync,omitempty"`          // 玩家积分
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PlayeringDTO) Reset() {
	*x = PlayeringDTO{}
	mi := &file_common_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PlayeringDTO) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlayeringDTO) ProtoMessage() {}

func (x *PlayeringDTO) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlayeringDTO.ProtoReflect.Descriptor instead.
func (*PlayeringDTO) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{4}
}

func (x *PlayeringDTO) GetPlayerId() int32 {
	if x != nil {
		return x.PlayerId
	}
	return 0
}

func (x *PlayeringDTO) GetSync() *PlayerSyncDTO {
	if x != nil {
		return x.Sync
	}
	return nil
}

// 结算数据
type PlayerSettlementDTO struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PlayerId      int32                  `protobuf:"varint,1,opt,name=playerId,proto3" json:"playerId,omitempty"` //  玩家id
	Rank          *PlayerRankDTO         `protobuf:"bytes,2,opt,name=rank,proto3" json:"rank,omitempty"`          //  玩家排行
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PlayerSettlementDTO) Reset() {
	*x = PlayerSettlementDTO{}
	mi := &file_common_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PlayerSettlementDTO) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlayerSettlementDTO) ProtoMessage() {}

func (x *PlayerSettlementDTO) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlayerSettlementDTO.ProtoReflect.Descriptor instead.
func (*PlayerSettlementDTO) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{5}
}

func (x *PlayerSettlementDTO) GetPlayerId() int32 {
	if x != nil {
		return x.PlayerId
	}
	return 0
}

func (x *PlayerSettlementDTO) GetRank() *PlayerRankDTO {
	if x != nil {
		return x.Rank
	}
	return nil
}

// 主播数据
type AnchorDTO struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AnchorId      int32                  `protobuf:"varint,1,opt,name=anchorId,proto3" json:"anchorId,omitempty"`    // 主播id
	AnchorName    string                 `protobuf:"bytes,2,opt,name=anchorName,proto3" json:"anchorName,omitempty"` // 主播名称
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AnchorDTO) Reset() {
	*x = AnchorDTO{}
	mi := &file_common_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AnchorDTO) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnchorDTO) ProtoMessage() {}

func (x *AnchorDTO) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnchorDTO.ProtoReflect.Descriptor instead.
func (*AnchorDTO) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{6}
}

func (x *AnchorDTO) GetAnchorId() int32 {
	if x != nil {
		return x.AnchorId
	}
	return 0
}

func (x *AnchorDTO) GetAnchorName() string {
	if x != nil {
		return x.AnchorName
	}
	return ""
}

// 游戏(PK房间)信息
type GameInfoDTO struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	RoomId         int32                  `protobuf:"varint,1,opt,name=roomId,proto3" json:"roomId,omitempty"`                 // 房间id
	Anchor         *AnchorDTO             `protobuf:"bytes,2,opt,name=anchor,proto3" json:"anchor,omitempty"`                  // 创建主播信息
	AnchorsInGame  []*AnchorDTO           `protobuf:"bytes,3,rep,name=anchorsInGame,proto3" json:"anchorsInGame,omitempty"`    // 参与主播列表
	AnchorsWatch   []*AnchorDTO           `protobuf:"bytes,4,rep,name=anchorsWatch,proto3" json:"anchorsWatch,omitempty"`      // 观战主播列表
	GameStatus     int32                  `protobuf:"varint,5,opt,name=gameStatus,proto3" json:"gameStatus,omitempty"`         // 游戏状态 1准备中 2游戏中 3结算中 4结算完成
	AllPlayerCount int32                  `protobuf:"varint,6,opt,name=allPlayerCount,proto3" json:"allPlayerCount,omitempty"` // 玩家总人数
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *GameInfoDTO) Reset() {
	*x = GameInfoDTO{}
	mi := &file_common_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GameInfoDTO) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GameInfoDTO) ProtoMessage() {}

func (x *GameInfoDTO) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GameInfoDTO.ProtoReflect.Descriptor instead.
func (*GameInfoDTO) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{7}
}

func (x *GameInfoDTO) GetRoomId() int32 {
	if x != nil {
		return x.RoomId
	}
	return 0
}

func (x *GameInfoDTO) GetAnchor() *AnchorDTO {
	if x != nil {
		return x.Anchor
	}
	return nil
}

func (x *GameInfoDTO) GetAnchorsInGame() []*AnchorDTO {
	if x != nil {
		return x.AnchorsInGame
	}
	return nil
}

func (x *GameInfoDTO) GetAnchorsWatch() []*AnchorDTO {
	if x != nil {
		return x.AnchorsWatch
	}
	return nil
}

func (x *GameInfoDTO) GetGameStatus() int32 {
	if x != nil {
		return x.GameStatus
	}
	return 0
}

func (x *GameInfoDTO) GetAllPlayerCount() int32 {
	if x != nil {
		return x.AllPlayerCount
	}
	return 0
}

// 击杀类型统计
type KillTypeDTO struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SoldierId     int32                  `protobuf:"varint,1,opt,name=soldierId,proto3" json:"soldierId,omitempty"` // 士兵id
	KillCount     int32                  `protobuf:"varint,2,opt,name=killCount,proto3" json:"killCount,omitempty"` // 杀敌数量
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *KillTypeDTO) Reset() {
	*x = KillTypeDTO{}
	mi := &file_common_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *KillTypeDTO) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KillTypeDTO) ProtoMessage() {}

func (x *KillTypeDTO) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KillTypeDTO.ProtoReflect.Descriptor instead.
func (*KillTypeDTO) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{8}
}

func (x *KillTypeDTO) GetSoldierId() int32 {
	if x != nil {
		return x.SoldierId
	}
	return 0
}

func (x *KillTypeDTO) GetKillCount() int32 {
	if x != nil {
		return x.KillCount
	}
	return 0
}

type PlayerKillCountDTO struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PlayerId      int32                  `protobuf:"varint,1,opt,name=playerId,proto3" json:"playerId,omitempty"` // 玩家id
	Kills         []*KillTypeDTO         `protobuf:"bytes,2,rep,name=kills,proto3" json:"kills,omitempty"`        // 杀敌类型
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PlayerKillCountDTO) Reset() {
	*x = PlayerKillCountDTO{}
	mi := &file_common_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PlayerKillCountDTO) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlayerKillCountDTO) ProtoMessage() {}

func (x *PlayerKillCountDTO) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlayerKillCountDTO.ProtoReflect.Descriptor instead.
func (*PlayerKillCountDTO) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{9}
}

func (x *PlayerKillCountDTO) GetPlayerId() int32 {
	if x != nil {
		return x.PlayerId
	}
	return 0
}

func (x *PlayerKillCountDTO) GetKills() []*KillTypeDTO {
	if x != nil {
		return x.Kills
	}
	return nil
}

type PlayerOperateDTO struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PlayerId      int32                  `protobuf:"varint,1,opt,name=playerId,proto3" json:"playerId,omitempty"` //  玩家id
	Type          int32                  `protobuf:"varint,2,opt,name=type,proto3" json:"type,omitempty"`         //  操作类型  1、添加礼物  2、结盟  3、攻击 4、修改阵营名 5、修改英雄
	Pamram        int32                  `protobuf:"varint,3,opt,name=pamram,proto3" json:"pamram,omitempty"`     //  参数 只有送礼物会涉及到参数整合使用 礼物数量<<|礼物id 的位整合方式
	Name          string                 `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`          //  换名字
	MsgId         string                 `protobuf:"bytes,5,opt,name=msgId,proto3" json:"msgId,omitempty"`        //  礼物记录
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PlayerOperateDTO) Reset() {
	*x = PlayerOperateDTO{}
	mi := &file_common_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PlayerOperateDTO) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlayerOperateDTO) ProtoMessage() {}

func (x *PlayerOperateDTO) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlayerOperateDTO.ProtoReflect.Descriptor instead.
func (*PlayerOperateDTO) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{10}
}

func (x *PlayerOperateDTO) GetPlayerId() int32 {
	if x != nil {
		return x.PlayerId
	}
	return 0
}

func (x *PlayerOperateDTO) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *PlayerOperateDTO) GetPamram() int32 {
	if x != nil {
		return x.Pamram
	}
	return 0
}

func (x *PlayerOperateDTO) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PlayerOperateDTO) GetMsgId() string {
	if x != nil {
		return x.MsgId
	}
	return ""
}

type SyncPlayerDataDTO struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PlayerId      int32                  `protobuf:"varint,1,opt,name=playerId,proto3" json:"playerId,omitempty"` // 玩家id
	Sync          *PlayerSyncDTO         `protobuf:"bytes,2,opt,name=sync,proto3" json:"sync,omitempty"`          // 玩家积分
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SyncPlayerDataDTO) Reset() {
	*x = SyncPlayerDataDTO{}
	mi := &file_common_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SyncPlayerDataDTO) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncPlayerDataDTO) ProtoMessage() {}

func (x *SyncPlayerDataDTO) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncPlayerDataDTO.ProtoReflect.Descriptor instead.
func (*SyncPlayerDataDTO) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{11}
}

func (x *SyncPlayerDataDTO) GetPlayerId() int32 {
	if x != nil {
		return x.PlayerId
	}
	return 0
}

func (x *SyncPlayerDataDTO) GetSync() *PlayerSyncDTO {
	if x != nil {
		return x.Sync
	}
	return nil
}

type ScoreRankDTO struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PlayerId      int32                  `protobuf:"varint,1,opt,name=playerId,proto3" json:"playerId,omitempty"`    // 玩家id
	HeaderUrl     string                 `protobuf:"bytes,2,opt,name=headerUrl,proto3" json:"headerUrl,omitempty"`   // 头像url
	PlayerName    string                 `protobuf:"bytes,3,opt,name=playerName,proto3" json:"playerName,omitempty"` // 玩家昵称
	Score         int64                  `protobuf:"varint,4,opt,name=score,proto3" json:"score,omitempty"`          // 积分
	Title         int32                  `protobuf:"varint,5,opt,name=title,proto3" json:"title,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ScoreRankDTO) Reset() {
	*x = ScoreRankDTO{}
	mi := &file_common_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ScoreRankDTO) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScoreRankDTO) ProtoMessage() {}

func (x *ScoreRankDTO) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScoreRankDTO.ProtoReflect.Descriptor instead.
func (*ScoreRankDTO) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{12}
}

func (x *ScoreRankDTO) GetPlayerId() int32 {
	if x != nil {
		return x.PlayerId
	}
	return 0
}

func (x *ScoreRankDTO) GetHeaderUrl() string {
	if x != nil {
		return x.HeaderUrl
	}
	return ""
}

func (x *ScoreRankDTO) GetPlayerName() string {
	if x != nil {
		return x.PlayerName
	}
	return ""
}

func (x *ScoreRankDTO) GetScore() int64 {
	if x != nil {
		return x.Score
	}
	return 0
}

func (x *ScoreRankDTO) GetTitle() int32 {
	if x != nil {
		return x.Title
	}
	return 0
}

type FamilyRankDTO struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	FamilyName    string                 `protobuf:"bytes,1,opt,name=familyName,proto3" json:"familyName,omitempty"`    // 家族名称
	FamilyScore   int64                  `protobuf:"varint,2,opt,name=familyScore,proto3" json:"familyScore,omitempty"` // 家族积分
	Rank          []*ScoreRankDTO        `protobuf:"bytes,3,rep,name=rank,proto3" json:"rank,omitempty"`                // 家族数据
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FamilyRankDTO) Reset() {
	*x = FamilyRankDTO{}
	mi := &file_common_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FamilyRankDTO) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FamilyRankDTO) ProtoMessage() {}

func (x *FamilyRankDTO) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FamilyRankDTO.ProtoReflect.Descriptor instead.
func (*FamilyRankDTO) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{13}
}

func (x *FamilyRankDTO) GetFamilyName() string {
	if x != nil {
		return x.FamilyName
	}
	return ""
}

func (x *FamilyRankDTO) GetFamilyScore() int64 {
	if x != nil {
		return x.FamilyScore
	}
	return 0
}

func (x *FamilyRankDTO) GetRank() []*ScoreRankDTO {
	if x != nil {
		return x.Rank
	}
	return nil
}

var File_common_proto protoreflect.FileDescriptor

const file_common_proto_rawDesc = "" +
	"\n" +
	"\fcommon.proto\x12\n" +
	"PB.Message\"\x93\x01\n" +
	"\rPlayerSyncDTO\x12\x1c\n" +
	"\tgameScore\x18\x01 \x01(\x05R\tgameScore\x12\x1c\n" +
	"\tgiftScore\x18\x02 \x01(\x05R\tgiftScore\x12$\n" +
	"\rgameKillScore\x18\x03 \x01(\x05R\rgameKillScore\x12 \n" +
	"\vcurrentRank\x18\x04 \x01(\x05R\vcurrentRank\"\xa1\x02\n" +
	"\rPlayerRankDTO\x12\x1c\n" +
	"\twinStreak\x18\x01 \x01(\x05R\twinStreak\x12 \n" +
	"\vcurrentRank\x18\x02 \x01(\x05R\vcurrentRank\x12\x1a\n" +
	"\bweekRank\x18\x03 \x01(\x05R\bweekRank\x12\x1c\n" +
	"\tweekScore\x18\x04 \x01(\x03R\tweekScore\x12\x1c\n" +
	"\tmonthRank\x18\x05 \x01(\x05R\tmonthRank\x12\x1e\n" +
	"\n" +
	"monthScore\x18\x06 \x01(\x03R\n" +
	"monthScore\x12$\n" +
	"\rfamilyDetails\x18\b \x01(\x05R\rfamilyDetails\x12\x1c\n" +
	"\tgameScore\x18\t \x01(\x05R\tgameScore\x12\x14\n" +
	"\x05title\x18\n" +
	" \x01(\x05R\x05title\"\xc0\x01\n" +
	"\x0ePlayerStateDTO\x12\x16\n" +
	"\x06heroId\x18\x01 \x01(\x05R\x06heroId\x12\x1c\n" +
	"\theroLevel\x18\x02 \x01(\x05R\theroLevel\x12\x12\n" +
	"\x04ally\x18\x03 \x01(\x05R\x04ally\x12\x1e\n" +
	"\n" +
	"targetCamp\x18\x04 \x01(\x05R\n" +
	"targetCamp\x12\x12\n" +
	"\x04name\x18\x05 \x01(\tR\x04name\x12\x16\n" +
	"\x06family\x18\x06 \x01(\tR\x06family\x12\x18\n" +
	"\ablockId\x18\a \x01(\x05R\ablockId\"\x91\x02\n" +
	"\tPlayerDTO\x12\x1a\n" +
	"\bplayerId\x18\x01 \x01(\x05R\bplayerId\x12\x1c\n" +
	"\theaderUrl\x18\x02 \x01(\tR\theaderUrl\x12\x1a\n" +
	"\banchorId\x18\x03 \x01(\x05R\banchorId\x12\x1e\n" +
	"\n" +
	"playerName\x18\x04 \x01(\tR\n" +
	"playerName\x12-\n" +
	"\x04sync\x18\x05 \x01(\v2\x19.PB.Message.PlayerSyncDTOR\x04sync\x12-\n" +
	"\x04rank\x18\x06 \x01(\v2\x19.PB.Message.PlayerRankDTOR\x04rank\x120\n" +
	"\x05state\x18\a \x01(\v2\x1a.PB.Message.PlayerStateDTOR\x05state\"Y\n" +
	"\fPlayeringDTO\x12\x1a\n" +
	"\bplayerId\x18\x01 \x01(\x05R\bplayerId\x12-\n" +
	"\x04sync\x18\x02 \x01(\v2\x19.PB.Message.PlayerSyncDTOR\x04sync\"`\n" +
	"\x13PlayerSettlementDTO\x12\x1a\n" +
	"\bplayerId\x18\x01 \x01(\x05R\bplayerId\x12-\n" +
	"\x04rank\x18\x02 \x01(\v2\x19.PB.Message.PlayerRankDTOR\x04rank\"G\n" +
	"\tAnchorDTO\x12\x1a\n" +
	"\banchorId\x18\x01 \x01(\x05R\banchorId\x12\x1e\n" +
	"\n" +
	"anchorName\x18\x02 \x01(\tR\n" +
	"anchorName\"\x94\x02\n" +
	"\vGameInfoDTO\x12\x16\n" +
	"\x06roomId\x18\x01 \x01(\x05R\x06roomId\x12-\n" +
	"\x06anchor\x18\x02 \x01(\v2\x15.PB.Message.AnchorDTOR\x06anchor\x12;\n" +
	"\ranchorsInGame\x18\x03 \x03(\v2\x15.PB.Message.AnchorDTOR\ranchorsInGame\x129\n" +
	"\fanchorsWatch\x18\x04 \x03(\v2\x15.PB.Message.AnchorDTOR\fanchorsWatch\x12\x1e\n" +
	"\n" +
	"gameStatus\x18\x05 \x01(\x05R\n" +
	"gameStatus\x12&\n" +
	"\x0eallPlayerCount\x18\x06 \x01(\x05R\x0eallPlayerCount\"I\n" +
	"\vKillTypeDTO\x12\x1c\n" +
	"\tsoldierId\x18\x01 \x01(\x05R\tsoldierId\x12\x1c\n" +
	"\tkillCount\x18\x02 \x01(\x05R\tkillCount\"_\n" +
	"\x12PlayerKillCountDTO\x12\x1a\n" +
	"\bplayerId\x18\x01 \x01(\x05R\bplayerId\x12-\n" +
	"\x05kills\x18\x02 \x03(\v2\x17.PB.Message.KillTypeDTOR\x05kills\"\x84\x01\n" +
	"\x10PlayerOperateDTO\x12\x1a\n" +
	"\bplayerId\x18\x01 \x01(\x05R\bplayerId\x12\x12\n" +
	"\x04type\x18\x02 \x01(\x05R\x04type\x12\x16\n" +
	"\x06pamram\x18\x03 \x01(\x05R\x06pamram\x12\x12\n" +
	"\x04name\x18\x04 \x01(\tR\x04name\x12\x14\n" +
	"\x05msgId\x18\x05 \x01(\tR\x05msgId\"^\n" +
	"\x11SyncPlayerDataDTO\x12\x1a\n" +
	"\bplayerId\x18\x01 \x01(\x05R\bplayerId\x12-\n" +
	"\x04sync\x18\x02 \x01(\v2\x19.PB.Message.PlayerSyncDTOR\x04sync\"\x94\x01\n" +
	"\fScoreRankDTO\x12\x1a\n" +
	"\bplayerId\x18\x01 \x01(\x05R\bplayerId\x12\x1c\n" +
	"\theaderUrl\x18\x02 \x01(\tR\theaderUrl\x12\x1e\n" +
	"\n" +
	"playerName\x18\x03 \x01(\tR\n" +
	"playerName\x12\x14\n" +
	"\x05score\x18\x04 \x01(\x03R\x05score\x12\x14\n" +
	"\x05title\x18\x05 \x01(\x05R\x05title\"\x7f\n" +
	"\rFamilyRankDTO\x12\x1e\n" +
	"\n" +
	"familyName\x18\x01 \x01(\tR\n" +
	"familyName\x12 \n" +
	"\vfamilyScore\x18\x02 \x01(\x03R\vfamilyScore\x12,\n" +
	"\x04rank\x18\x03 \x03(\v2\x18.PB.Message.ScoreRankDTOR\x04rankb\x06proto3"

var (
	file_common_proto_rawDescOnce sync.Once
	file_common_proto_rawDescData []byte
)

func file_common_proto_rawDescGZIP() []byte {
	file_common_proto_rawDescOnce.Do(func() {
		file_common_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_common_proto_rawDesc), len(file_common_proto_rawDesc)))
	})
	return file_common_proto_rawDescData
}

var file_common_proto_msgTypes = make([]protoimpl.MessageInfo, 14)
var file_common_proto_goTypes = []any{
	(*PlayerSyncDTO)(nil),       // 0: PB.Message.PlayerSyncDTO
	(*PlayerRankDTO)(nil),       // 1: PB.Message.PlayerRankDTO
	(*PlayerStateDTO)(nil),      // 2: PB.Message.PlayerStateDTO
	(*PlayerDTO)(nil),           // 3: PB.Message.PlayerDTO
	(*PlayeringDTO)(nil),        // 4: PB.Message.PlayeringDTO
	(*PlayerSettlementDTO)(nil), // 5: PB.Message.PlayerSettlementDTO
	(*AnchorDTO)(nil),           // 6: PB.Message.AnchorDTO
	(*GameInfoDTO)(nil),         // 7: PB.Message.GameInfoDTO
	(*KillTypeDTO)(nil),         // 8: PB.Message.KillTypeDTO
	(*PlayerKillCountDTO)(nil),  // 9: PB.Message.PlayerKillCountDTO
	(*PlayerOperateDTO)(nil),    // 10: PB.Message.PlayerOperateDTO
	(*SyncPlayerDataDTO)(nil),   // 11: PB.Message.SyncPlayerDataDTO
	(*ScoreRankDTO)(nil),        // 12: PB.Message.ScoreRankDTO
	(*FamilyRankDTO)(nil),       // 13: PB.Message.FamilyRankDTO
}
var file_common_proto_depIdxs = []int32{
	0,  // 0: PB.Message.PlayerDTO.sync:type_name -> PB.Message.PlayerSyncDTO
	1,  // 1: PB.Message.PlayerDTO.rank:type_name -> PB.Message.PlayerRankDTO
	2,  // 2: PB.Message.PlayerDTO.state:type_name -> PB.Message.PlayerStateDTO
	0,  // 3: PB.Message.PlayeringDTO.sync:type_name -> PB.Message.PlayerSyncDTO
	1,  // 4: PB.Message.PlayerSettlementDTO.rank:type_name -> PB.Message.PlayerRankDTO
	6,  // 5: PB.Message.GameInfoDTO.anchor:type_name -> PB.Message.AnchorDTO
	6,  // 6: PB.Message.GameInfoDTO.anchorsInGame:type_name -> PB.Message.AnchorDTO
	6,  // 7: PB.Message.GameInfoDTO.anchorsWatch:type_name -> PB.Message.AnchorDTO
	8,  // 8: PB.Message.PlayerKillCountDTO.kills:type_name -> PB.Message.KillTypeDTO
	0,  // 9: PB.Message.SyncPlayerDataDTO.sync:type_name -> PB.Message.PlayerSyncDTO
	12, // 10: PB.Message.FamilyRankDTO.rank:type_name -> PB.Message.ScoreRankDTO
	11, // [11:11] is the sub-list for method output_type
	11, // [11:11] is the sub-list for method input_type
	11, // [11:11] is the sub-list for extension type_name
	11, // [11:11] is the sub-list for extension extendee
	0,  // [0:11] is the sub-list for field type_name
}

func init() { file_common_proto_init() }
func file_common_proto_init() {
	if File_common_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_common_proto_rawDesc), len(file_common_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   14,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_common_proto_goTypes,
		DependencyIndexes: file_common_proto_depIdxs,
		MessageInfos:      file_common_proto_msgTypes,
	}.Build()
	File_common_proto = out.File
	file_common_proto_goTypes = nil
	file_common_proto_depIdxs = nil
}
