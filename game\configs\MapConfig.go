package configs

import (
	"fmt"
	"sync"
	"zone/lib/config"
	"zone/lib/core"
	"zone/pb/Data"
)

// 主播ID为自定义ID，从1开始一直加
type MapConfig struct {
	MapMap *sync.Map
}

var s_MapConfig *MapConfig = nil

func GetMapConfig() *MapConfig {
	if s_MapConfig == nil {
		s_MapConfig = new(MapConfig)
	}

	return s_MapConfig
}

func (self *MapConfig) Init() {
	// 使用新的数据加载器
	var configs []*Data.Mapconfig
	err := config.GetDataLoader().LoadData("mapconfig", &configs)
	if err != nil {
		core.LogError("加载mapConfig失败:", err)
		return
	}

	self.MapMap = new(sync.Map)
	for _, config := range configs {
		self.MapMap.Store(int(config.Id), config)
	}

	core.LogInfo(fmt.Sprintf("加载BattleConfig成功，共%d条记录", len(configs)))
}

func (self *MapConfig) GetMapConfig(mapId int) *Data.Mapconfig {
	if config, ok := self.MapMap.Load(mapId); ok {
		return config.(*Data.Mapconfig)
	}
	return nil
}
