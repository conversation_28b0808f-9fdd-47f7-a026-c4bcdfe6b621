# LiveGame 暂停/恢复机制设计文档

## 概述

为了支持从LiveGame切换到LiveMultiGame，并在多人游戏结束后能够继续原有的单人游戏，我们为LiveGame设计了暂停/恢复机制。这确保了游戏状态的连续性和用户体验的流畅性。

## 设计原理

### 核心思想
- **永远只从LiveGame切换到LiveMultiGame**：设计中明确只支持单向切换到多人游戏
- **暂停而非销毁**：切换到多人游戏时暂停LiveGame，而不是销毁它
- **自动恢复**：多人游戏结束时自动恢复LiveGame的状态
- **时间补偿**：暂停期间的时间不计入游戏时长

## 技术实现

### 1. 数据结构扩展

在`LiveGame`结构体中新增暂停相关字段：

```go
type LiveGame struct {
    // ... 原有字段 ...
    
    // 暂停机制相关
    IsPaused        bool  // 是否暂停
    PauseStartTime  int64 // 暂停开始时间
    TotalPausedTime int64 // 总暂停时间
    PausedPlayState int   // 暂停前的游戏状态
    PausedEndTime   int64 // 暂停前的结束时间
}
```

### 2. 游戏状态常量

新增暂停状态常量：

```go
const (
    PLAY_STATE_WAIT = iota
    PLAY_STATE_END
    PLAY_STATE_READY
    PLAY_STATE_START
    PLAY_STATE_PAUSE // 暂停状态
)
```

### 3. 核心方法

#### 暂停游戏
```go
func (game *LiveGame) Pause() {
    if game.IsPaused {
        return // 已经暂停，无需重复操作
    }
    
    // 记录暂停状态
    game.IsPaused = true
    game.PauseStartTime = core.TimeServer().Unix()
    game.PausedPlayState = game.PlayState
    game.PausedEndTime = game.EndTime
    
    // 暂停特殊礼物定时器
    if game.SpecialGiftTimer != nil {
        game.SpecialGiftTimer.Stop()
    }
    
    // 设置为暂停状态
    game.PlayState = PLAY_STATE_PAUSE
}
```

#### 恢复游戏
```go
func (game *LiveGame) Resume() {
    if !game.IsPaused {
        return // 没有暂停，无需恢复
    }
    
    // 计算暂停时间
    pauseDuration := core.TimeServer().Unix() - game.PauseStartTime
    game.TotalPausedTime += pauseDuration
    
    // 恢复游戏状态
    game.PlayState = game.PausedPlayState
    
    // 调整结束时间（延长暂停的时间）
    if game.PausedEndTime > 0 {
        game.EndTime = game.PausedEndTime + pauseDuration
    }
    
    // 重置暂停标记
    game.IsPaused = false
    game.PauseStartTime = 0
    game.PausedPlayState = 0
    game.PausedEndTime = 0
    
    // 如果游戏正在进行中，重新启动特殊礼物定时器
    if game.PlayState == PLAY_STATE_START {
        game.StartSpecialGiftSafely()
    }
}
```

## 集成流程

### 1. 切换到多人游戏

```go
func (liveRoom *LiveRoom) SwitchToMultiGame(multiGame *LiveMultiGame) {
    // 暂停当前的LiveGame
    if liveRoom.LiveGame != nil {
        liveRoom.LiveGame.Pause()
    }
    
    // 设置多人游戏实例
    liveRoom.LiveMultiGame = multiGame
    liveRoom.GameMode = GameModeMulti
}
```

### 2. 切换回单人游戏

```go
func (liveRoom *LiveRoom) SwitchToLiveGame() {
    // 创建或恢复单人游戏
    if liveRoom.LiveGame == nil {
        liveRoom.LiveGame = NewLiveGame(liveRoom)
    } else if liveRoom.LiveGame.IsPausedState() {
        // 如果LiveGame处于暂停状态，恢复它
        liveRoom.LiveGame.Resume()
    } else {
        // 如果LiveGame没有暂停，重置它
        liveRoom.LiveGame.Reset(false)
    }
    
    liveRoom.LiveMultiGame = nil
    liveRoom.GameMode = GameModeLive
}
```

### 3. 多人游戏结束自动切换

```go
func (game *LiveMultiGame) GameOver(gameOverC2S *Message.GameOverC2S) {
    // ... 游戏结束逻辑 ...
    
    // 将所有房间切换回单人游戏模式
    rooms := game.GetConnectedRooms()
    for _, room := range rooms {
        if room != nil {
            room.SwitchToLiveGame() // 自动恢复LiveGame
        }
    }
}
```

## 事件处理保护

在暂停状态下，所有事件处理方法都会被跳过：

```go
func (game *LiveGame) ExComment() {
    // 如果游戏暂停，不处理评论事件
    if game.IsPaused {
        return
    }
    // ... 正常处理逻辑 ...
}
```

类似的保护应用于：
- `ExGift()` - 礼物事件处理
- `ExFans()` - 粉丝事件处理
- `ExLike()` - 点赞事件处理

## 时间管理

### 有效游戏时间计算

```go
func (game *LiveGame) GetEffectivePlayTime() int64 {
    if game.StartTime == 0 {
        return 0
    }
    
    currentTime := core.TimeServer().Unix()
    totalTime := currentTime - game.StartTime
    return totalTime - game.GetTotalPausedTime()
}
```

### 总暂停时间

```go
func (game *LiveGame) GetTotalPausedTime() int64 {
    totalPaused := game.TotalPausedTime
    if game.IsPaused && game.PauseStartTime > 0 {
        // 如果当前正在暂停，加上当前暂停的时间
        totalPaused += core.TimeServer().Unix() - game.PauseStartTime
    }
    return totalPaused
}
```

## 使用示例

### 基本使用流程

```go
// 1. 创建房间（默认单人游戏）
room := &LiveRoom{...}
room.Init() // 自动创建LiveGame

// 2. 开始单人游戏
room.LiveGame.GameStart()

// 3. 切换到多人游戏（自动暂停LiveGame）
multiGame, _ := multiGameMgr.CreateMultiGame()
room.SwitchToMultiGame(multiGame)

// 4. 多人游戏结束（自动恢复LiveGame）
multiGame.GameOver(nil) // 内部会调用room.SwitchToLiveGame()

// 5. LiveGame继续运行，时间已自动补偿
```

### 状态检查

```go
// 检查是否暂停
if room.LiveGame.IsPausedState() {
    fmt.Println("LiveGame当前处于暂停状态")
}

// 获取暂停时间
pausedTime := room.LiveGame.GetTotalPausedTime()
fmt.Printf("总暂停时间: %d秒\n", pausedTime)

// 获取有效游戏时间
effectiveTime := room.LiveGame.GetEffectivePlayTime()
fmt.Printf("有效游戏时间: %d秒\n", effectiveTime)
```

## 优势特性

1. **状态连续性**：LiveGame的所有状态在暂停期间都被完整保存
2. **时间准确性**：游戏时长不受暂停影响，自动补偿暂停时间
3. **资源保护**：暂停期间停止定时器和事件处理，节省资源
4. **自动化管理**：切换过程完全自动化，无需手动干预
5. **向后兼容**：不影响现有的单人游戏逻辑

## 测试验证

项目包含完整的测试用例：
- `TestLiveGamePauseResume`：测试基本的暂停/恢复功能
- `TestLiveGamePauseEventHandling`：测试暂停状态下的事件处理
- `TestLiveGameResetClearsPauseState`：测试重置时清除暂停状态

运行测试：
```bash
go test -v ./game/test -run TestLiveGame
```
