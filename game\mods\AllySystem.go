package mods

import (
	"errors"
	"fmt"
	"sync"
	"time"
)

// 常量定义
const (
	NoAlly             = 0  // 无盟友标识
	DefaultAllyTimeout = 30 // 默认盟友请求超时时间（秒）
	MaxPendingRequests = 10 // 最大待处理请求数量
)

// 错误定义
var (
	ErrPlayerNotFound       = errors.New("player not found")
	ErrSelfRequest          = errors.New("cannot send request to self")
	ErrPlayerAlreadyAllied  = errors.New("player already has ally")
	ErrDuplicateRequest     = errors.New("duplicate request to same target")
	ErrTargetHasPendingReq  = errors.New("target already has pending request")
	ErrPlayerStateChanged   = errors.New("player state changed")
	ErrNoPendingRequest     = errors.New("no pending request")
	ErrInvalidRequestStatus = errors.New("invalid request status")
	ErrRequesterNotFound    = errors.New("requester not found")
)

// 联盟系统核心结构
type AllySystem struct {
	players   *sync.Map   // 玩家存储 sync.Map[int]*AllyPlayer
	alliances *sync.Map   // 联盟关系存储 sync.Map[[2]int]struct{}
	reqMutex  *sync.Mutex // 全局请求互斥锁
	allyTime  int         // 盟友请求超时时间（秒）
}

// 玩家数据结构
type AllyPlayer struct {
	Id       int                  // 玩家ID
	LiveId   int64                // 在线ID
	mu       sync.RWMutex         // 细粒度读写锁
	sentReqs map[int]*RequestAlly // 已发送的请求 map[targetID]*Request
	recvReq  *RequestAlly         // 当前接收的请求
	ally     int                  // 盟友ID (NoAlly表示无盟友)
}

// 请求状态类型
type RequestStatus int

const (
	Pending  RequestStatus = iota // 等待中
	Accepted                      // 已接受
	Expired                       // 已过期
	Rejected                      // 已拒绝
)

// String 返回请求状态的字符串表示
func (rs RequestStatus) String() string {
	switch rs {
	case Pending:
		return "Pending"
	case Accepted:
		return "Accepted"
	case Expired:
		return "Expired"
	case Rejected:
		return "Rejected"
	default:
		return "Unknown"
	}
}

// 联盟请求结构
type RequestAlly struct {
	From       int           // 请求方ID
	FromLiveId int64         // 请求方在线ID
	To         int           // 接收方ID
	ToLiveId   int64         // 接收方在线ID
	CreateTime time.Time     // 创建时间
	Status     RequestStatus // 当前状态
	timer      *time.Timer   // 自动过期计时器
}

// CreateAllySystem 创建新的联盟系统实例
func CreateAllySystem(allyTimeoutSeconds int) *AllySystem {
	if allyTimeoutSeconds <= 0 {
		allyTimeoutSeconds = DefaultAllyTimeout
	}

	return &AllySystem{
		players:   &sync.Map{},
		alliances: &sync.Map{},
		reqMutex:  &sync.Mutex{},
		allyTime:  allyTimeoutSeconds,
	}
}

// AddPlayer 添加玩家到联盟系统
func (as *AllySystem) AddPlayer(id int, liveId int64) error {
	if id <= 0 || liveId <= 0 {
		return errors.New("invalid player id or live id")
	}

	// 检查玩家是否已存在
	if _, exists := as.players.Load(id); exists {
		return fmt.Errorf("player %d already exists", id)
	}

	player := &AllyPlayer{
		Id:       id,
		LiveId:   liveId,
		sentReqs: make(map[int]*RequestAlly),
		recvReq:  nil,
		ally:     NoAlly,
	}

	as.players.Store(id, player)
	return nil
}

// SendRequest 发送联盟请求（线程安全）
func (as *AllySystem) SendRequest(from, to int) error {
	if from == to {
		return ErrSelfRequest
	}

	as.reqMutex.Lock()
	defer as.reqMutex.Unlock()

	// 获取双方玩家对象
	fromObj, ok := as.players.Load(from)
	if !ok {
		return ErrPlayerNotFound
	}
	toObj, ok := as.players.Load(to)
	if !ok {
		return ErrPlayerNotFound
	}

	fp := fromObj.(*AllyPlayer)
	tp := toObj.(*AllyPlayer)

	// 预先获取LiveId，避免在锁外重复访问
	fp.mu.RLock()
	fromLiveId := fp.LiveId
	fromAlly := fp.ally
	fp.mu.RUnlock()

	tp.mu.RLock()
	toLiveId := tp.LiveId
	toAlly := tp.ally
	tp.mu.RUnlock()

	// 检查双方是否已有盟友
	if fromAlly != NoAlly || toAlly != NoAlly {
		return ErrPlayerAlreadyAllied
	}

	// 检查重复请求和目标状态
	fp.mu.RLock()
	_, hasDuplicateReq := fp.sentReqs[to]
	fp.mu.RUnlock()

	if hasDuplicateReq {
		return ErrDuplicateRequest
	}

	tp.mu.RLock()
	hasTargetPendingReq := (tp.recvReq != nil)
	tp.mu.RUnlock()

	if hasTargetPendingReq {
		return ErrTargetHasPendingReq
	}

	// 创建新请求
	req := &RequestAlly{
		From:       from,
		FromLiveId: fromLiveId,
		To:         to,
		ToLiveId:   toLiveId,
		CreateTime: time.Now(),
		Status:     Pending,
		timer:      time.NewTimer(time.Second * time.Duration(as.allyTime)),
	}

	// 设置过期回调
	go func() {
		<-req.timer.C
		as.handleExpired(req)
	}()

	// 原子化更新状态 - 按固定顺序获取锁避免死锁
	var firstLock, secondLock *sync.RWMutex
	if from < to {
		firstLock, secondLock = &fp.mu, &tp.mu
	} else {
		firstLock, secondLock = &tp.mu, &fp.mu
	}

	firstLock.Lock()
	secondLock.Lock()
	defer firstLock.Unlock()
	defer secondLock.Unlock()

	// 二次状态校验（防止竞态）
	if fp.ally != NoAlly || tp.ally != NoAlly {
		req.timer.Stop() // 清理timer
		return ErrPlayerStateChanged
	}
	if _, exists := fp.sentReqs[to]; exists {
		req.timer.Stop() // 清理timer
		return ErrDuplicateRequest
	}
	if tp.recvReq != nil {
		req.timer.Stop() // 清理timer
		return ErrTargetHasPendingReq
	}

	// 更新双方状态
	fp.sentReqs[to] = req
	tp.recvReq = req
	return nil
}

// handleExpired 处理请求过期
func (as *AllySystem) handleExpired(req *RequestAlly) {
	if req == nil || req.Status != Pending {
		return
	}

	as.reqMutex.Lock()
	defer as.reqMutex.Unlock()

	// 双重检查，防止竞态条件
	if req.Status != Pending {
		return
	}

	req.Status = Expired

	// 清理发送方
	if fromObj, ok := as.players.Load(req.From); ok {
		fp := fromObj.(*AllyPlayer)
		fp.mu.Lock()
		delete(fp.sentReqs, req.To)
		fp.mu.Unlock()
	}

	// 清理接收方
	if toObj, ok := as.players.Load(req.To); ok {
		tp := toObj.(*AllyPlayer)
		tp.mu.Lock()
		if tp.recvReq == req {
			tp.recvReq = nil
		}
		tp.mu.Unlock()
	}
}

// AcceptRequest 接受联盟请求
func (as *AllySystem) AcceptRequest(acceptor int) (int64, error) {
	as.reqMutex.Lock()
	defer as.reqMutex.Unlock()

	accObj, ok := as.players.Load(acceptor)
	if !ok {
		return 0, ErrPlayerNotFound
	}

	ap := accObj.(*AllyPlayer)

	// 先检查请求状态，不持有玩家锁太久
	ap.mu.RLock()
	if ap.recvReq == nil {
		ap.mu.RUnlock()
		return 0, ErrNoPendingRequest
	}
	req := ap.recvReq
	if req.Status != Pending {
		ap.mu.RUnlock()
		return 0, ErrInvalidRequestStatus
	}
	fromLiveId := req.FromLiveId
	fromID := req.From
	ap.mu.RUnlock()

	// 验证请求方对象存在
	_, ok = as.players.Load(fromID)
	if !ok {
		return 0, ErrRequesterNotFound
	}

	// 解除现有关系（如果有的话）
	as.breakAlliance(acceptor)
	as.breakAlliance(fromID)

	// 清理所有相关请求
	as.cleanupPlayerRequests(acceptor)
	as.cleanupPlayerRequests(fromID)

	// 创建新联盟关系（简化版，避免重复清理）
	as.alliances.Store([2]int{acceptor, fromID}, struct{}{})
	as.alliances.Store([2]int{fromID, acceptor}, struct{}{})

	// 更新玩家状态
	if playerObj, ok := as.players.Load(acceptor); ok {
		p := playerObj.(*AllyPlayer)
		p.mu.Lock()
		p.ally = fromID
		p.mu.Unlock()
	}
	if playerObj, ok := as.players.Load(fromID); ok {
		p := playerObj.(*AllyPlayer)
		p.mu.Lock()
		p.ally = acceptor
		p.mu.Unlock()
	}

	return fromLiveId, nil
}

// createAlliance 创建联盟关系
func (as *AllySystem) createAlliance(playerA, playerB int) {
	// 清理所有相关请求
	as.cleanupPlayerRequests(playerA)
	as.cleanupPlayerRequests(playerB)

	// 存储双向关系
	as.alliances.Store([2]int{playerA, playerB}, struct{}{})
	as.alliances.Store([2]int{playerB, playerA}, struct{}{})

	// 更新玩家状态
	if playerObj, ok := as.players.Load(playerA); ok {
		p := playerObj.(*AllyPlayer)
		p.mu.Lock()
		p.ally = playerB
		p.mu.Unlock()
	}
	if playerObj, ok := as.players.Load(playerB); ok {
		p := playerObj.(*AllyPlayer)
		p.mu.Lock()
		p.ally = playerA
		p.mu.Unlock()
	}
}

// cleanupPlayerRequests 清理玩家所有请求（无锁版本，调用方需要持有全局锁）
func (as *AllySystem) cleanupPlayerRequests(playerID int) {
	playerObj, ok := as.players.Load(playerID)
	if !ok {
		return
	}

	p := playerObj.(*AllyPlayer)

	// 收集需要清理的请求信息，避免在锁内进行复杂操作
	var sentReqs []*RequestAlly
	var recvReq *RequestAlly
	var targetIDs []int
	var fromID int

	p.mu.Lock()
	// 收集发送的请求
	for targetID, req := range p.sentReqs {
		if req != nil {
			sentReqs = append(sentReqs, req)
			targetIDs = append(targetIDs, targetID)
		}
	}
	// 收集接收的请求
	if p.recvReq != nil {
		recvReq = p.recvReq
		fromID = p.recvReq.From
	}
	// 清空本地状态
	p.sentReqs = make(map[int]*RequestAlly)
	p.recvReq = nil
	p.mu.Unlock()

	// 在锁外清理定时器和状态
	for _, req := range sentReqs {
		if req != nil {
			req.timer.Stop()
			req.Status = Expired
		}
	}
	if recvReq != nil {
		recvReq.timer.Stop()
		recvReq.Status = Expired
	}

	// 按固定顺序清理其他玩家的状态，避免死锁
	as.cleanupOtherPlayersRequests(playerID, targetIDs, fromID, sentReqs, recvReq)
}

// cleanupOtherPlayersRequests 按固定顺序清理其他玩家的请求状态
func (as *AllySystem) cleanupOtherPlayersRequests(playerID int, targetIDs []int, fromID int, sentReqs []*RequestAlly, recvReq *RequestAlly) {
	// 收集所有需要操作的玩家ID并排序，确保固定的锁获取顺序
	allPlayerIDs := make([]int, 0, len(targetIDs)+1)
	allPlayerIDs = append(allPlayerIDs, targetIDs...)
	if fromID != 0 && fromID != playerID {
		allPlayerIDs = append(allPlayerIDs, fromID)
	}

	// 去重并排序
	uniqueIDs := make(map[int]bool)
	for _, id := range allPlayerIDs {
		if id != playerID {
			uniqueIDs[id] = true
		}
	}

	sortedIDs := make([]int, 0, len(uniqueIDs))
	for id := range uniqueIDs {
		sortedIDs = append(sortedIDs, id)
	}

	// 简单排序
	for i := 0; i < len(sortedIDs); i++ {
		for j := i + 1; j < len(sortedIDs); j++ {
			if sortedIDs[i] > sortedIDs[j] {
				sortedIDs[i], sortedIDs[j] = sortedIDs[j], sortedIDs[i]
			}
		}
	}

	// 按固定顺序清理
	for _, id := range sortedIDs {
		if playerObj, ok := as.players.Load(id); ok {
			p := playerObj.(*AllyPlayer)
			p.mu.Lock()

			// 清理该玩家的接收请求（如果是我们发送的）
			for _, req := range sentReqs {
				if req != nil && req.To == id && p.recvReq == req {
					p.recvReq = nil
				}
			}

			// 清理该玩家的发送请求（如果是发给我们的）
			if id == fromID {
				delete(p.sentReqs, playerID)
			}

			p.mu.Unlock()
		}
	}
}

// PlayerDie 处理玩家死亡，清理所有相关状态
func (as *AllySystem) PlayerDie(playerID int) int64 {
	as.reqMutex.Lock()
	defer as.reqMutex.Unlock()

	// 解除所有联盟关系，返回盟友的LiveId用于通知
	allyLiveId := as.breakAlliance(playerID)

	// 清理所有相关请求
	as.cleanupPlayerRequests(playerID)

	// 根据游戏设计决定是否移除玩家
	// as.players.Delete(playerID)

	return allyLiveId
}

// breakAlliance 解除联盟关系
func (as *AllySystem) breakAlliance(playerID int) int64 {
	playerObj, ok := as.players.Load(playerID)
	if !ok {
		return 0
	}

	p := playerObj.(*AllyPlayer)
	p.mu.RLock()
	allyID := p.ally
	p.mu.RUnlock()

	if allyID == NoAlly {
		return 0
	}

	// 删除全局联盟记录
	as.alliances.Delete([2]int{playerID, allyID})
	as.alliances.Delete([2]int{allyID, playerID})

	// 更新自身状态
	p.mu.Lock()
	p.ally = NoAlly
	p.mu.Unlock()

	// 更新盟友状态
	if allyObj, ok := as.players.Load(allyID); ok {
		ally := allyObj.(*AllyPlayer)
		ally.mu.Lock()
		ally.ally = NoAlly
		allyLiveId := ally.LiveId
		ally.mu.Unlock()
		return allyLiveId
	}

	return 0
}

// IsAllied 查询两个玩家是否为盟友
func (as *AllySystem) IsAllied(playerA, playerB int) bool {
	if playerA == playerB {
		return false
	}
	_, ok := as.alliances.Load([2]int{playerA, playerB})
	return ok
}

// RejectRequest 拒绝联盟请求
func (as *AllySystem) RejectRequest(receiverID int) int64 {
	as.reqMutex.Lock()
	defer as.reqMutex.Unlock()

	receiverObj, ok := as.players.Load(receiverID)
	if !ok {
		return 0
	}

	receiver := receiverObj.(*AllyPlayer)

	// 先检查请求状态
	receiver.mu.RLock()
	if receiver.recvReq == nil || receiver.recvReq.Status != Pending {
		receiver.mu.RUnlock()
		return 0 // 无待处理请求
	}
	req := receiver.recvReq
	fromID := req.From
	fromLiveId := req.FromLiveId
	receiver.mu.RUnlock()

	// 清理请求状态
	req.timer.Stop()
	req.Status = Rejected

	// 清理接收方状态
	receiver.mu.Lock()
	if receiver.recvReq == req {
		receiver.recvReq = nil
	}
	receiver.mu.Unlock()

	// 清理发送方的请求状态
	if senderObj, ok := as.players.Load(fromID); ok {
		sender := senderObj.(*AllyPlayer)
		sender.mu.Lock()
		delete(sender.sentReqs, receiverID)
		sender.mu.Unlock()
	}

	return fromLiveId
}

// GetPlayerAlly 获取玩家的盟友ID
func (as *AllySystem) GetPlayerAlly(playerID int) int {
	if playerObj, ok := as.players.Load(playerID); ok {
		p := playerObj.(*AllyPlayer)
		p.mu.RLock()
		defer p.mu.RUnlock()
		return p.ally
	}
	return NoAlly
}

// GetPlayerStatus 获取玩家状态信息
func (as *AllySystem) GetPlayerStatus(playerID int) (ally int, sentCount int, hasPendingReq bool) {
	if playerObj, ok := as.players.Load(playerID); ok {
		p := playerObj.(*AllyPlayer)
		p.mu.RLock()
		defer p.mu.RUnlock()
		return p.ally, len(p.sentReqs), p.recvReq != nil
	}
	return NoAlly, 0, false
}

// BreakAlliance 主动解除联盟关系
func (as *AllySystem) BreakAlliance(playerID int) int64 {
	as.reqMutex.Lock()
	defer as.reqMutex.Unlock()

	return as.breakAlliance(playerID)
}
