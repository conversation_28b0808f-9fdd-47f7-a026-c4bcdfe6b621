package network

var AccessToken string
var ExpiresIn int64 = 3601
var IsTestServer bool = false
var Platform int = 0
var PlatformName string = ""

type RankList struct {
	RankResult        int         `json:"rank_result"`
	RankResultComment string      `json:"rank_result_comment"`
	RankUsers         []*RankUser `json:"rank_users"`
}

type RankUser struct {
	OpenID string `json:"open_id"`
	Rank   int64  `json:"rank"`
	Scores int64  `json:"scores"`
}

const (
	Platform_DouYin   = 0
	Platform_KuaiShou = 1
)
