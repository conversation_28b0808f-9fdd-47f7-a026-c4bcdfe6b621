/*
 Navicat MySQL Data Transfer

 Source Server         : 127.0.0.1_3306
 Source Server Type    : MySQL
 Source Server Version : 50726
 Source Host           : 127.0.0.1:3306
 Source Schema         : mapbattle

 Target Server Type    : MySQL
 Target Server Version : 50726
 File Encoding         : 65001

 Date: 12/05/2025 15:34:57
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

DROP TABLE IF EXISTS `playerinfo`;
CREATE TABLE `playerinfo`  (
  `playerid` bigint(20) NOT NULL,
  `openid` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `icon` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `playername` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `lastjoingame` bigint(20) NOT NULL,
  `joingametimes` int(11) NOT NULL,
  `winstreak` int(11) NOT NULL,
  `winstreakrank` int(11) NOT NULL,
  `weekscore` bigint(20) NOT NULL,
  `weekrank` int(11) NOT NULL,
  `monthscore` bigint(20) NOT NULL,
  `monthrank` int(11) NOT NULL,
  `totalcall` bigint(20) NOT NULL,
  `giftrecord` JSON NOT NULL,
  `giftvalue` bigint(20) NOT NULL,
  `family` JSON NOT NULL,
  PRIMARY KEY (`playerid`) USING BTREE
) ENGINE = MyISAM CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

DROP TABLE IF EXISTS `anchorinfo`;
CREATE TABLE `anchorinfo`  (
  `id` int(11) NOT NULL,
  `openid` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `icon` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `name` varchar(125) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `playerlist` JSON NOT NULL,
  `giftvalue` bigint(20) NOT NULL,
  `livetimes` int(11) NOT NULL,
  `time` bigint(20) NOT NULL,
  `closetime` bigint(20) NOT NULL,
  `scorePool` bigint(20) NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `familyinfo`;
CREATE TABLE `familyinfo`  (
  `id` int(11) NOT NULL,
  `name` varchar(125) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `score` bigint(20) NOT NULL,
  `playerlist` JSON NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

DROP TABLE IF EXISTS `announcement`;
CREATE TABLE `announcement`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `content` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '公告内容',
  `start_time` bigint(20) NOT NULL COMMENT '公告生效起始时间',
  `end_time` bigint(20) NOT NULL COMMENT '公告失效结束时间',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '公告状态：0-禁用，1-启用',
  `priority` int(11) NOT NULL DEFAULT 0 COMMENT '公告优先级，数值越大优先级越高',
  `created_time` bigint(20) NOT NULL COMMENT '创建时间',
  `updated_time` bigint(20) NOT NULL COMMENT '更新时间',
  `created_by` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'system' COMMENT '创建者',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_time_status` (`start_time`, `end_time`, `status`) USING BTREE COMMENT '时间和状态索引',
  INDEX `idx_priority` (`priority`) USING BTREE COMMENT '优先级索引'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '公告表' ROW_FORMAT = Dynamic;

DROP TABLE IF EXISTS `season`;
CREATE TABLE `season`  (
  `id` int(11) NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ServerUrl配置表（简化版）
DROP TABLE IF EXISTS `serverurl`;
CREATE TABLE `serverurl` (
  `id` int(11) NOT NULL COMMENT '平台编号：0-抖音，1-快手，99-本地开发环境',
  `websocket_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'WebSocket地址',
  `http_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'HTTP地址',
  `use_encrypt` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用加密：0-否，1-是',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'ServerUrl配置表' ROW_FORMAT = Dynamic;

-- 插入默认ServerUrl配置数据
INSERT INTO `serverurl` (`id`, `websocket_url`, `http_url`, `use_encrypt`) VALUES
(0, 'wss://ysyq.ziyewangluo.com', 'https://ysyq.ziyewangluo.com', 1),
(1, 'wss://kstest.ziyewangluo.com', 'https://ysyq.ziyewangluo.com', 1),
(99, 'ws://127.0.0.1:10027', 'http://127.0.0.1:10027', 1);

SET FOREIGN_KEY_CHECKS = 1;
