package manager

import (
	"sort"
	"zone/game/mods"
	"zone/lib/utils"
	"zone/pb/Message"
)

// ! 排行分类
const (
	RANK_WEEK = iota
	RANK_MONTH
	RANK_CALL
	RANK_WIN
)

type LiveTopNode struct {
	LiveId int64  //! 直播Id
	Week   int64  //! 周
	Month  int64  //! 月
	Call   int64  //! 召唤
	Win    int64  //!  连胜
	Rank   [4]int //! 排名
}

// LiveTopMgr ! 直播排名，世界排名，7天清理
type LiveTopMgr struct {
	MapLiveData *utils.SafeMapInt64

	LiveWeek  []*LiveTopNode //! 击杀
	LiveMonth []*LiveTopNode //! 积分
	LiveCall  []*LiveTopNode //! 礼物
	LiveWin   []*LiveTopNode //! 点赞
	WeekRank  *Message.GetScoreRankS2C
	MonthRank *Message.GetScoreRankS2C
	WinRank   *Message.GetScoreRankS2C
	CallRank  *Message.GetScoreRankS2C
}

var s_livetopmgr *LiveTopMgr = nil

// GetLiveTopMgr ! 排名管理类
func GetLiveTopMgr() *LiveTopMgr {
	if s_livetopmgr == nil {
		s_livetopmgr = new(LiveTopMgr)

		s_livetopmgr.MapLiveData = utils.NewSafeMapInt64()
		s_livetopmgr.WeekRank = new(Message.GetScoreRankS2C)
		s_livetopmgr.MonthRank = new(Message.GetScoreRankS2C)
		s_livetopmgr.WinRank = new(Message.GetScoreRankS2C)
		s_livetopmgr.CallRank = new(Message.GetScoreRankS2C)

		// 注册到mods包的管理器注册表
		mods.RegisterLiveTopManager(s_livetopmgr)
	}

	return s_livetopmgr
}

func (livetop *LiveTopMgr) ClearAllData() {
	//! 清理数据
	livetop.MapLiveData = utils.NewSafeMapInt64()
	livetop.WeekRank = new(Message.GetScoreRankS2C)
	livetop.MonthRank = new(Message.GetScoreRankS2C)
	livetop.WinRank = new(Message.GetScoreRankS2C)
	livetop.CallRank = new(Message.GetScoreRankS2C)

	livetop.LiveWin = make([]*LiveTopNode, 0)
	livetop.LiveWeek = make([]*LiveTopNode, 0)
	livetop.LiveMonth = make([]*LiveTopNode, 0)
	livetop.LiveCall = make([]*LiveTopNode, 0)
}

func (livetop *LiveTopMgr) ClearWeekData() {
	livetop.MapLiveData.Range(func(key, value interface{}) bool {
		node := value.(*LiveTopNode)
		node.Week = 0
		node.Rank[RANK_WEEK] = 9999
		return true
	})
	// livetop.LiveWeek = make([]*LiveTopNode, 0)
}

func (livetop *LiveTopMgr) ClearMonthData() {
	livetop.MapLiveData.Range(func(key, value interface{}) bool {
		node := value.(*LiveTopNode)
		node.Win = 0
		node.Month = 0
		node.Rank[RANK_WIN] = 9999
		node.Rank[RANK_MONTH] = 9999
		return true
	})
	// livetop.LiveMonth = make([]*LiveTopNode, 0)
}

func (livetop *LiveTopMgr) ClearCallData() {
	livetop.MapLiveData.Range(func(key, value interface{}) bool {
		node := value.(*LiveTopNode)
		node.Call = 0
		node.Rank[RANK_CALL] = 9999
		return true
	})
	// livetop.LiveMonth = make([]*LiveTopNode, 0)
}

func (livetop *LiveTopMgr) RankSort() {
	livetop.RankSortWeek()
	livetop.RankSortMonth()
	livetop.RankSortWin()
	livetop.RankSortCall()
}

func (livetop *LiveTopMgr) RankSortWeek() {
	sort.Sort(TopLiveWeek(livetop.LiveWeek))
	for i := 0; i < len(livetop.LiveWeek); i++ {
		livetop.LiveWeek[i].Rank[RANK_WEEK] = i + 1
	}
}

func (livetop *LiveTopMgr) RankSortMonth() {
	sort.Sort(TopLiveMonth(livetop.LiveMonth))
	for i := 0; i < len(livetop.LiveMonth); i++ {
		livetop.LiveMonth[i].Rank[RANK_MONTH] = i + 1
	}
}

func (livetop *LiveTopMgr) RankSortWin() {
	sort.Sort(TopLiveWin(livetop.LiveWin))
	for i := 0; i < len(livetop.LiveWin); i++ {
		livetop.LiveWin[i].Rank[RANK_WIN] = i + 1
	}
}

func (livetop *LiveTopMgr) RankSortCall() {
	sort.Sort(TopLiveCall(livetop.LiveCall))
	for i := 0; i < len(livetop.LiveCall); i++ {
		livetop.LiveCall[i].Rank[RANK_CALL] = i + 1
	}
}

func (livetop *LiveTopMgr) AddKill(liveId int64, value int) {
	if node, ok := livetop.MapLiveData.Load(liveId); ok {
		node.(*LiveTopNode).Week += int64(value)
		node.(*LiveTopNode).Month += int64(value)
	} else {
		newNodeInterface := livetop.NewTopNode(liveId)
		if newNodeInterface != nil {
			if newNode, ok := newNodeInterface.(*LiveTopNode); ok {
				newNode.Week = int64(value)
				newNode.Month += int64(value)
			}
		}
	}
}

func (livetop *LiveTopMgr) AddCall(liveId int64, value int) {
	if node, ok := livetop.MapLiveData.Load(liveId); ok {
		node.(*LiveTopNode).Call += int64(value)
	} else {
		newNodeInterface := livetop.NewTopNode(liveId)
		if newNodeInterface != nil {
			if newNode, ok := newNodeInterface.(*LiveTopNode); ok {
				newNode.Call += int64(value)
			}
		}
	}
}

func (livetop *LiveTopMgr) AddScore(liveId int64, value int64) {
	if node, ok := livetop.MapLiveData.Load(liveId); ok {
		node.(*LiveTopNode).Week += value
		node.(*LiveTopNode).Month += value
	} else {
		newNodeInterface := livetop.NewTopNode(liveId)
		if newNodeInterface != nil {
			if newNode, ok := newNodeInterface.(*LiveTopNode); ok {
				newNode.Week += value
				newNode.Month += value
			}
		}
	}
}

func (livetop *LiveTopMgr) SetWin(liveId int64, value int) {
	if node, ok := livetop.MapLiveData.Load(liveId); ok {
		node.(*LiveTopNode).Win = int64(value)
	} else {
		newNodeInterface := livetop.NewTopNode(liveId)
		if newNodeInterface != nil {
			if newNode, ok := newNodeInterface.(*LiveTopNode); ok {
				newNode.Win = int64(value)
			}
		}
	}
}

func (livetop *LiveTopMgr) NewTopNode(liveId int64) interface{} {
	//! 查找不到，则新增一个
	newNode := &LiveTopNode{
		LiveId: liveId,
		Week:   0,
		Month:  0,
		Call:   0,
		Win:    0,
		Rank:   [4]int{9999, 9999, 9999, 9999},
	}

	livetop.LiveCall = append(livetop.LiveCall, newNode)
	livetop.LiveMonth = append(livetop.LiveMonth, newNode)
	livetop.LiveWeek = append(livetop.LiveWeek, newNode)
	livetop.LiveWin = append(livetop.LiveWin, newNode)

	livetop.MapLiveData.Store(liveId, newNode)
	if len(livetop.WeekRank.Rank) < 100 {
		livetop.WeekRank.Rank = append(livetop.WeekRank.Rank, &Message.ScoreRankDTO{})
	}
	if len(livetop.MonthRank.Rank) < 100 {
		livetop.MonthRank.Rank = append(livetop.MonthRank.Rank, &Message.ScoreRankDTO{})
	}
	if len(livetop.WinRank.Rank) < 100 {
		livetop.WinRank.Rank = append(livetop.WinRank.Rank, &Message.ScoreRankDTO{})
	}
	if len(livetop.CallRank.Rank) < 100 {
		livetop.CallRank.Rank = append(livetop.CallRank.Rank, &Message.ScoreRankDTO{})
	}

	return newNode
}

func (livetop *LiveTopMgr) GetTop_Win(liveId int64) int {
	if node, ok := livetop.MapLiveData.Load(liveId); ok {
		return node.(*LiveTopNode).Rank[RANK_WIN]
	}
	return 9999
}

func (livetop *LiveTopMgr) GetTop_Call(liveId int64) int {
	if node, ok := livetop.MapLiveData.Load(liveId); ok {
		return node.(*LiveTopNode).Rank[RANK_CALL]
	}
	return 9999
}

func (livetop *LiveTopMgr) GetTop_Week(liveId int64) int {
	if node, ok := livetop.MapLiveData.Load(liveId); ok {
		return node.(*LiveTopNode).Rank[RANK_WEEK]
	}
	return 9999
}

func (livetop *LiveTopMgr) GetTop_Month(liveId int64) int {
	if node, ok := livetop.MapLiveData.Load(liveId); ok {
		return node.(*LiveTopNode).Rank[RANK_MONTH]
	}
	return 9999
}

func (livetop *LiveTopMgr) GetMonthRank() *Message.GetScoreRankS2C {
	// var re protocol.GetScoreRankS2C
	livetop.RankSortMonth()
	livetop.MonthRank.Type = 3
	// re.Rank = make([]*protocol.ScoreRankDTO, 0)
	for i := 0; i < len(livetop.LiveMonth); i++ {
		if i >= 100 {
			break
		}
		playerData := GetLiveMgr().GetLiveData(livetop.LiveMonth[i].LiveId)
		dto := livetop.MonthRank.Rank[i]
		dto.PlayerId = int32(playerData.PlayerId)
		dto.HeaderUrl = playerData.Icon
		dto.PlayerName = playerData.PlayerName
		dto.Score = playerData.MonthScore
		dto.Title = playerData.GetTitle()
	}
	return livetop.MonthRank
}

func (livetop *LiveTopMgr) GetWeekRank() *Message.GetScoreRankS2C {
	// var re protocol.GetScoreRankS2C
	livetop.RankSortWeek()
	livetop.WeekRank.Type = 2
	// re.Rank = make([]*protocol.ScoreRankDTO, 0)
	for i := 0; i < len(livetop.LiveWeek); i++ {
		if i >= 100 {
			break
		}

		playerData := GetLiveMgr().GetLiveData(livetop.LiveWeek[i].LiveId)
		dto := livetop.WeekRank.Rank[i]
		dto.PlayerId = int32(playerData.PlayerId)
		dto.HeaderUrl = playerData.Icon
		dto.PlayerName = playerData.PlayerName
		dto.Score = playerData.WeekScore
		dto.Title = playerData.GetTitle()
	}
	return livetop.WeekRank
}

func (livetop *LiveTopMgr) GetWinRank() *Message.GetScoreRankS2C {
	// var re protocol.GetScoreRankS2C
	livetop.RankSortWin()
	livetop.WinRank.Type = 4
	// re.Rank = make([]*protocol.ScoreRankDTO, 0)
	for i := 0; i < len(livetop.LiveWin); i++ {
		if i >= 100 {
			break
		}
		playerData := GetLiveMgr().GetLiveData(livetop.LiveWin[i].LiveId)
		playerData.WinStreakRank = livetop.LiveWin[i].Rank[RANK_WIN]
		dto := livetop.WinRank.Rank[i]
		dto.PlayerId = int32(playerData.PlayerId)
		dto.HeaderUrl = playerData.Icon
		dto.PlayerName = playerData.PlayerName
		dto.Score = int64(playerData.WinStreak)
		dto.Title = playerData.GetTitle()
	}
	return livetop.WinRank
}

func (livetop *LiveTopMgr) GetCallRank() *Message.GetScoreRankS2C {
	// var re protocol.GetScoreRankS2C
	livetop.RankSortCall()
	livetop.CallRank.Type = 5
	// re.Rank = make([]*protocol.ScoreRankDTO, 0)
	for i := 0; i < len(livetop.LiveCall); i++ {
		if i >= 100 {
			break
		}
		playerData := GetLiveMgr().GetLiveData(livetop.LiveCall[i].LiveId)
		dto := livetop.CallRank.Rank[i]
		dto.PlayerId = int32(playerData.PlayerId)
		dto.HeaderUrl = playerData.Icon
		dto.PlayerName = playerData.PlayerName
		dto.Score = playerData.TotalCall
		dto.Title = playerData.GetTitle()
	}
	return livetop.CallRank
}

// TopLiveWeek ! 直播击杀排行榜
type TopLiveWeek []*LiveTopNode

func (s TopLiveWeek) Len() int      { return len(s) }
func (s TopLiveWeek) Swap(i, j int) { s[i], s[j] = s[j], s[i] }
func (s TopLiveWeek) Less(i, j int) bool {
	return s[i].Week > s[j].Week
}

// TopLiveCall ! 直播击杀排行榜
type TopLiveCall []*LiveTopNode

func (s TopLiveCall) Len() int      { return len(s) }
func (s TopLiveCall) Swap(i, j int) { s[i], s[j] = s[j], s[i] }
func (s TopLiveCall) Less(i, j int) bool {
	return s[i].Call > s[j].Call
}

// TopLiveMonth ! 直播积分排行榜
type TopLiveMonth []*LiveTopNode

func (s TopLiveMonth) Len() int      { return len(s) }
func (s TopLiveMonth) Swap(i, j int) { s[i], s[j] = s[j], s[i] }
func (s TopLiveMonth) Less(i, j int) bool {
	return s[i].Month > s[j].Month
}

// TopLiveWin ! 直播积分排行榜
type TopLiveWin []*LiveTopNode

func (s TopLiveWin) Len() int      { return len(s) }
func (s TopLiveWin) Swap(i, j int) { s[i], s[j] = s[j], s[i] }
func (s TopLiveWin) Less(i, j int) bool {
	return s[i].Win > s[j].Win
}
