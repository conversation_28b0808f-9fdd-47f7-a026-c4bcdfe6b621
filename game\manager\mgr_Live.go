package manager

import (
	"fmt"
	"sync"
	"zone/game/models"
	"zone/game/mods"
	"zone/game/platform/bytedance"
	"zone/game/platform/kuaishou"
	"zone/lib/core"
	"zone/lib/db"
	"zone/lib/network"
	"zone/lib/payload"
	"zone/lib/storage"
	"zone/lib/utils"
)

// LiveMgr ! 直播用户管理
// ! 全局的用户数据管理，参加过游戏的用户在这里都会有记录
// ! 每周3的5：00数据进行清理，增加赛季的概念，每一周一个赛季
// ! 玩家加入一个房间后，可以重复加入多个房间，多个阵营方
type LiveMgr struct {
	MapLivePlayer    *sync.Map //! 在线用户，以直播Id为主键，结果是 LivePlayer
	MapLiveData      *sync.Map //!	LiveId的数据,结构 models.LivePlayerDB
	MapLiveOpenId    *sync.Map //! 以抖音的OpenId为主键，结构是 models.LivePlayerDB
	MapRoomId2OpenId *sync.Map //! OpenId 映射直播 Id
	MaxLiveId        int64     //! 已存在的直播游戏Id
}

// RoomData ! 主播房间
type RoomData struct {
	OpenId string //! 主播Id
	RoomId string //! 房间号
}

var s_LiveMgr *LiveMgr = nil

func GetLiveMgr() *LiveMgr {
	if s_LiveMgr == nil {
		s_LiveMgr = new(LiveMgr)

		s_LiveMgr.MapLiveData = new(sync.Map)
		s_LiveMgr.MapLivePlayer = new(sync.Map)
		s_LiveMgr.MapLiveOpenId = new(sync.Map)

		s_LiveMgr.MapRoomId2OpenId = new(sync.Map)

		// 注册ByteDance回调函数，解决循环引用问题
		if network.Platform == network.Platform_DouYin {
			s_LiveMgr.registerByteDanceCallbacks()
		} else {
			s_LiveMgr.registerKuaiShouCallbacks()
		}

		// 注册到mods包的管理器注册表
		mods.RegisterLiveManager(s_LiveMgr)
	}

	return s_LiveMgr
}

// registerByteDanceCallbacks 注册ByteDance回调函数
// 解决循环引用问题：通过回调机制而不是直接import
func (live *LiveMgr) registerByteDanceCallbacks() {
	// 注册数据重置回调
	weeklyResetCallback := func() {
		live.ResetDataWeekly()
	}

	monthlyResetCallback := func() {
		live.ResetDataMonthly()
	}

	// 注册消息处理回调
	addCommentCallback := func(roomId string, comment []byte) {
		live.AddComment(roomId, comment)
	}

	addGiftCallback := func(roomId string, gifts []*bytedance.Payload) {
		live.AddGift(roomId, payload.ConvertDYPayloadData(gifts), "")
	}

	addLikeCallback := func(roomId string, like []byte) {
		live.AddLike(roomId, like)
	}

	addFansCallback := func(roomId string, fans []*bytedance.Payload) {
		live.AddFans(roomId, payload.ConvertDYPayloadData(fans), "")
	}

	// 调用bytedance包的注册函数
	bytedance.RegisterCallbacks(
		weeklyResetCallback,
		monthlyResetCallback,
		addCommentCallback,
		addGiftCallback,
		addLikeCallback,
		addFansCallback,
	)
}

func (live *LiveMgr) registerKuaiShouCallbacks() {
	// 注册数据重置回调
	weeklyResetCallback := func() {
		live.ResetDataWeekly()
	}

	monthlyResetCallback := func() {
		live.ResetDataMonthly()
	}

	// 注册消息处理回调
	addCommentCallback := func(roomId string, comment []byte) {
		live.AddComment(roomId, comment)
	}

	addGiftCallback := func(roomId, msgId string, gifts []*kuaishou.Payload) {
		live.AddGift(roomId, payload.ConvertKSPayloadData(gifts), msgId)
	}

	addLikeCallback := func(roomId string, like []byte) {
		live.AddLike(roomId, like)
	}

	addFansCallback := func(roomId, msgId string, fans []*kuaishou.Payload) {
		live.AddFans(roomId, payload.ConvertKSPayloadData(fans), msgId)
	}

	// 调用bytedance包的注册函数
	kuaishou.RegisterCallbacks(
		weeklyResetCallback,
		monthlyResetCallback,
		addCommentCallback,
		addGiftCallback,
		addLikeCallback,
		addFansCallback,
	)
}

// GetData ! 从数据库载入数据，启动时，后续优化
// ! 从数据库拉起用户
// ! 中心服生成LiveId
func (live *LiveMgr) GetData() {
	var liveData models.LivePlayerDB
	sql := fmt.Sprintf("select * from `%s`", storage.TABLE_Player)
	res := db.GetDBMgr().DBUser.GetAllData(sql, &liveData)
	for i := 0; i < len(res); i++ {
		data := res[i].(*models.LivePlayerDB)
		data.Init(storage.TABLE_Player, data, true)
		data.Decode()

		// if data.ArrRecord == nil {
		// 	data.ArrRecord = make([]*models.San_LiveRecord, 0)
		// }

		live.MapLiveData.Store(data.PlayerId, data)
		live.MapLiveOpenId.Store(data.OpenId, data)

		if data.PlayerId > live.MaxLiveId {
			live.MaxLiveId = data.PlayerId
		}

		GetSeasonMgr().CheckPlayer(int(data.PlayerId), data.MonthScore)

		//! 加入排行数据
		nodeInterface := GetLiveTopMgr().NewTopNode(data.PlayerId)
		if nodeInterface != nil {
			if node, ok := nodeInterface.(*LiveTopNode); ok {
				node.Week = data.WeekScore
				node.Call = data.TotalCall
				node.Win = int64(data.WinStreak)
				node.Month = data.MonthScore
			}
		}
	}

	live.Save()
}

func (live *LiveMgr) GetLiveData(liveId int64) *models.LivePlayerDB {
	if lp, ok := live.MapLiveData.Load(liveId); ok {
		if utils.IsNotNil(lp) {
			return lp.(*models.LivePlayerDB)
		}
	}

	return nil
}

// Save ! 保存数据
func (live *LiveMgr) Save() {
	GetLiveTopMgr().RankSort()
	live.MapLiveData.Range(func(key, value interface{}) bool {
		liveId := key.(int64)
		data := (value).(*models.LivePlayerDB)
		data.WeekRank = GetLiveTopMgr().GetTop_Week(liveId)
		data.MonthRank = GetLiveTopMgr().GetTop_Month(liveId)
		data.OnSave(true)
		return true
	})
}

// ResetDataWeekly ! 每周三5：00重置数据
func (live *LiveMgr) ResetDataWeekly() {
	//! 清楚备份数据
	live.MapLiveData.Range(func(key, value interface{}) bool {
		data := value.(*models.LivePlayerDB)
		data.WeekScore = 0
		data.WeekRank = 9999

		return true
	})

	live.Save()

	//! 清除世界排行榜
	GetLiveTopMgr().ClearWeekData()
	GetRoomMgr().ReStoreLiveRoom(false)
}

func (live *LiveMgr) ResetDataMonthly() {
	//! 清楚备份数据
	live.MapLiveData.Range(func(key, value interface{}) bool {
		data := value.(*models.LivePlayerDB)
		GetSeasonMgr().SetPlayer(int(data.PlayerId), data.MonthScore)
		data.WinStreak = 0
		data.WinStreakRank = 9999
		data.MonthScore = 0
		data.MonthRank = 9999

		return true
	})

	live.Save()

	GetSeasonMgr().AddSeason()

	//! 清除世界排行榜
	GetLiveTopMgr().ClearMonthData()

	GetRoomMgr().ReStoreLiveRoom(true)
}

func (live *LiveMgr) ResetDataCall() {
	//! 清楚备份数据
	live.MapLiveData.Range(func(key, value interface{}) bool {
		data := value.(*models.LivePlayerDB)
		data.TotalCall = 0

		return true
	})

	//! 清除世界排行榜
	GetLiveTopMgr().ClearCallData()
}

// NewLiveId ! 返回后续的直播Id
func (live *LiveMgr) NewLiveId() int64 {
	live.MaxLiveId += 1 //! 自增返回
	return live.MaxLiveId
}

func (live *LiveMgr) AddLiveRoom(openId, roomId string) {
	core.LogInfo("设置抖音房间和主播ID：", openId, roomId)
	live.MapRoomId2OpenId.LoadOrStore(roomId, &RoomData{
		OpenId: openId,
		RoomId: roomId,
	})
}

func (live *LiveMgr) GetLiveRoomIdByOpenId(openId string) string {
	roomId := ""
	live.MapRoomId2OpenId.Range(func(key, value interface{}) bool {
		room := value.(*RoomData)
		if room.OpenId == openId {
			roomId = room.RoomId
			return false
		}
		return true
	})
	return roomId
}

func (live *LiveMgr) GetOpenIdByLiveRoomId(roomid string) string {
	room, ok := live.MapRoomId2OpenId.Load(roomid)
	if ok {
		return room.(*RoomData).OpenId
	}

	return ""
}

// GetLiveDataByOpenId ! 由OpenId 获得角色
func (live *LiveMgr) GetLiveDataByOpenId(openId string) *models.LivePlayerDB {
	if value, ok := live.MapLiveOpenId.Load(openId); ok {
		if utils.IsNil(value) {
			return nil
		}

		return value.(*models.LivePlayerDB)
	}

	return nil
}

// GetLivePlayerByOpenId ! 获得角色数据，如果不存在，就创建一个角色
func (live *LiveMgr) GetLivePlayerByOpenId(openId string) *mods.LivePlayer {
	liveData := live.GetLiveDataByOpenId(openId)
	if liveData != nil {
		return live.GetLivePlayer(liveData.PlayerId)
	}

	return nil
}

// GetLivePlayer ! 获得角色数据，如果不存在，就创建一个角色
func (live *LiveMgr) GetLivePlayer(liveId int64) *mods.LivePlayer {
	if lp, ok := live.MapLivePlayer.Load(liveId); ok {
		if utils.IsNotNil(lp) {
			return lp.(*mods.LivePlayer)
		}
	}

	return nil
}

// NewLiveData ! 初始化直播数据
func (live *LiveMgr) NewLiveData(platformId, uname string) *models.LivePlayerDB {
	liveId := live.NewLiveId() //! 创建一个新的直播角色

	core.LogDebug("New Live Player : ", platformId, uname)
	dbData := &models.LivePlayerDB{
		PlayerId:   liveId,
		OpenId:     platformId,
		PlayerName: uname,
	}

	dbData.MapFamily = new(sync.Map)
	dbData.MapGiftRecord = new(sync.Map)
	dbData.Encode()

	//! 插入数据库
	db.InsertTable(storage.TABLE_Player, dbData, 0, true)
	dbData.Init(storage.TABLE_Player, dbData, true)

	GetSeasonMgr().CheckPlayer(int(dbData.PlayerId), dbData.MonthScore)
	live.MapLiveOpenId.Store(platformId, dbData)
	live.MapLiveData.Store(liveId, dbData)
	return dbData
}

// NewLivePlayer ! 创建在线角色数据
func (live *LiveMgr) NewLivePlayer(platformId, uname string) *mods.LivePlayer {
	dbData := live.GetLiveDataByOpenId(platformId)
	if dbData == nil {
		//! 数据不存在，则创建一个新的
		dbData = live.NewLiveData(platformId, uname)
	}

	livePlayer := &mods.LivePlayer{
		LiveId:    dbData.PlayerId,
		Data:      dbData,
		ScoreData: new(sync.Map),
		InfoData:  new(sync.Map),
	}

	live.MapLivePlayer.Store(dbData.PlayerId, livePlayer)

	return livePlayer
}

func (live *LiveMgr) LoadLivePlayerDB(liveid int64) *models.LivePlayerDB {
	dbData := live.GetLiveData(liveid)
	if dbData == nil {
		//! 数据不存在，则创建一个新的
		return nil
	}

	return dbData
}

func (live *LiveMgr) GetLivePlayerByName(uname string) *mods.LivePlayer {
	var livePlayer *mods.LivePlayer = nil
	live.MapLivePlayer.Range(func(key, value interface{}) bool {
		data := value.(*mods.LivePlayer)
		if data.Data.PlayerName == uname {
			livePlayer = data
			return false
		}
		return true
	})

	return livePlayer
}

func (live *LiveMgr) AddLivePlayer(livePlayer *mods.LivePlayer) bool {

	return false
}

func (live *LiveMgr) AddComment(roomDYId string, comment []byte) bool {

	room := GetRoomMgr().GetRoomByPlatformId(roomDYId)
	if room == nil {
		core.LogInfo("AddComment找不到对应房间:", roomDYId)
		return false
	}
	// core.LogInfo("AddComment房间:", roomDYId, "comment:", string(comment))
	room.AddComment(comment)
	return true
}

func (live *LiveMgr) AddGift(roomDYId string, comment []*payload.PayloadData, msgId string) bool {
	room := GetRoomMgr().GetRoomByPlatformId(roomDYId)
	if room == nil {
		core.LogInfo("AddGift找不到对应房间:", roomDYId)
		return false
	}
	if room.GetPlayState() <= mods.PLAY_STATE_END {
		core.LogInfo("AddGift房间状态不对:", roomDYId)
		return false
	}

	room.AddGift(comment, msgId)
	return true
}

func (live *LiveMgr) AddFans(roomDYId string, comment []*payload.PayloadData, msgId string) bool {
	room := GetRoomMgr().GetRoomByPlatformId(roomDYId)
	if room == nil {
		core.LogInfo("AddGift找不到对应房间:", roomDYId)
		return false
	}
	if room.GetPlayState() <= mods.PLAY_STATE_END {
		core.LogInfo("AddGift房间状态不对:", roomDYId)
		return false
	}

	room.AddFans(comment, msgId)
	return true
}

func (live *LiveMgr) AddLike(roomDYId string, comment []byte) bool {
	room := GetRoomMgr().GetRoomByPlatformId(roomDYId)
	if room == nil {
		core.LogInfo("AddLike找不到对应房间:", roomDYId)
		return false
	}
	if room.GetPlayState() <= mods.PLAY_STATE_END {
		core.LogInfo("AddLike房间状态不对:", roomDYId)
		return false
	}
	room.AddLike(comment)
	return true
}
