package manager

import (
	"fmt"
	"sync"
	"zone/game/models"
	"zone/game/mods"
	"zone/lib/core"
	"zone/lib/db"
	"zone/lib/storage"
)

// 主播ID为自定义ID，从1开始一直加
type AnchorMgr struct {
	LastIndex int
	// MapAnchor     *sync.Map //! 在线主播，以主播Id为主键，结果是 AnchorDB
	MapAnchorData *sync.Map //! 以OpenId为主键，数据为主播信息 AnchorDB
}

var s_anchormgr *AnchorMgr = nil

func GetAnchorMgr() *AnchorMgr {
	if s_anchormgr == nil {
		s_anchormgr = new(AnchorMgr)

		// s_anchormgr.MapAnchor = new(sync.Map)
		s_anchormgr.MapAnchorData = new(sync.Map)
		s_anchormgr.LastIndex = 0

		// 注册到mods包的管理器注册表
		mods.RegisterAnchorManager(s_anchormgr)
	}

	return s_anchormgr
}

func (anchor *AnchorMgr) GetData() {
	var liveData models.AnchorDB
	sql := fmt.Sprintf("select * from `%s`", storage.TABLE_Anchor)
	res := db.GetDBMgr().DBUser.GetAllData(sql, &liveData)
	for i := 0; i < len(res); i++ {
		data := res[i].(*models.AnchorDB)
		data.Init(storage.TABLE_Anchor, data, false)
		data.Decode()
		anchor.MapAnchorData.Store(data.OpenId, data)
	}
	anchor.LastIndex = len(res)
}

//	OpenId     string //! 主播ID
//	Icon       string //! 头像
//	Name       string //! 点赞
//	PlayerList string //! 玩家列表
//	GiftValue  int64  //! 礼物总价值
//	Time       int64  //! 最后一次开播

func (anchor *AnchorMgr) AnchorOnLine(AnchorOpenId, AvatarUrl, NickName string) *models.AnchorDB {
	dbData := anchor.GetAnchor(AnchorOpenId)
	if dbData == nil {
		anchor.LastIndex++
		dbData = &models.AnchorDB{
			Id:        anchor.LastIndex,
			OpenId:    AnchorOpenId,
			Icon:      AvatarUrl,
			Name:      NickName,
			GiftValue: 0,
			ScorePool: 0,
			LiveTimes: 0,
			MapPlayer: new(sync.Map),
		}

		dbData.Encode()

		//! 插入数据库
		db.InsertTable(storage.TABLE_Anchor, dbData, 0, false)
		dbData.Init(storage.TABLE_Anchor, dbData, false)
	}
	dbData.Time = core.TimeServer().Unix()
	dbData.LiveTimes += 1
	anchor.MapAnchorData.Store(dbData.OpenId, dbData)
	return dbData
}

func (anchor *AnchorMgr) GetAnchor(openId string) *models.AnchorDB {
	if data, ok := anchor.MapAnchorData.Load(openId); ok {
		return data.(*models.AnchorDB)
	}
	return nil
}

// func ( anchor *AnchorMgr) SetPlayerValue(openId string, liveId int, value int64) {
// 	if data, ok :=  anchor.MapAnchorData.Load(openId); ok {
// 		return data.(*models.AnchorDB)
// 	}
// 	return nil
// }

func (anchor *AnchorMgr) Save() {
	anchor.MapAnchorData.Range(func(key, value interface{}) bool {
		data := (value).(*models.AnchorDB)
		data.OnSave(true)
		return true
	})
}
