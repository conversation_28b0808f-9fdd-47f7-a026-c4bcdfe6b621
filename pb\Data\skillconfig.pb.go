// Generated from Excel file: SkillConfig.xlsx

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.5.1
// source: skillconfig.proto

package Data

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Skillconfig 配置数据
type Skillconfig struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 编号
	Id int32 `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty"`
	// 技能cd（最小单位倍数）
	Cd            int32 `protobuf:"varint,2,opt,name=cd,proto3" json:"cd,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Skillconfig) Reset() {
	*x = Skillconfig{}
	mi := &file_skillconfig_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Skillconfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Skillconfig) ProtoMessage() {}

func (x *Skillconfig) ProtoReflect() protoreflect.Message {
	mi := &file_skillconfig_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Skillconfig.ProtoReflect.Descriptor instead.
func (*Skillconfig) Descriptor() ([]byte, []int) {
	return file_skillconfig_proto_rawDescGZIP(), []int{0}
}

func (x *Skillconfig) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Skillconfig) GetCd() int32 {
	if x != nil {
		return x.Cd
	}
	return 0
}

// SkillconfigList 配置数据列表
type SkillconfigList struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Items         []*Skillconfig         `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SkillconfigList) Reset() {
	*x = SkillconfigList{}
	mi := &file_skillconfig_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SkillconfigList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SkillconfigList) ProtoMessage() {}

func (x *SkillconfigList) ProtoReflect() protoreflect.Message {
	mi := &file_skillconfig_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SkillconfigList.ProtoReflect.Descriptor instead.
func (*SkillconfigList) Descriptor() ([]byte, []int) {
	return file_skillconfig_proto_rawDescGZIP(), []int{1}
}

func (x *SkillconfigList) GetItems() []*Skillconfig {
	if x != nil {
		return x.Items
	}
	return nil
}

var File_skillconfig_proto protoreflect.FileDescriptor

const file_skillconfig_proto_rawDesc = "" +
	"\n" +
	"\x11skillconfig.proto\x12\x04Data\"-\n" +
	"\vSkillconfig\x12\x0e\n" +
	"\x02Id\x18\x01 \x01(\x05R\x02Id\x12\x0e\n" +
	"\x02cd\x18\x02 \x01(\x05R\x02cd\":\n" +
	"\x0fSkillconfigList\x12'\n" +
	"\x05items\x18\x01 \x03(\v2\x11.Data.SkillconfigR\x05itemsB\x0eZ\fzone/pb/Datab\x06proto3"

var (
	file_skillconfig_proto_rawDescOnce sync.Once
	file_skillconfig_proto_rawDescData []byte
)

func file_skillconfig_proto_rawDescGZIP() []byte {
	file_skillconfig_proto_rawDescOnce.Do(func() {
		file_skillconfig_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_skillconfig_proto_rawDesc), len(file_skillconfig_proto_rawDesc)))
	})
	return file_skillconfig_proto_rawDescData
}

var file_skillconfig_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_skillconfig_proto_goTypes = []any{
	(*Skillconfig)(nil),     // 0: Data.Skillconfig
	(*SkillconfigList)(nil), // 1: Data.SkillconfigList
}
var file_skillconfig_proto_depIdxs = []int32{
	0, // 0: Data.SkillconfigList.items:type_name -> Data.Skillconfig
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_skillconfig_proto_init() }
func file_skillconfig_proto_init() {
	if File_skillconfig_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_skillconfig_proto_rawDesc), len(file_skillconfig_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_skillconfig_proto_goTypes,
		DependencyIndexes: file_skillconfig_proto_depIdxs,
		MessageInfos:      file_skillconfig_proto_msgTypes,
	}.Build()
	File_skillconfig_proto = out.File
	file_skillconfig_proto_goTypes = nil
	file_skillconfig_proto_depIdxs = nil
}
