package announcement

import (
	"sync"
	"zone/lib/core"
)

// Manager 公告管理器
type Manager struct {
	service *AnnouncementService
	mutex   sync.RWMutex
}

var (
	managerInstance *Manager
	managerOnce     sync.Once
)

// GetManager 获取公告管理器单例
func GetManager() *Manager {
	managerOnce.Do(func() {
		managerInstance = &Manager{
			service: NewAnnouncementService(),
		}
	})
	return managerInstance
}

// Start 启动公告管理器
func (m *Manager) Start() {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	core.LogInfo("启动公告管理器")
	m.service.Start()
}

// Stop 停止公告管理器
func (m *Manager) Stop() {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	core.LogInfo("停止公告管理器")
	if m.service != nil {
		m.service.Stop()
	}
}

// GetService 获取公告服务实例
func (m *Manager) GetService() *AnnouncementService {
	m.mutex.RLock()
	defer m.mutex.RUnlock()
	return m.service
}

// IsRunning 检查公告服务是否正在运行
func (m *Manager) IsRunning() bool {
	m.mutex.RLock()
	defer m.mutex.RUnlock()
	return m.service != nil && m.service.isRunning
}

// TriggerAnnouncementCheck 手动触发公告检查
// 用于房间状态变更时的即时检查
func (m *Manager) TriggerAnnouncementCheck() {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	if m.service != nil && m.service.isRunning {
		core.LogDebug("手动触发公告检查")
		go m.service.processAnnouncements()
	}
}

// OnRoomStateChanged 房间状态变更回调
// 当房间状态变更为 >= PLAY_STATE_READY 时调用
func (m *Manager) OnRoomStateChanged(roomID int, newState int) {
	// 只有当房间状态变更为有效状态时才触发检查
	if newState >= 1 { // PLAY_STATE_READY = 1
		core.LogDebug("房间状态变更触发公告检查", "房间ID:", roomID, "新状态:", newState)
		m.TriggerAnnouncementCheck()
	}
}

// OnPlayerJoinRoom 玩家加入房间回调
// 当玩家加入房间时检查是否有需要推送的公告
func (m *Manager) OnPlayerJoinRoom(sessionID, roomID int) {
	core.LogDebug("玩家加入房间触发公告检查",
		"会话ID:", sessionID,
		"房间ID:", roomID)
	m.TriggerAnnouncementCheck()
}

// GetMemoryDeduplicationManager 获取内存去重管理器
func (m *Manager) GetMemoryDeduplicationManager() *MemoryDeduplicationManager {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	if m.service != nil {
		return m.service.memoryDeduplicationMgr
	}
	return nil
}

// GetMySQLCleanupManager 获取MySQL清理管理器
func (m *Manager) GetMySQLCleanupManager() *MySQLCleanupManager {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	if m.service != nil {
		return m.service.mysqlCleanupMgr
	}
	return nil
}

// GetDeduplicationManager 获取去重管理器（保留兼容性，但推荐使用内存版本）
// func (m *Manager) GetDeduplicationManager() *DeduplicationManager {
// 	m.mutex.RLock()
// 	defer m.mutex.RUnlock()

// 	if m.service != nil {
// 		return m.service.deduplicationMgr
// 	}
// 	return nil
// }

// OnPlayerLeaveRoom 玩家离开房间回调
// 可选择性清理该玩家的去重记录
func (m *Manager) OnPlayerLeaveRoom(sessionID, roomID, playerID int) {
	core.LogDebug("玩家离开房间",
		"会话ID:", sessionID,
		"房间ID:", roomID,
		"玩家ID:", playerID)

	// 可以选择在这里清理该会话的去重记录
	// deduplicationMgr := m.GetDeduplicationManager()
	// if deduplicationMgr != nil {
	//     deduplicationMgr.CleanupBySessionID(sessionID)
	// }
}

// OnRoomDestroyed 房间销毁回调
// 清理该房间相关的去重记录
func (m *Manager) OnRoomDestroyed(roomID int) {
	core.LogDebug("房间销毁，清理相关去重记录", "房间ID:", roomID)

	// 内存去重管理器不需要按房间清理，因为使用的是sessionID:announcementID格式
	// 如果需要，可以在这里添加其他清理逻辑
	core.LogDebug("内存去重管理器无需按房间清理")
}

// ForceCleanupExpiredAnnouncements 强制清理过期公告（使用MySQL清理管理器）
func (m *Manager) ForceCleanupExpiredAnnouncements() *MySQLCleanupStats {
	mysqlCleanupMgr := m.GetMySQLCleanupManager()
	if mysqlCleanupMgr != nil {
		return mysqlCleanupMgr.CleanupExpiredAnnouncements()
	}
	return nil
}

// ForceCleanupAll 强制清理所有过期公告（使用MySQL清理管理器）
func (m *Manager) ForceCleanupAll() *MySQLCleanupStats {
	mysqlCleanupMgr := m.GetMySQLCleanupManager()
	if mysqlCleanupMgr != nil {
		return mysqlCleanupMgr.ForceCleanup()
	}
	return nil
}

// GetAnnouncementStats 获取公告统计信息
func (m *Manager) GetAnnouncementStats() map[string]interface{} {
	stats := make(map[string]interface{})

	// 获取内存去重统计
	memoryDeduplicationMgr := m.GetMemoryDeduplicationManager()
	if memoryDeduplicationMgr != nil {
		stats["memory_deduplication"] = memoryDeduplicationMgr.GetStats()
	}

	// 获取MySQL清理统计
	mysqlCleanupMgr := m.GetMySQLCleanupManager()
	if mysqlCleanupMgr != nil {
		stats["mysql_cleanup"] = mysqlCleanupMgr.GetStats()
	}

	// 获取服务状态
	stats["service_running"] = m.IsRunning()

	// 获取去重模式
	if m.service != nil {
		stats["deduplication_mode"] = "memory"
	}

	return stats
}
