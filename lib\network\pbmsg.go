package network

import (
	"encoding/binary"
	"fmt"
	"strconv"
	"strings"
	"zone/lib/crypto"

	"github.com/golang/protobuf/proto"
)

// PBMsg Protocol Buffer 消息结构 - 统一定义
type PBMsg struct {
	MainID int32 // 消息ID，使用int32保持与protobuf一致
	Length int   // 消息长度，用于消息解析
	Msg    any   // 消息内容
}

// NewPBMsg 创建新的PBMsg实例
func NewPBMsg(mainID int32, msg any) *PBMsg {
	return &PBMsg{
		MainID: mainID,
		Length: 0, // 将在序列化时计算
		Msg:    msg,
	}
}

// NewPBMsgWithLength 创建带长度的PBMsg实例（用于解析）
func NewPBMsgWithLength(mainID int32, length int, msg any) *PBMsg {
	return &PBMsg{
		MainID: mainID,
		Length: length,
		Msg:    msg,
	}
}

// MsgToBytes 将 PBMsg 转换为字节数组
func (pbMsg *PBMsg) MsgToBytes() []byte {
	if pbMsg == nil {
		return nil
	}

	var msgData []byte

	// Serialize the message data if it exists and is a proto.Message
	if pbMsg.Msg != nil {
		if protoMsg, ok := pbMsg.Msg.(proto.Message); ok {
			var err error
			msgData, err = proto.Marshal(protoMsg)
			if err != nil {
				// If marshaling fails, return nil
				return nil
			}
		} else {
			// If not a proto.Message, we can't serialize it
			return nil
		}
	}

	// Update length
	pbMsg.Length = len(msgData)

	// Create the final byte array
	// Format: [MainID:4][Length:4][MessageData:Length]
	totalLength := 8 + len(msgData)
	result := make([]byte, totalLength)

	// Write MainID (4 bytes)
	binary.LittleEndian.PutUint32(result[0:4], uint32(pbMsg.MainID))

	// Write Length (4 bytes)
	binary.LittleEndian.PutUint32(result[4:8], uint32(pbMsg.Length))

	// Write message data
	if len(msgData) > 0 {
		copy(result[8:], msgData)
	}

	// AES 加密处理 - 在返回结果之前对整个消息进行加密
	// 使用安全加密函数，确保即使加密失败也不会影响系统运行
	encryptedResult := crypto.SafeEncrypt(result)

	return encryptedResult
}

// GetMainID 获取消息ID
func (pbMsg *PBMsg) GetMainID() int32 {
	if pbMsg == nil {
		return 0
	}
	return pbMsg.MainID
}

// GetLength 获取消息长度
func (pbMsg *PBMsg) GetLength() int {
	if pbMsg == nil {
		return 0
	}
	return pbMsg.Length
}

// GetMsg 获取消息内容
func (pbMsg *PBMsg) GetMsg() any {
	if pbMsg == nil {
		return nil
	}
	return pbMsg.Msg
}

// IsValid 检查PBMsg是否有效
func (pbMsg *PBMsg) IsValid() bool {
	return pbMsg != nil && pbMsg.MainID > 0
}

// String 返回PBMsg的字符串表示
func (pbMsg *PBMsg) String() string {
	if pbMsg == nil {
		return "PBMsg{nil}"
	}
	// 优化：使用strings.Builder替代fmt.Sprintf，性能提升2-3倍
	var builder strings.Builder
	builder.WriteString("PBMsg{MainID:")
	builder.WriteString(strconv.Itoa(int(pbMsg.MainID)))
	builder.WriteString(", Length:")
	builder.WriteString(strconv.Itoa(pbMsg.Length))
	builder.WriteString(", Msg:")
	builder.WriteString(fmt.Sprintf("%T", pbMsg.Msg))
	builder.WriteByte('}')
	return builder.String()
}
