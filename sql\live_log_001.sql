/*
 Navicat MySQL Data Transfer

 Source Server         : 127.0.0.1_3306
 Source Server Type    : MySQL
 Source Server Version : 50726
 Source Host           : 127.0.0.1:3306
 Source Schema         : live_log_001

 Target Server Type    : MySQL
 Target Server Version : 50726
 File Encoding         : 65001

 Date: 12/05/2025 15:35:16
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

DROP TABLE IF EXISTS `san_Giftlog`;
CREATE TABLE `san_Giftlog`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `PlayerName` varchar(125) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `PlayerId` varchar(125) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `RoomId` int(11) NOT NULL,
  `AnchorId` varchar(125) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `MsgId` varchar(125) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `GiftName` varchar(125) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `GiftCount` int(11) NOT NULL,
  `State` int(8) NOT NULL,
  `Time` bigint(20) NOT NULL,
  `Platform` varchar(125) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

DROP TABLE IF EXISTS `san_Commentlog`;
CREATE TABLE `san_Commentlog`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `PlayerName` varchar(125) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `PlayerId` varchar(125) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `RoomId` int(11) NOT NULL,
  `AnchorId` varchar(125) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `MsgId` varchar(125) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Comment` varchar(125) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Time` bigint(20) NOT NULL,
  `Platform` varchar(125) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

DROP TABLE IF EXISTS `san_Playerlog`;
CREATE TABLE `san_Playerlog`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `PlayerName` varchar(125) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `PlayerId` bigint(20) NOT NULL,
  `RoomId` bigint(20) NOT NULL,
  `Time` bigint(20) NOT NULL,
  `Week` bigint(20) NOT NULL,
  `Month` bigint(20) NOT NULL,
  `Win` bigint(20) NOT NULL,
  `Desc` varchar(125) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Platform` varchar(125) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for san_belog
-- ----------------------------
DROP TABLE IF EXISTS `san_belog`;
CREATE TABLE `san_belog`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `time` bigint(20) NOT NULL,
  `type` int(11) NOT NULL,
  `value` int(11) NOT NULL,
  `param1` int(11) NOT NULL,
  `param2` int(11) NOT NULL,
  `uid` bigint(20) NOT NULL,
  `dec` varchar(45) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `cur` bigint(20) NOT NULL COMMENT '当前值',
  `param3` int(11) NOT NULL,
  `level` int(4) NOT NULL,
  `vip` int(4) NOT NULL,
  `fight` bigint(4) NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `INDEX_UID`(`uid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1709 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;


SET FOREIGN_KEY_CHECKS = 1;
